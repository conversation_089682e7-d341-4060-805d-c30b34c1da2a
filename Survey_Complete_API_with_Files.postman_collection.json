{"info": {"_postman_id": "survey-complete-api-with-files", "name": "Survey Complete API with Files", "description": "Comprehensive Postman collection for Survey Creation and Sample Response APIs with file upload support", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "survey-api-collection"}, "item": [{"name": "Survey Creation API", "item": [{"name": "Create Survey with Files (Multipart)", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Token {{auth_token}}", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "title", "value": "Document Upload Survey with Files", "type": "text"}, {"key": "description", "value": "Survey demonstrating file upload capabilities for questions and options", "type": "text"}, {"key": "status", "value": "Draft", "type": "text"}, {"key": "sections", "value": "[{\"title\":\"Document Upload Section\",\"order\":1,\"questions\":[{\"question_text\":\"Please upload your resume\",\"type\":\"Text\",\"is_mandatory\":true,\"order\":1},{\"question_text\":\"Upload a profile picture\",\"type\":\"ImageCapture\",\"is_mandatory\":false,\"order\":2},{\"question_text\":\"Which product do you prefer?\",\"type\":\"MCQ\",\"is_mandatory\":true,\"order\":3,\"options\":[{\"option_text\":\"Product A\",\"order\":1},{\"option_text\":\"Product B\",\"order\":2},{\"option_text\":\"Product C\",\"order\":3}]}]},{\"title\":\"Preferences Section\",\"order\":2,\"questions\":[{\"question_text\":\"What is your budget range?\",\"type\":\"MCQ\",\"is_mandatory\":true,\"order\":1,\"options\":[{\"option_text\":\"$0-100\",\"order\":1},{\"option_text\":\"$100-500\",\"order\":2},{\"option_text\":\"$500-1000\",\"order\":3},{\"option_text\":\"$1000+\",\"order\":4}]},{\"question_text\":\"Do you like spicy food?\",\"type\":\"Boolean\",\"is_mandatory\":true,\"order\":2},{\"question_text\":\"Any additional comments?\",\"type\":\"Text\",\"is_mandatory\":false,\"order\":3}]}]", "type": "text"}, {"key": "sections.0.questions.0.file", "type": "file", "src": []}, {"key": "sections.0.questions.1.file", "type": "file", "src": []}, {"key": "sections.0.questions.2.options.0.image", "type": "file", "src": []}, {"key": "sections.0.questions.2.options.1.image", "type": "file", "src": []}, {"key": "sections.0.questions.2.options.2.image", "type": "file", "src": []}]}, "url": {"raw": "{{base_url}}/api/surveys/create-complete/", "host": ["{{base_url}}"], "path": ["api", "surveys", "create-complete", ""]}, "description": "Create a survey with file uploads for questions and option images using multipart form data"}, "response": []}, {"name": "Create Survey with Flow Control (JSON)", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Token {{auth_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"title\": \"Flow Control Survey with next_section_default\",\n    \"description\": \"Survey demonstrating section flow control using next_section_default\",\n    \"status\": \"Draft\",\n    \"config_json\": {\n        \"theme\": \"flow_control\",\n        \"allow_anonymous\": false,\n        \"estimated_time\": \"5 minutes\"\n    },\n    \"sections\": [\n        {\n            \"title\": \"Welcome Section\",\n            \"order\": 1,\n            \"next_section_default\": 1,\n            \"questions\": [\n                {\n                    \"question_text\": \"Welcome! Are you ready to start the survey?\",\n                    \"type\": \"Boolean\",\n                    \"is_mandatory\": true,\n                    \"order\": 1\n                }\n            ]\n        },\n        {\n            \"title\": \"Demographics\",\n            \"order\": 2,\n            \"next_section_default\": 2,\n            \"questions\": [\n                {\n                    \"question_text\": \"What is your age group?\",\n                    \"type\": \"MCQ\",\n                    \"is_mandatory\": true,\n                    \"order\": 1,\n                    \"options\": [\n                        {\"option_text\": \"18-25 years\", \"order\": 1},\n                        {\"option_text\": \"26-35 years\", \"order\": 2},\n                        {\"option_text\": \"36+ years\", \"order\": 3}\n                    ]\n                }\n            ]\n        },\n        {\n            \"title\": \"Preferences\",\n            \"order\": 3,\n            \"next_section_default\": 3,\n            \"questions\": [\n                {\n                    \"question_text\": \"What is your favorite color?\",\n                    \"type\": \"MCQ\",\n                    \"is_mandatory\": true,\n                    \"order\": 1,\n                    \"options\": [\n                        {\"option_text\": \"Red\", \"order\": 1},\n                        {\"option_text\": \"Blue\", \"order\": 2},\n                        {\"option_text\": \"Green\", \"order\": 3}\n                    ]\n                }\n            ]\n        },\n        {\n            \"title\": \"Final Section\",\n            \"order\": 4,\n            \"next_section_default\": null,\n            \"questions\": [\n                {\n                    \"question_text\": \"Thank you for completing the survey!\",\n                    \"type\": \"Text\",\n                    \"is_mandatory\": false,\n                    \"order\": 1\n                }\n            ]\n        }\n    ]\n}"}, "url": {"raw": "{{base_url}}/api/surveys/create-complete/", "host": ["{{base_url}}"], "path": ["api", "surveys", "create-complete", ""]}, "description": "Create a survey with flow control using next_section_default (JSON format)"}, "response": []}, {"name": "Create Simple Survey (JSON)", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Token {{auth_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"title\": \"Simple Survey without Files\",\n    \"description\": \"Basic survey creation without file uploads\",\n    \"status\": \"Draft\",\n    \"config_json\": {\n        \"theme\": \"simple\",\n        \"allow_anonymous\": false,\n        \"estimated_time\": \"3 minutes\"\n    },\n    \"sections\": [\n        {\n            \"title\": \"Basic Information\",\n            \"order\": 1,\n            \"questions\": [\n                {\n                    \"question_text\": \"What is your name?\",\n                    \"type\": \"Text\",\n                    \"is_mandatory\": true,\n                    \"order\": 1\n                },\n                {\n                    \"question_text\": \"What is your age?\",\n                    \"type\": \"MCQ\",\n                    \"is_mandatory\": true,\n                    \"order\": 2,\n                    \"options\": [\n                        {\"option_text\": \"18-25\", \"order\": 1},\n                        {\"option_text\": \"26-35\", \"order\": 2},\n                        {\"option_text\": \"36-50\", \"order\": 3},\n                        {\"option_text\": \"50+\", \"order\": 4}\n                    ]\n                },\n                {\n                    \"question_text\": \"Do you like surveys?\",\n                    \"type\": \"Boolean\",\n                    \"is_mandatory\": true,\n                    \"order\": 3\n                }\n            ]\n        }\n    ]\n}"}, "url": {"raw": "{{base_url}}/api/surveys/create-complete/", "host": ["{{base_url}}"], "path": ["api", "surveys", "create-complete", ""]}, "description": "Create a simple survey without file uploads (JSON format)"}, "response": []}, {"name": "Get All Surveys", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Token {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/surveys/", "host": ["{{base_url}}"], "path": ["api", "surveys", ""]}, "description": "Get all surveys with samples count"}, "response": []}, {"name": "Get Survey by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Token {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/surveys/{{survey_id}}/", "host": ["{{base_url}}"], "path": ["api", "surveys", "{{survey_id}}", ""]}, "description": "Get a specific survey by ID with all sections, questions, and options"}, "response": []}, {"name": "Update Survey Complete (PUT)", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Token {{auth_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"title\": \"Updated Survey Title\",\n    \"description\": \"Updated survey description\",\n    \"status\": \"Draft\",\n    \"config_json\": {\n        \"theme\": \"updated_theme\",\n        \"allow_anonymous\": true,\n        \"estimated_time\": \"5 minutes\"\n    },\n    \"sections\": [\n        {\n            \"id\": 1,\n            \"title\": \"Updated Section Title\",\n            \"order\": 1,\n            \"questions\": [\n                {\n                    \"id\": 1,\n                    \"question_text\": \"Updated question text?\",\n                    \"type\": \"Text\",\n                    \"is_mandatory\": true,\n                    \"order\": 1\n                },\n                {\n                    \"question_text\": \"New question added\",\n                    \"type\": \"MCQ\",\n                    \"is_mandatory\": false,\n                    \"order\": 2,\n                    \"options\": [\n                        {\"option_text\": \"Option A\", \"order\": 1},\n                        {\"option_text\": \"Option B\", \"order\": 2}\n                    ]\n                }\n            ]\n        },\n        {\n            \"title\": \"New Section Added\",\n            \"order\": 2,\n            \"questions\": [\n                {\n                    \"question_text\": \"Question in new section?\",\n                    \"type\": \"Boolean\",\n                    \"is_mandatory\": true,\n                    \"order\": 1\n                }\n            ]\n        }\n    ]\n}"}, "url": {"raw": "{{base_url}}/api/surveys/{{survey_id}}/update-complete/", "host": ["{{base_url}}"], "path": ["api", "surveys", "{{survey_id}}", "update-complete", ""]}, "description": "Update a complete survey using the same structure as GET response.\n\n**How it works:**\n1. First GET the survey using 'Get Survey by ID'\n2. Modify the returned JSON object as needed\n3. Send the modified object back using this PUT request\n\n**Features:**\n- Update existing items (include their 'id' field)\n- Add new items (omit 'id' field)\n- Delete items (remove them from the request)\n- Supports file uploads via multipart/form-data\n\n**Example modifications:**\n- Change survey title/description\n- Update question text\n- Add new questions/options\n- Remove sections/questions/options\n- Change question types or options"}, "response": []}, {"name": "Update Survey Complete (Multipart)", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Token {{auth_token}}", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "title", "value": "Updated Survey with Files", "type": "text"}, {"key": "description", "value": "Updated survey with file uploads", "type": "text"}, {"key": "status", "value": "Draft", "type": "text"}, {"key": "sections", "value": "[{\"id\":1,\"title\":\"Updated Section\",\"order\":1,\"questions\":[{\"id\":1,\"question_text\":\"Updated question with file\",\"type\":\"Text\",\"is_mandatory\":true,\"order\":1},{\"question_text\":\"New question with file\",\"type\":\"ImageCapture\",\"is_mandatory\":false,\"order\":2}]}]", "type": "text"}, {"key": "sections.0.questions.0.file", "type": "file", "src": []}, {"key": "sections.0.questions.1.file", "type": "file", "src": []}]}, "url": {"raw": "{{base_url}}/api/surveys/{{survey_id}}/update-complete/", "host": ["{{base_url}}"], "path": ["api", "surveys", "{{survey_id}}", "update-complete", ""]}, "description": "Update survey with file uploads using multipart/form-data format"}, "response": []}], "description": "API endpoints for creating and managing surveys with file upload support"}, {"name": "Sample Response API", "item": [{"name": "Submit Complete Response with Files (Multipart)", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Token {{auth_token}}", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "survey", "value": "{{survey_id}}", "type": "text"}, {"key": "latitude", "value": "40.7589", "type": "text"}, {"key": "longitude", "value": "-73.9851", "type": "text"}, {"key": "address", "value": "Times Square, New York, NY", "type": "text"}, {"key": "device_info", "value": "{\"device_model\":\"iPhone 14\",\"os_version\":\"iOS 17.0\",\"app_version\":\"2.1.0\",\"battery_level\":85,\"network_type\":\"WiFi\"}", "type": "text"}, {"key": "is_offline", "value": "false", "type": "text"}, {"key": "responses", "value": "[{\"question_id\":1,\"response\":\"Uploading my resume\",\"selected_option_ids\":[],\"audio_response\":\"\",\"image_response\":\"\"},{\"question_id\":2,\"response\":\"Uploading profile picture\",\"selected_option_ids\":[],\"audio_response\":\"\",\"image_response\":\"\"},{\"question_id\":3,\"response\":\"\",\"selected_option_ids\":[1,2],\"audio_response\":\"\",\"image_response\":\"\"},{\"question_id\":4,\"response\":\"\",\"selected_option_ids\":[2],\"audio_response\":\"\",\"image_response\":\"\"},{\"question_id\":5,\"response\":\"Yes\",\"selected_option_ids\":[],\"audio_response\":\"\",\"image_response\":\"\"},{\"question_id\":6,\"response\":\"Great survey!\",\"selected_option_ids\":[],\"audio_response\":\"\",\"image_response\":\"\"}]", "type": "text", "description": "IMPORTANT: This must be a valid JSON string (all double quotes). If you get 'This field is required.' error, check your JSON formatting."}, {"key": "responses.0.file", "type": "file", "src": []}, {"key": "responses.1.file", "type": "file", "src": []}, {"key": "responses.2.file", "type": "file", "src": []}]}, "url": {"raw": "{{base_url}}/api/samples/submit-complete/", "host": ["{{base_url}}"], "path": ["api", "samples", "submit-complete", ""]}, "description": "Submit a complete survey response with file uploads for question responses.\n\n**Note:** The 'responses' field must be a valid JSON string (all double quotes). If you get a 'This field is required.' error, check your JSON formatting in the 'responses' field.\n\nExample:\n[{\"question_id\":1,\"response\":\"Uploading my resume\",...}]\n\nAttach files as 'responses.0.file', 'responses.1.file', etc."}, "response": []}, {"name": "Submit Complete Response (JSON Only)", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Token {{auth_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"survey\": \"{{survey_id}}\",\n    \"latitude\": \"40.7128\",\n    \"longitude\": \"-74.0060\",\n    \"address\": \"Manhattan, New York, NY\",\n    \"device_info\": {\n        \"device_model\": \"Samsung Galaxy S23\",\n        \"os_version\": \"Android 13\",\n        \"app_version\": \"2.1.0\",\n        \"battery_level\": 92,\n        \"network_type\": \"5G\"\n    },\n    \"is_offline\": false,\n    \"responses\": [\n        {\n            \"question_id\": 1,\n            \"response\": \"I will upload later\",\n            \"selected_option_ids\": [],\n            \"audio_response\": \"\",\n            \"image_response\": \"\"\n        },\n        {\n            \"question_id\": 2,\n            \"response\": \"No image to upload\",\n            \"selected_option_ids\": [],\n            \"audio_response\": \"\",\n            \"image_response\": \"\"\n        },\n        {\n            \"question_id\": 3,\n            \"response\": \"\",\n            \"selected_option_ids\": [3],\n            \"audio_response\": \"\",\n            \"image_response\": \"\"\n        },\n        {\n            \"question_id\": 4,\n            \"response\": \"\",\n            \"selected_option_ids\": [1],\n            \"audio_response\": \"\",\n            \"image_response\": \"\"\n        },\n        {\n            \"question_id\": 5,\n            \"response\": \"No\",\n            \"selected_option_ids\": [],\n            \"audio_response\": \"\",\n            \"image_response\": \"\"\n        },\n        {\n            \"question_id\": 6,\n            \"response\": \"No comments\",\n            \"selected_option_ids\": [],\n            \"audio_response\": \"\",\n            \"image_response\": \"\"\n        }\n    ]\n}"}, "url": {"raw": "{{base_url}}/api/samples/submit-complete/", "host": ["{{base_url}}"], "path": ["api", "samples", "submit-complete", ""]}, "description": "Submit a complete survey response without file uploads (JSON format)"}, "response": []}, {"name": "Get All Samples", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Token {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/samples/", "host": ["{{base_url}}"], "path": ["api", "samples", ""]}, "description": "Get all survey samples with responses"}, "response": []}, {"name": "Get <PERSON><PERSON> by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Token {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/samples/{{sample_id}}/", "host": ["{{base_url}}"], "path": ["api", "samples", "{{sample_id}}", ""]}, "description": "Get a specific sample by ID with all responses and files"}, "response": []}, {"name": "Submit Individual Question Response", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Token {{auth_token}}", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "sample", "value": "{{sample_id}}", "type": "text"}, {"key": "question", "value": "{{question_id}}", "type": "text"}, {"key": "response", "value": "Individual response text", "type": "text"}, {"key": "selected_option_ids", "value": "[1,2]", "type": "text"}, {"key": "file", "type": "file", "src": []}]}, "url": {"raw": "{{base_url}}/api/question-responses/", "host": ["{{base_url}}"], "path": ["api", "question-responses", ""]}, "description": "Submit an individual question response with optional file upload"}, "response": []}], "description": "API endpoints for submitting survey responses with file upload support"}, {"name": "Authentication", "item": [{"name": "Get Auth <PERSON>ken", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"username\": \"{{username}}\",\n    \"password\": \"{{password}}\"\n}"}, "url": {"raw": "{{base_url}}/api/auth/login/", "host": ["{{base_url}}"], "path": ["api", "auth", "login", ""]}, "description": "Get authentication token for API access"}, "response": []}, {"name": "Create Test User", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"username\": \"test_surveyor\",\n    \"email\": \"<EMAIL>\",\n    \"password\": \"testpass123\",\n    \"name\": \"Test Surveyor\",\n    \"role\": \"Field Agent\"\n}"}, "url": {"raw": "{{base_url}}/api/users/", "host": ["{{base_url}}"], "path": ["api", "users", ""]}, "description": "Create a test user for API testing"}, "response": []}], "description": "Authentication endpoints for getting tokens and creating test users"}, {"name": "Examples and Templates", "item": [{"name": "Survey with Complex Flow Control", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Token {{auth_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"title\": \"Complex Flow Survey with Conditional Branching\",\n    \"description\": \"Survey demonstrating complex section flow with conditional branching and loops\",\n    \"status\": \"Draft\",\n    \"config_json\": {\n        \"theme\": \"complex_flow\",\n        \"allow_anonymous\": false,\n        \"estimated_time\": \"8 minutes\"\n    },\n    \"sections\": [\n        {\n            \"title\": \"Start\",\n            \"order\": 1,\n            \"next_section_default\": 1,\n            \"questions\": [\n                {\n                    \"question_text\": \"Welcome to our survey! Click Next to begin.\",\n                    \"type\": \"Boolean\",\n                    \"is_mandatory\": true,\n                    \"order\": 1\n                }\n            ]\n        },\n        {\n            \"title\": \"Age Check\",\n            \"order\": 2,\n            \"next_section_default\": 2,\n            \"questions\": [\n                {\n                    \"question_text\": \"Are you 18 years or older?\",\n                    \"type\": \"Boolean\",\n                    \"is_mandatory\": true,\n                    \"order\": 1\n                }\n            ]\n        },\n        {\n            \"title\": \"Adult Questions\",\n            \"order\": 3,\n            \"next_section_default\": 3,\n            \"questions\": [\n                {\n                    \"question_text\": \"What is your employment status?\",\n                    \"type\": \"MCQ\",\n                    \"is_mandatory\": true,\n                    \"order\": 1,\n                    \"options\": [\n                        {\"option_text\": \"Employed\", \"order\": 1},\n                        {\"option_text\": \"Unemployed\", \"order\": 2},\n                        {\"option_text\": \"Student\", \"order\": 3},\n                        {\"option_text\": \"Retired\", \"order\": 4}\n                    ]\n                }\n            ]\n        },\n        {\n            \"title\": \"Employment Status\",\n            \"order\": 4,\n            \"next_section_default\": 4,\n            \"questions\": [\n                {\n                    \"question_text\": \"What is your income level?\",\n                    \"type\": \"MCQ\",\n                    \"is_mandatory\": true,\n                    \"order\": 1,\n                    \"options\": [\n                        {\"option_text\": \"Low Income\", \"order\": 1},\n                        {\"option_text\": \"Middle Income\", \"order\": 2},\n                        {\"option_text\": \"High Income\", \"order\": 3}\n                    ]\n                }\n            ]\n        },\n        {\n            \"title\": \"Income Level\",\n            \"order\": 5,\n            \"next_section_default\": 5,\n            \"questions\": [\n                {\n                    \"question_text\": \"What type of products do you prefer?\",\n                    \"type\": \"MCQ\",\n                    \"is_mandatory\": true,\n                    \"order\": 1,\n                    \"options\": [\n                        {\"option_text\": \"Budget-friendly\", \"order\": 1},\n                        {\"option_text\": \"Mid-range\", \"order\": 2},\n                        {\"option_text\": \"Premium\", \"order\": 3}\n                    ]\n                }\n            ]\n        },\n        {\n            \"title\": \"Preferences\",\n            \"order\": 6,\n            \"next_section_default\": 6,\n            \"questions\": [\n                {\n                    \"question_text\": \"How did you hear about us?\",\n                    \"type\": \"MCQ\",\n                    \"is_mandatory\": false,\n                    \"order\": 1,\n                    \"options\": [\n                        {\"option_text\": \"Social Media\", \"order\": 1},\n                        {\"option_text\": \"Friend/Family\", \"order\": 2},\n                        {\"option_text\": \"Advertisement\", \"order\": 3},\n                        {\"option_text\": \"Search Engine\", \"order\": 4}\n                    ]\n                }\n            ]\n        },\n        {\n            \"title\": \"Final\",\n            \"order\": 7,\n            \"next_section_default\": null,\n            \"questions\": [\n                {\n                    \"question_text\": \"Thank you for completing our survey!\",\n                    \"type\": \"Text\",\n                    \"is_mandatory\": false,\n                    \"order\": 1\n                }\n            ]\n        }\n    ]\n}"}, "url": {"raw": "{{base_url}}/api/surveys/create-complete/", "host": ["{{base_url}}"], "path": ["api", "surveys", "create-complete", ""]}, "description": "Example of a complex survey with flow control and conditional branching"}, "response": []}, {"name": "Response with Multiple File Types", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Token {{auth_token}}", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "survey", "value": "{{survey_id}}", "type": "text"}, {"key": "latitude", "value": "40.7589", "type": "text"}, {"key": "longitude", "value": "-73.9851", "type": "text"}, {"key": "address", "value": "Times Square, New York, NY", "type": "text"}, {"key": "device_info", "value": "{\"device_model\":\"iPhone 14\",\"os_version\":\"iOS 17.0\",\"app_version\":\"2.1.0\",\"battery_level\":85,\"network_type\":\"WiFi\"}", "type": "text"}, {"key": "is_offline", "value": "false", "type": "text"}, {"key": "responses", "value": "[{\"question_id\":1,\"response\":\"Uploading resume\",\"selected_option_ids\":[]},{\"question_id\":2,\"response\":\"Uploading picture\",\"selected_option_ids\":[]},{\"question_id\":3,\"response\":\"Uploading audio\",\"selected_option_ids\":[]},{\"question_id\":4,\"response\":\"Uploading video\",\"selected_option_ids\":[]},{\"question_id\":5,\"response\":\"\",\"selected_option_ids\":[1,2]},{\"question_id\":6,\"response\":\"Yes\",\"selected_option_ids\":[]},{\"question_id\":7,\"response\":\"Great experience!\",\"selected_option_ids\":[]}]", "type": "text"}, {"key": "responses.0.file", "type": "file", "src": []}, {"key": "responses.1.file", "type": "file", "src": []}, {"key": "responses.2.file", "type": "file", "src": []}, {"key": "responses.3.file", "type": "file", "src": []}]}, "url": {"raw": "{{base_url}}/api/samples/submit-complete/", "host": ["{{base_url}}"], "path": ["api", "samples", "submit-complete", ""]}, "description": "Example of submitting responses with multiple file types (PDF, image, audio, video)"}, "response": []}], "description": "Example requests and templates for different use cases"}, {"name": "Dashboard Analytics API", "item": [{"name": "Get Comprehensive Dashboard", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Token {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/analytics/?date_range=30d", "host": ["{{base_url}}"], "path": ["api", "analytics", ""], "query": [{"key": "date_range", "value": "30d", "description": "Date range: 7d, 30d, 90d, 1y"}]}, "description": "Get comprehensive dashboard data with all analytics"}, "response": []}, {"name": "Get Overview Statistics", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Token {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/analytics/overview/?date_range=7d", "host": ["{{base_url}}"], "path": ["api", "analytics", "overview", ""], "query": [{"key": "date_range", "value": "7d", "description": "Date range: 7d, 30d, 90d, 1y"}]}, "description": "Get overview statistics for the dashboard"}, "response": []}, {"name": "Get Survey Analytics", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Token {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/analytics/surveys/?date_range=30d", "host": ["{{base_url}}"], "path": ["api", "analytics", "surveys", ""], "query": [{"key": "date_range", "value": "30d", "description": "Date range: 7d, 30d, 90d, 1y"}]}, "description": "Get detailed survey analytics"}, "response": []}, {"name": "Get Sample Analytics", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Token {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/analytics/samples/?date_range=30d", "host": ["{{base_url}}"], "path": ["api", "analytics", "samples", ""], "query": [{"key": "date_range", "value": "30d", "description": "Date range: 7d, 30d, 90d, 1y"}]}, "description": "Get sample collection analytics"}, "response": []}, {"name": "Get User Analytics", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Token {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/analytics/users/?date_range=30d", "host": ["{{base_url}}"], "path": ["api", "analytics", "users", ""], "query": [{"key": "date_range", "value": "30d", "description": "Date range: 7d, 30d, 90d, 1y"}]}, "description": "Get user performance analytics"}, "response": []}, {"name": "Get Assignment Analytics", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Token {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/analytics/assignments/?date_range=30d", "host": ["{{base_url}}"], "path": ["api", "analytics", "assignments", ""], "query": [{"key": "date_range", "value": "30d", "description": "Date range: 7d, 30d, 90d, 1y"}]}, "description": "Get assignment analytics"}, "response": []}, {"name": "Get QC Analytics", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Token {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/analytics/qc/?date_range=30d", "host": ["{{base_url}}"], "path": ["api", "analytics", "qc", ""], "query": [{"key": "date_range", "value": "30d", "description": "Date range: 7d, 30d, 90d, 1y"}]}, "description": "Get quality control analytics"}, "response": []}, {"name": "Get Company Analytics", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Token {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/analytics/companies/?date_range=30d", "host": ["{{base_url}}"], "path": ["api", "analytics", "companies", ""], "query": [{"key": "date_range", "value": "30d", "description": "Date range: 7d, 30d, 90d, 1y"}]}, "description": "Get company performance analytics"}, "response": []}, {"name": "Get Question Analytics", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Token {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/analytics/questions/?survey_id=1&date_range=30d", "host": ["{{base_url}}"], "path": ["api", "analytics", "questions", ""], "query": [{"key": "survey_id", "value": "1", "description": "Survey ID to filter questions (optional)"}, {"key": "date_range", "value": "30d", "description": "Date range: 7d, 30d, 90d, 1y"}]}, "description": "Get question-level analytics"}, "response": []}, {"name": "Get Real-time Statistics", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Token {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/analytics/realtime/", "host": ["{{base_url}}"], "path": ["api", "analytics", "realtime", ""]}, "description": "Get real-time statistics"}, "response": []}], "description": "Dashboard analytics endpoints for comprehensive insights"}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// Set default values for variables if not already set", "if (!pm.environment.get('base_url')) {", "    pm.environment.set('base_url', 'http://localhost:8000');", "}", "", "if (!pm.environment.get('username')) {", "    pm.environment.set('username', 'test_admin');", "}", "", "if (!pm.environment.get('password')) {", "    pm.environment.set('password', 'testpass123');", "}"]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["// Common test script for all requests", "pm.test('Status code is 200 or 201', function () {", "    pm.expect(pm.response.code).to.be.oneOf([200, 201]);", "});", "", "pm.test('Response has required fields', function () {", "    const responseJson = pm.response.json();", "    ", "    // Check if response is not empty", "    pm.expect(responseJson).to.not.be.null;", "    ", "    // For survey creation, check for id and title", "    if (pm.request.url.path.includes('surveys/create-complete')) {", "        pm.expect(responseJson).to.have.property('id');", "        pm.expect(responseJson).to.have.property('title');", "        ", "        // Store survey ID for later use", "        if (responseJson.id) {", "            pm.environment.set('survey_id', responseJson.id);", "        }", "    }", "    ", "    // For sample submission, check for id", "    if (pm.request.url.path.includes('samples/submit-complete')) {", "        pm.expect(responseJson).to.have.property('id');", "        ", "        // Store sample ID for later use", "        if (responseJson.id) {", "            pm.environment.set('sample_id', responseJson.id);", "        }", "    }", "});", "", "// Log response for debugging", "console.log('Response:', pm.response.json());"]}}], "variable": [{"key": "base_url", "value": "http://localhost:8000", "type": "string"}, {"key": "auth_token", "value": "", "type": "string"}, {"key": "username", "value": "test_admin", "type": "string"}, {"key": "password", "value": "testpass123", "type": "string"}, {"key": "survey_id", "value": "", "type": "string"}, {"key": "sample_id", "value": "", "type": "string"}, {"key": "question_id", "value": "", "type": "string"}]}