from django.contrib import admin
from .models import QCScore


@admin.register(QCScore)
class QCScoreAdmin(admin.ModelAdmin):
    list_display = ('sample', 'reviewer', 'score', 'reviewed_at')
    list_filter = ('score', 'reviewed_at', 'sample__survey')
    search_fields = ('sample__id', 'reviewer__name', 'reviewer__username', 'comment', 'remark')
    readonly_fields = ('reviewed_at',)
    ordering = ('-reviewed_at',)
