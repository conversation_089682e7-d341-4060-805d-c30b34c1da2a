from rest_framework import viewsets
from rest_framework.permissions import Is<PERSON><PERSON><PERSON>icated
from .serializers import QCScoreSerializer
from ..models import QCScore
from voter.contrib.audit import AuditLogMixin
from voter.contrib.pagination import PaginatedViewSetMixin
from voter.users.permissions import (
    IsAdmin, IsCompanyAdmin, IsAdminOrCompanyAdmin, IsQCReviewer,
    CanReviewSamples
)


class QCScoreViewSet(PaginatedViewSetMixin, AuditLogMixin, viewsets.ModelViewSet):
    queryset = QCScore.objects.all()
    serializer_class = QCScoreSerializer
    permission_classes = [IsAuthenticated]
    search_fields = ['sample__survey__title', 'reviewer__name']
    filterset_fields = ['sample', 'reviewer']
    date_filter_field = 'reviewed_at'
    ordering_fields = ['reviewed_at', 'sample__survey__title']
    
    def perform_create(self, serializer):
        """Override to log QC score creation with improved audit logging."""
        instance = serializer.save()
        
        # Get additional context data
        additional_data = self._get_additional_audit_data('create', instance, serializer.validated_data)
        
        # Create audit log
        self._create_audit_log(
            action=LogEntry.Action.CREATE,
            instance=instance,
            additional_data=additional_data,
            operation_context='qc_score_creation'
        )
        
        return instance
    
    def perform_update(self, serializer):
        """Override to log QC score updates with improved change detection."""
        # Get the instance before saving to capture old values
        instance = serializer.instance
        
        # Save the instance with new data
        updated_instance = serializer.save()
        
        # Get changes by comparing old and new values
        changes = self._get_changes(instance, serializer.validated_data)
        
        # Get additional context data
        additional_data = self._get_additional_audit_data('update', updated_instance, serializer.validated_data)
        
        # Create audit log
        self._create_audit_log(
            action=LogEntry.Action.UPDATE,
            instance=updated_instance,
            changes=changes,
            additional_data=additional_data,
            operation_context='qc_score_update'
        )
        
        return updated_instance
    
    def _get_additional_audit_data(self, action, instance, validated_data):
        """Add custom audit data for QC score operations."""
        additional_data = {
            'action_source': 'api',
            'sample_id': getattr(instance, 'sample_id', None),
            'reviewer_name': getattr(instance.reviewer, 'name', None) if hasattr(instance, 'reviewer') else None,
            'survey_title': getattr(instance.sample.survey, 'title', None) if hasattr(instance, 'sample') and hasattr(instance.sample, 'survey') else None,
        }
        
        if action == 'create':
            additional_data['created_via'] = 'api'
        elif action == 'update':
            additional_data['updated_fields'] = list(validated_data.keys())
        elif action == 'delete':
            additional_data['deleted_via'] = 'hard_delete'
        
        return additional_data
    
    def get_permissions(self):
        """Return appropriate permissions based on action"""
        if self.action in ['create', 'update', 'partial_update', 'destroy']:
            return [IsAuthenticated(), IsAdminOrCompanyAdmin(), IsQCReviewer()]
        return [IsAuthenticated()]
    
    def get_queryset(self):
        """Filter queryset based on user role and permissions"""
        queryset = QCScore.objects.all()
        
        # Admin can see all QC scores
        if self.request.user.is_admin():
            return queryset
        
        # Company Admin can see QC scores from their company
        if self.request.user.is_company_admin():
            return queryset.filter(reviewer__company=self.request.user.company)
        
        # QC Reviewer can only see their own QC scores
        if self.request.user.is_qc_reviewer():
            return queryset.filter(reviewer=self.request.user)
        
        # Surveyor and Vendor Surveyor can see QC scores from their company
        if self.request.user.is_surveyor() or self.request.user.is_vendor_surveyor():
            return queryset.filter(reviewer__company=self.request.user.company)
        
        # Default: return empty queryset for unknown roles
        return queryset.none()


 