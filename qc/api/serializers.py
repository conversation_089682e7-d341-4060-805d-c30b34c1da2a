from rest_framework import serializers
from ..models import QCScore


class QCScoreSerializer(serializers.ModelSerializer):
    sample_id = serializers.CharField(source='sample.id', read_only=True)
    survey_title = serializers.CharField(source='sample.survey.title', read_only=True)
    reviewer_name = serializers.CharField(source='reviewer.name', read_only=True)
    
    class Meta:
        model = QCScore
        fields = '__all__'
        read_only_fields = ('reviewed_at',) 