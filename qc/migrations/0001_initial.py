# Generated by Django 5.1.11 on 2025-06-29 05:53

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('samples', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='QCScore',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('score', models.DecimalField(decimal_places=2, max_digits=5, verbose_name='QC Score')),
                ('comment', models.TextField(blank=True, null=True, verbose_name='Comment')),
                ('remark', models.TextField(blank=True, null=True, verbose_name='Remark')),
                ('reviewed_at', models.DateTimeField(auto_now_add=True)),
                ('reviewer', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
                ('sample', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='qc_score', to='samples.sample')),
            ],
            options={
                'verbose_name': 'QC Score',
                'verbose_name_plural': 'QC Scores',
            },
        ),
    ]
