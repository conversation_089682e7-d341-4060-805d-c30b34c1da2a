# Generated by Django 5.1.11 on 2025-07-23 02:48

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('qc', '0001_initial'),
        ('surveys', '0003_question_file'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='qcscore',
            options={'ordering': ['-reviewed_at'], 'verbose_name': 'QC Score', 'verbose_name_plural': 'QC Scores'},
        ),
        migrations.AddField(
            model_name='qcscore',
            name='updated_at',
            field=models.DateTimeField(auto_now=True),
        ),
        migrations.CreateModel(
            name='QCSurveyAssignment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('assigned_at', models.DateTimeField(auto_now_add=True)),
                ('due_date', models.DateField(blank=True, null=True, verbose_name='Due Date')),
                ('status', models.CharField(choices=[('Assigned', 'Assigned'), ('InProgress', 'In Progress'), ('Completed', 'Completed'), ('OnHold', 'On Hold')], default='Assigned', max_length=20, verbose_name='Status')),
                ('total_samples', models.PositiveIntegerField(default=0, verbose_name='Total Samples')),
                ('reviewed_samples', models.PositiveIntegerField(default=0, verbose_name='Reviewed Samples')),
                ('approved_samples', models.PositiveIntegerField(default=0, verbose_name='Approved Samples')),
                ('rejected_samples', models.PositiveIntegerField(default=0, verbose_name='Rejected Samples')),
                ('guidelines', models.TextField(blank=True, null=True, verbose_name='Review Guidelines')),
                ('started_at', models.DateTimeField(blank=True, null=True, verbose_name='Started At')),
                ('completed_at', models.DateTimeField(blank=True, null=True, verbose_name='Completed At')),
                ('is_deleted', models.BooleanField(default=False)),
                ('assigned_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='qc_assignments_given', to=settings.AUTH_USER_MODEL)),
                ('assigned_to', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='qc_survey_assignments', to=settings.AUTH_USER_MODEL)),
                ('survey', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='qc_assignments', to='surveys.survey')),
            ],
            options={
                'verbose_name': 'QC Survey Assignment',
                'verbose_name_plural': 'QC Survey Assignments',
                'ordering': ['-assigned_at'],
                'unique_together': {('survey', 'assigned_to')},
            },
        ),
    ]
