from django.db import models
from django.db.models import OneToOneField, ForeignKey, CASCADE, SET_NULL, DecimalField, TextField, DateTimeField
from django.utils.translation import gettext_lazy as _
from django.contrib.auth import get_user_model
from auditlog.registry import auditlog

User = get_user_model()


class QCScore(models.Model):
    """
    QCScore model for tracking post-survey evaluations by reviewers
    """
    sample = OneToOneField('samples.Sample', on_delete=CASCADE, related_name='qc_score')
    reviewer = ForeignKey(User, on_delete=SET_NULL, null=True)
    score = DecimalField(_("QC Score"), max_digits=5, decimal_places=2)
    comment = TextField(_("Comment"), null=True, blank=True)
    remark = TextField(_("Remark"), null=True, blank=True)
    reviewed_at = DateTimeField(auto_now_add=True)
    
    def __str__(self):
        return f"{self.sample.id} - Score: {self.score} by {self.reviewer.name if self.reviewer else 'Unknown'}"
    
    class Meta:
        verbose_name = _("QC Score")
        verbose_name_plural = _("QC Scores")
