# Generated by Django 5.1.11 on 2025-06-29 05:53

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Question',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('question_text', models.TextField(verbose_name='Question Text')),
                ('type', models.CharField(choices=[('Text', 'Text'), ('MCQ', 'Multiple Choice'), ('Checkbox', 'Checkbox'), ('Boolean', 'Yes/No'), ('Audio', 'Audio'), ('ImageCapture', 'Image Capture')], max_length=20, verbose_name='Question Type')),
                ('is_mandatory', models.BooleanField(default=False, verbose_name='Is Mandatory')),
                ('condition_value', models.TextField(blank=True, null=True, verbose_name='Condition Value')),
                ('order', models.PositiveIntegerField(default=0, verbose_name='Order')),
                ('depends_on_question', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='dependent_questions', to='surveys.question')),
            ],
            options={
                'verbose_name': 'Question',
                'verbose_name_plural': 'Questions',
                'ordering': ['order'],
            },
        ),
        migrations.CreateModel(
            name='Section',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=255, verbose_name='Section Title')),
                ('order', models.PositiveIntegerField(verbose_name='Order')),
                ('next_section_default', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='previous_sections', to='surveys.section')),
            ],
            options={
                'verbose_name': 'Section',
                'verbose_name_plural': 'Sections',
                'ordering': ['order'],
            },
        ),
        migrations.CreateModel(
            name='QuestionOption',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('option_text', models.CharField(max_length=255, verbose_name='Option Text')),
                ('image', models.ImageField(blank=True, null=True, upload_to='option_images/', verbose_name='Option Image')),
                ('order', models.PositiveIntegerField(default=0, verbose_name='Order')),
                ('question', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='options', to='surveys.question')),
                ('linked_section', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='linked_options', to='surveys.section')),
            ],
            options={
                'verbose_name': 'Question Option',
                'verbose_name_plural': 'Question Options',
                'ordering': ['order'],
            },
        ),
        migrations.AddField(
            model_name='question',
            name='next_section_on_answer',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='conditional_questions', to='surveys.section'),
        ),
        migrations.AddField(
            model_name='question',
            name='section',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='questions', to='surveys.section'),
        ),
        migrations.CreateModel(
            name='Survey',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200, verbose_name='Survey Title')),
                ('description', models.TextField(blank=True, null=True, verbose_name='Description')),
                ('config_json', models.JSONField(blank=True, null=True, verbose_name='Configuration JSON')),
                ('status', models.CharField(choices=[('Draft', 'Draft'), ('Active', 'Active'), ('Closed', 'Closed')], default='Draft', max_length=20, verbose_name='Status')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('is_deleted', models.BooleanField(default=False)),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Survey',
                'verbose_name_plural': 'Surveys',
            },
        ),
        migrations.AddField(
            model_name='section',
            name='survey',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='sections', to='surveys.survey'),
        ),
    ]
