from django.contrib import admin
from .models import Survey, Section, Question, QuestionOption


@admin.register(Survey)
class SurveyAdmin(admin.ModelAdmin):
    list_display = ('title', 'status', 'created_by', 'created_at', 'is_deleted')
    list_filter = ('status', 'is_deleted', 'created_at')
    search_fields = ('title', 'description')
    readonly_fields = ('created_at',)
    ordering = ('-created_at',)


@admin.register(Section)
class SectionAdmin(admin.ModelAdmin):
    list_display = ('title', 'survey', 'order', 'next_section_default')
    list_filter = ('survey',)
    search_fields = ('title', 'survey__title')
    ordering = ('survey', 'order')


@admin.register(Question)
class QuestionAdmin(admin.ModelAdmin):
    list_display = ('question_text', 'section', 'type', 'is_mandatory', 'order')
    list_filter = ('type', 'is_mandatory', 'section__survey')
    search_fields = ('question_text', 'section__title')
    ordering = ('section', 'order')


@admin.register(QuestionOption)
class QuestionOptionAdmin(admin.ModelAdmin):
    list_display = ('option_text', 'question', 'order')
    list_filter = ('question__type', 'question__section__survey')
    search_fields = ('option_text', 'question__question_text')
    ordering = ('question', 'order')
