import json
from rest_framework import serializers
from ..models import Survey, Section, Question, QuestionOption
from config.storage_backends import handle_file_storage
from django.utils import timezone
from assignments.models import SurveyAssignment


# Global variable to store question attributes during creation
_question_attributes = {}

class CustomFileField(serializers.FileField):
    """
    Custom file field that handles file uploads from multipart form data
    """
    def to_internal_value(self, data):
        # If data is already a file object, return it
        if hasattr(data, 'read'):
            return data
        
        # If data is a string (filename), return None (no file uploaded)
        if isinstance(data, str):
            return None
        
        # For other cases, let the parent handle it
        return super().to_internal_value(data)


class CustomImageField(serializers.ImageField):
    """
    Custom image field that handles image uploads from multipart form data
    """
    def to_internal_value(self, data):
        # If data is already a file object, return it
        if hasattr(data, 'read'):
            return data
        
        # If data is a string (filename), return None (no image uploaded)
        if isinstance(data, str):
            return None
        
        # For other cases, let the parent handle it
        return super().to_internal_value(data)


class ImageDisplayField(serializers.ImageField):
    """
    Image field for displaying existing images with proper URLs
    """
    def to_representation(self, value):
        if value:
            # Use the storage backend's URL method to ensure consistent format
            return value.url
        return None


class FileDisplayField(serializers.FileField):
    """
    File field for displaying existing files with proper URLs
    """
    def to_representation(self, value):
        if value:
            # Use the storage backend's URL method to ensure consistent format
            return value.url
        return None


class QuestionOptionSerializer(serializers.ModelSerializer):
    image = ImageDisplayField(required=False, allow_null=True)
    
    class Meta:
        model = QuestionOption
        fields = '__all__'
    
    def create(self, validated_data):
        # Handle image upload
        image = validated_data.pop('image', None)
        if image is not None:
            folder = 'options'
            success, message = handle_file_storage(validated_data, image, folder, 'image')
            if not success:
                raise serializers.ValidationError(message)
        
        return super().create(validated_data)
    
    def update(self, instance, validated_data):
        # Handle image upload
        image = validated_data.pop('image', None)
        if image is not None:
            folder = 'options'
            success, message = handle_file_storage(validated_data, image, folder, 'image')
            if not success:
                raise serializers.ValidationError(message)
        
        return super().update(instance, validated_data)


class QuestionSerializer(serializers.ModelSerializer):
    options = QuestionOptionSerializer(many=True, read_only=True)
    file = FileDisplayField(required=False, allow_null=True)
    depends_on_question = serializers.SerializerMethodField()

    class Meta:
        model = Question
        fields = '__all__'

    def get_depends_on_question(self, obj):
        return obj.depends_on_question.id if obj.depends_on_question else None

    def create(self, validated_data):
        # Handle file upload
        file = validated_data.pop('file', None)
        if file is not None:
            folder = 'questions'
            success, message = handle_file_storage(validated_data, file, folder, 'file')
            if not success:
                raise serializers.ValidationError(message)
        
        return super().create(validated_data)
    
    def update(self, instance, validated_data):
        # Handle file upload
        file = validated_data.pop('file', None)
        if file is not None:
            folder = 'questions'
            success, message = handle_file_storage(validated_data, file, folder, 'file')
            if not success:
                raise serializers.ValidationError(message)
        
        return super().update(instance, validated_data)


class SectionSerializer(serializers.ModelSerializer):
    questions = QuestionSerializer(many=True, read_only=True)
    
    class Meta:
        model = Section
        fields = '__all__'


class SurveyAssignmentSerializer(serializers.ModelSerializer):
    """
    Serializer for SurveyAssignment to be included in Survey responses
    """
    assignee_name = serializers.CharField(source='assignee.name', read_only=True)
    assignee_username = serializers.CharField(source='assignee.username', read_only=True)
    assigned_by_name = serializers.CharField(source='assigned_by.name', read_only=True)
    
    class Meta:
        model = SurveyAssignment
        fields = [
            'id', 'role', 'date', 'is_one_time', 'is_closed', 'status',
            'assigned_at', 'daily_target_samples', 'total_target_samples',
            'assignee_name', 'assignee_username', 'assigned_by_name'
        ]


class SurveySerializer(serializers.ModelSerializer):
    sections = SectionSerializer(many=True, read_only=True)
    created_by_name = serializers.CharField(source='created_by.name', read_only=True)
    samples_count = serializers.SerializerMethodField(default=0)
    today_samples_count = serializers.SerializerMethodField(default=0)
    total_users_count = serializers.SerializerMethodField(default=0)
    today_users_count = serializers.SerializerMethodField(default=0)
    assignments = serializers.SerializerMethodField()
    
    class Meta:
        model = Survey
        fields = '__all__'
        read_only_fields = ('created_at', 'created_by')
    
    def get_samples_count(self, obj):
        """
        Get the count of samples (survey responses) for this survey
        """
        return obj.sample_set.count()

    def get_today_samples_count(self, obj):
        """
        Get the count of samples (survey responses) for this survey for today
        """
        return obj.sample_set.filter(timestamp__date=timezone.now().date()).count()
    
    def get_total_users_count(self, obj):
        """
        Get the total count of users who have collected samples for this survey
        """
        # Count users who have collected samples for this survey
        total_users = obj.sample_set.filter(
            user__isnull=False
        ).values('user').distinct().count()
        
        return total_users
    
    def get_today_users_count(self, obj):
        """
        Get the count of users who collected samples today for this survey
        """
        from django.utils import timezone
        
        # Count users who collected samples today for this survey
        today_users = obj.sample_set.filter(
            user__isnull=False,
            timestamp__date=timezone.now().date()
        ).values('user').distinct().count()
        
        return today_users
    
    def get_assignments(self, obj):
        """
        Get assignments for this survey based on user role
        """
        request = self.context.get('request')
        if not request or not request.user:
            return []
        
        # Only include assignments for non-admin/non-company-admin users
        if request.user.is_admin() or request.user.is_company_admin():
            return []
        
        # For surveyors and vendor surveyors, include their assignments for this survey
        if request.user.is_surveyor() or request.user.is_vendor_surveyor():
            assignments = SurveyAssignment.objects.filter(
                survey=obj,
                assignee=request.user,
                is_deleted=False
            )
            return SurveyAssignmentSerializer(assignments, many=True).data
        
        # For QC reviewers, include assignments from their company for this survey
        if request.user.is_qc_reviewer():
            assignments = SurveyAssignment.objects.filter(
                survey=obj,
                assignee__company=request.user.company,
                is_deleted=False
            )
            return SurveyAssignmentSerializer(assignments, many=True).data
        
        return []


# Nested serializers for creating complete survey in one request
class QuestionOptionCreateSerializer(serializers.ModelSerializer):
    # Use IntegerField for linked_section to handle indices during creation
    linked_section = serializers.IntegerField(required=False, allow_null=True)
    image = CustomImageField(required=False, allow_null=True)
    
    class Meta:
        model = QuestionOption
        fields = ['option_text', 'image', 'linked_section', 'order']
    
    def validate_linked_section(self, value):
        """Skip validation during creation - will be resolved later"""
        return value
    
    def create(self, validated_data):
        # Handle image upload
        image = validated_data.pop('image', None)
        if image is not None and hasattr(image, 'read'):  # Check if it's actually a file
            folder = 'options'
            success, message = handle_file_storage(validated_data, image, folder, 'image')
            if not success:
                raise serializers.ValidationError(message)
        else:
            pass # No valid image found
        
        # Don't set linked_section during creation - will be resolved later
        linked_section_index = validated_data.pop('linked_section', None)
        option = super().create(validated_data)
        
        # Store the index for later resolution
        if linked_section_index is not None:
            option._linked_section_index = linked_section_index
            
            # Store in global variable for options
            global _question_attributes
            if 'options' not in _question_attributes:
                _question_attributes['options'] = {}
            _question_attributes['options'][option.id] = {'_linked_section_index': linked_section_index}
        
        return option


class QuestionCreateSerializer(serializers.ModelSerializer):
    options = QuestionOptionCreateSerializer(many=True, required=False)
    file = CustomFileField(required=False, allow_null=True)
    # Use IntegerField for next_section_on_answer to handle indices during creation
    next_section_on_answer = serializers.IntegerField(required=False, allow_null=True)
    # Use CharField for depends_on_question to handle question text during creation
    depends_on_question = serializers.CharField(required=False, allow_null=True, allow_blank=True)
    
    class Meta:
        model = Question
        fields = [
            'question_text', 'type', 'file', 'is_mandatory', 'depends_on_question',
            'condition_value', 'next_section_on_answer', 'order', 'options'
        ]
    
    def validate_next_section_on_answer(self, value):
        """Skip validation during creation - will be resolved later"""
        return value
    
    def validate_depends_on_question(self, value):
        """Skip validation during creation - will be resolved later"""
        return value
    
    def create(self, validated_data):
        # Handle file upload for question
        file = validated_data.pop('file', None)
        if file is not None and hasattr(file, 'read'):  # Check if it's actually a file
            folder = 'questions'
            success, message = handle_file_storage(validated_data, file, folder, 'file')
            if not success:
                raise serializers.ValidationError(message)
        
        options_data = validated_data.pop('options', [])
        # Don't set next_section_on_answer during creation - will be resolved later
        next_section_index = validated_data.pop('next_section_on_answer', None)
        # Don't set depends_on_question during creation - will be resolved later
        depends_on_question_text = validated_data.pop('depends_on_question', None)
        
        # Remove depends_on_question from validated_data to avoid FK constraint issues
        validated_data.pop('depends_on_question', None)
        
        question = Question.objects.create(**validated_data)
        
        # Store the index for later resolution
        if next_section_index is not None:
            question._next_section_index = next_section_index
        
        # Store the question text for later resolution
        if depends_on_question_text:
            question._depends_on_question_text = depends_on_question_text
        
        # Store attributes in global variable
        global _question_attributes
        _question_attributes[question.id] = {}
        if depends_on_question_text:
            _question_attributes[question.id]['_depends_on_question_text'] = depends_on_question_text
        if next_section_index is not None:
            _question_attributes[question.id]['_next_section_index'] = next_section_index
        
        # Create options for this question
        for i, option_data in enumerate(options_data):
            option_data['question'] = question
            QuestionOptionCreateSerializer().create(option_data)
        
        return question


class SectionCreateSerializer(serializers.ModelSerializer):
    questions = QuestionCreateSerializer(many=True, required=False, write_only=True)
    next_section_default = serializers.IntegerField(required=False, allow_null=True)
    
    class Meta:
        model = Section
        fields = ['title', 'order', 'next_section_default', 'questions']
    
    def validate_next_section_default(self, value):
        """
        Validate that next_section_default is a valid integer index
        """
        if value is not None and not isinstance(value, int):
            raise serializers.ValidationError('next_section_default must be an integer or null')
        return value
    
    def create(self, validated_data):
        questions_data = validated_data.pop('questions', [])
        next_section_default = validated_data.pop('next_section_default', None)
        
        # Create section without next_section_default first
        section = Section.objects.create(**validated_data)
        
        # Create questions for this section
        for question_data in questions_data:
            question_data['section'] = section
            QuestionCreateSerializer().create(question_data)
        
        return section


class SurveyCreateSerializer(serializers.ModelSerializer):
    sections = SectionCreateSerializer(many=True, required=False, write_only=True)
    
    class Meta:
        model = Survey
        fields = ['title', 'description', 'config_json', 'status', 'sections']
        read_only_fields = ('created_by',)
    
    def to_internal_value(self, data):
        try:
            # If sections is a string, try to parse it as JSON
            if 'sections' in data and isinstance(data['sections'], str):
                try:
                    sections_data = json.loads(data['sections'])
                    # Create a mutable copy of data
                    mutable_data = data.copy()
                    mutable_data['sections'] = sections_data
                    data = mutable_data
                except json.JSONDecodeError as e:
                    raise serializers.ValidationError({
                        'sections': 'Invalid JSON string in sections field'
                    })
            
            # Ensure data is a proper dictionary before calling super()
            if not isinstance(data, dict):
                # Convert QueryDict or other data types to dict
                if hasattr(data, 'dict'):
                    data = data.dict()
                else:
                    data = dict(data) if data else {}
            
            # Process file fields and attach them to the appropriate nested data
            file_fields = {}
            filtered_data = {}
            
            for key, value in data.items():
                if key.startswith('sections.') and (key.endswith('.file') or key.endswith('.image')):
                    # Extract the path to attach the file to the correct nested object
                    file_fields[key] = value
                else:
                    filtered_data[key] = value
            
            result = super().to_internal_value(filtered_data)
            
            # Manually validate sections if present in data and not in result
            if 'sections' in data and not result.get('sections'):
                sections_serializer = SectionCreateSerializer(many=True, context=self.context)
                validated_sections = sections_serializer.run_validation(data['sections'])
                result['sections'] = validated_sections
            
            # Attach file fields to the appropriate nested objects
            if 'sections' in result and file_fields:
                for file_key, file_value in file_fields.items():
                    # Parse the key like "sections.0.questions.1.options.2.image"
                    parts = file_key.split('.')
                    if len(parts) >= 4:  # sections.0.questions.1.options.2.image
                        section_index = int(parts[1])
                        if parts[2] == 'questions':
                            question_index = int(parts[3])
                            if len(parts) >= 6 and parts[4] == 'options':
                                option_index = int(parts[5])
                                field_name = parts[6]  # 'image'
                                
                                # Attach file to the option
                                if (section_index < len(result['sections']) and
                                    question_index < len(result['sections'][section_index].get('questions', [])) and
                                    option_index < len(result['sections'][section_index]['questions'][question_index].get('options', []))):
                                    result['sections'][section_index]['questions'][question_index]['options'][option_index][field_name] = file_value
                            else:
                                field_name = parts[4]  # 'file'
                                # Attach file to the question
                                if (section_index < len(result['sections']) and
                                    question_index < len(result['sections'][section_index].get('questions', []))):
                                    result['sections'][section_index]['questions'][question_index][field_name] = file_value
            
            return result
        except Exception as e:
            # Provide a clean error message if something goes wrong
            raise serializers.ValidationError({
                'non_field_errors': f'Failed to process request data: {str(e)}'
            })

    def validate_sections(self, value):
        # Ensure sections is a list and not empty (if required)
        if not isinstance(value, list):
            raise serializers.ValidationError('Sections must be a list.')
        
        # Validate section order and ensure sequential flow
        for i, section in enumerate(value):
            if not isinstance(section, dict):
                raise serializers.ValidationError(f'Section {i} must be a dictionary.')
            
            # Ensure order is set
            if 'order' not in section:
                section['order'] = i + 1
            
            # Auto-assign next_section_default if not provided (for sequential flow)
            if 'next_section_default' not in section:
                if i < len(value) - 1:
                    section['next_section_default'] = i + 1  # Next section index
                else:
                    section['next_section_default'] = None  # Last section
        
        return value

    def create(self, validated_data):
        sections_data = validated_data.pop('sections', [])
        survey = Survey.objects.create(**validated_data)
        
        # Store next_section_default references to resolve after creation
        section_references = []
        
        # Create sections for this survey
        created_sections = []
        
        for i, section_data in enumerate(sections_data):
            section_data['survey'] = survey
            next_section_default_id = section_data.get('next_section_default')
            
            # Store reference for later resolution (using index)
            if next_section_default_id is not None:
                section_references.append((i, next_section_default_id))
            
            # Remove next_section_default from data to avoid FK constraint issues
            section_data.pop('next_section_default', None)
            
            # Create section
            section = SectionCreateSerializer().create(section_data)
            created_sections.append(section)
        
        # Resolve next_section_default references
        for source_index, target_index in section_references:
            if 0 <= source_index < len(created_sections) and 0 <= target_index < len(created_sections):
                source_section = created_sections[source_index]
                target_section = created_sections[target_index]
                source_section.next_section_default = target_section
                source_section.save()
        
        # Resolve question and option references
        # First, create a mapping of all questions by their text for depends_on_question resolution
        all_questions = {}
        for section in created_sections:
            for question in section.questions.all():
                all_questions[question.question_text] = question
        
        for section in created_sections:
            for question in section.questions.all():
                question_id = question.id
                
                # Resolve question next_section_on_answer
                if question_id in _question_attributes and '_next_section_index' in _question_attributes[question_id]:
                    target_index = _question_attributes[question_id]['_next_section_index']
                    if 0 <= target_index < len(created_sections):
                        question.next_section_on_answer = created_sections[target_index]
                        question.save()
                
                # Resolve question depends_on_question using question text
                if question_id in _question_attributes and '_depends_on_question_text' in _question_attributes[question_id]:
                    depends_text = _question_attributes[question_id]['_depends_on_question_text']
                    if depends_text in all_questions:
                        question.depends_on_question = all_questions[depends_text]
                        question.save()
                
                # Resolve option linked_section
                for option in question.options.all():
                    option_id = option.id
                    if ('options' in _question_attributes and 
                        option_id in _question_attributes['options'] and 
                        '_linked_section_index' in _question_attributes['options'][option_id]):
                        target_index = _question_attributes['options'][option_id]['_linked_section_index']
                        if 0 <= target_index < len(created_sections):
                            option.linked_section = created_sections[target_index]
                            option.save()
        
        # Clear the global variable for next use
        _question_attributes.clear()
        
        # Re-fetch the survey from the database to ensure all related fields are up to date
        return Survey.objects.prefetch_related(
            'sections__questions__options',
            'sections__questions__depends_on_question'
        ).select_related(
        ).get(pk=survey.pk)


# Nested serializers for updating complete survey in one request
class QuestionOptionUpdateSerializer(serializers.ModelSerializer):
    id = serializers.IntegerField(required=False)  # Optional for new options
    image = CustomImageField(required=False, allow_null=True)
    # Use IntegerField for linked_section to handle indices during updates
    linked_section = serializers.IntegerField(required=False, allow_null=True)
    
    class Meta:
        model = QuestionOption
        fields = ['id', 'option_text', 'image', 'linked_section', 'order']
    
    def validate_linked_section(self, value):
        """Skip validation during update - will be resolved later"""
        return value
    
    def update(self, instance, validated_data):
        # Handle image upload
        image = validated_data.pop('image', None)
        if image is not None and hasattr(image, 'read'):  # Check if it's actually a file
            folder = 'options'
            success, message = handle_file_storage(validated_data, image, folder, 'image')
            if not success:
                raise serializers.ValidationError(message)
        
        # Don't set linked_section during update - will be resolved later
        linked_section_value = validated_data.pop('linked_section', None)
        
        # Store the value for later resolution
        if linked_section_value is not None:
            # If it's a database ID, we need to convert it to section index
            if isinstance(linked_section_value, int) and linked_section_value > 100:  # Likely a database ID
                # Find the section by ID and get its index
                try:
                    from surveys.models import Section
                    section = Section.objects.get(id=linked_section_value)
                    # Get all sections for this survey and find the index
                    all_sections = list(section.survey.sections.all().order_by('order'))
                    section_index = None
                    for i, s in enumerate(all_sections):
                        if s.id == linked_section_value:
                            section_index = i
                            break
                    if section_index is not None:
                        linked_section_value = section_index
                except Section.DoesNotExist:
                    linked_section_value = None
            
            instance._linked_section_index = linked_section_value
            
            # Store in global variable for options
            global _question_attributes
            if 'options' not in _question_attributes:
                _question_attributes['options'] = {}
            _question_attributes['options'][instance.id] = {'_linked_section_index': linked_section_value}
        
        return super().update(instance, validated_data)


class QuestionUpdateSerializer(serializers.ModelSerializer):
    id = serializers.IntegerField(required=False)  # Optional for new questions
    options = QuestionOptionUpdateSerializer(many=True, required=False)
    file = CustomFileField(required=False, allow_null=True)
    # Custom field to handle depends_on_question by text during updates
    depends_on_question_text = serializers.CharField(required=False, allow_null=True, allow_blank=True, write_only=True)
    # Use IntegerField for next_section_on_answer to handle indices during updates
    next_section_on_answer = serializers.IntegerField(required=False, allow_null=True)
    
    class Meta:
        model = Question
        fields = [
            'id', 'question_text', 'type', 'file', 'is_mandatory', 'depends_on_question',
            'condition_value', 'next_section_on_answer', 'order', 'options', 'depends_on_question_text'
        ]
    
    def validate_next_section_on_answer(self, value):
        """Skip validation during update - will be resolved later"""
        return value
    
    def validate_depends_on_question_text(self, value):
        """Skip validation during update - will be resolved later"""
        return value
    
    def update(self, instance, validated_data):
        # Handle file upload for question
        file = validated_data.pop('file', None)
        if file is not None and hasattr(file, 'read'):  # Check if it's actually a file
            folder = 'questions'
            success, message = handle_file_storage(validated_data, file, folder, 'file')
            if not success:
                raise serializers.ValidationError(message)
        
        options_data = validated_data.pop('options', None)
        depends_on_question_text = validated_data.pop('depends_on_question_text', None)
        next_section_index = validated_data.pop('next_section_on_answer', None)
        
        # Don't set depends_on_question during update - will be resolved later
        validated_data.pop('depends_on_question', None)
        
        # Store the index for later resolution
        if next_section_index is not None:
            # If it's a database ID, we need to convert it to section index
            if isinstance(next_section_index, int) and next_section_index > 100:  # Likely a database ID
                # Find the section by ID and get its index
                try:
                    from surveys.models import Section
                    section = Section.objects.get(id=next_section_index)
                    # Get all sections for this survey and find the index
                    all_sections = list(section.survey.sections.all().order_by('order'))
                    section_index = None
                    for i, s in enumerate(all_sections):
                        if s.id == next_section_index:
                            section_index = i
                            break
                    if section_index is not None:
                        next_section_index = section_index
                except Section.DoesNotExist:
                    next_section_index = None
            
            instance._next_section_index = next_section_index
        
        # Store the question text for later resolution
        if depends_on_question_text:
            instance._depends_on_question_text = depends_on_question_text
        
        # Store attributes in global variable
        global _question_attributes
        if instance.id not in _question_attributes:
            _question_attributes[instance.id] = {}
        if depends_on_question_text:
            _question_attributes[instance.id]['_depends_on_question_text'] = depends_on_question_text
        if next_section_index is not None:
            _question_attributes[instance.id]['_next_section_index'] = next_section_index
        
        # Update question fields
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()
        
        # Handle options - update existing, create new, delete missing
        if options_data is not None:
            existing_option_ids = set(instance.options.values_list('id', flat=True))
            updated_option_ids = set()
            
            for option_data in options_data:
                option_id = option_data.get('id')
                if option_id and option_id in existing_option_ids:
                    # Update existing option
                    option = instance.options.get(id=option_id)
                    option_serializer = QuestionOptionUpdateSerializer(option, data=option_data, context=self.context)
                    if option_serializer.is_valid():
                        option_serializer.save()
                    updated_option_ids.add(option_id)
                else:
                    # Create new option - handle linked_section for later resolution
                    linked_section_value = option_data.pop('linked_section', None)
                    
                    # If it's a database ID, we need to convert it to section index
                    if linked_section_value is not None and isinstance(linked_section_value, int) and linked_section_value > 100:
                        # Find the section by ID and get its index
                        try:
                            from surveys.models import Section
                            section = Section.objects.get(id=linked_section_value)
                            # Get all sections for this survey and find the index
                            all_sections = list(section.survey.sections.all().order_by('order'))
                            section_index = None
                            for i, s in enumerate(all_sections):
                                if s.id == linked_section_value:
                                    section_index = i
                                    break
                            if section_index is not None:
                                linked_section_value = section_index
                        except Section.DoesNotExist:
                            linked_section_value = None
                    
                    option_data['question'] = instance
                    new_option = QuestionOptionCreateSerializer().create(option_data)
                    
                    # Store the linked_section value for later resolution
                    if linked_section_value is not None:
                        if 'options' not in _question_attributes:
                            _question_attributes['options'] = {}
                        _question_attributes['options'][new_option.id] = {'_linked_section_index': linked_section_value}
            
            # Delete options that were not updated (removed from the request)
            options_to_delete = existing_option_ids - updated_option_ids
            if options_to_delete:
                instance.options.filter(id__in=options_to_delete).delete()
        
        return instance


class SectionUpdateSerializer(serializers.ModelSerializer):
    id = serializers.IntegerField(required=False)  # Optional for new sections
    questions = QuestionUpdateSerializer(many=True, required=False)
    # Use IntegerField for next_section_default to handle indices during updates
    next_section_default = serializers.IntegerField(required=False, allow_null=True)
    
    class Meta:
        model = Section
        fields = ['id', 'title', 'order', 'next_section_default', 'questions']
    
    def validate_next_section_default(self, value):
        """Skip validation during update - will be resolved later"""
        return value
    
    def update(self, instance, validated_data):
        questions_data = validated_data.pop('questions', None)
        next_section_default_index = validated_data.pop('next_section_default', None)
        
        # Don't set next_section_default during update - will be resolved later
        validated_data.pop('next_section_default', None)
        
        # Store the index for later resolution
        if next_section_default_index is not None:
            # If it's a database ID, we need to convert it to section index
            if isinstance(next_section_default_index, int) and next_section_default_index > 100:  # Likely a database ID
                # Find the section by ID and get its index
                try:
                    from surveys.models import Section
                    section = Section.objects.get(id=next_section_default_index)
                    # Get all sections for this survey and find the index
                    all_sections = list(section.survey.sections.all().order_by('order'))
                    section_index = None
                    for i, s in enumerate(all_sections):
                        if s.id == next_section_default_index:
                            section_index = i
                            break
                    if section_index is not None:
                        next_section_default_index = section_index
                except Section.DoesNotExist:
                    next_section_default_index = None
            
            instance._next_section_default_index = next_section_default_index
        
        # Update section fields
        for attr, value in validated_data.items():
            if attr != 'id':
                setattr(instance, attr, value)
        instance.save()
        
        # Handle questions - update existing, create new, delete missing
        if questions_data is not None:
            existing_question_ids = set(instance.questions.values_list('id', flat=True))
            updated_question_ids = set()
            
            for question_data in questions_data:
                question_id = question_data.get('id')
                if question_id and question_id in existing_question_ids:
                    # Update existing question
                    question = instance.questions.get(id=question_id)
                    question_serializer = QuestionUpdateSerializer(question, data=question_data, context=self.context)
                    if question_serializer.is_valid():
                        question_serializer.save()
                    updated_question_ids.add(question_id)
                else:
                    # Create new question - convert update format to create format
                    create_question_data = question_data.copy()
                    
                    # Convert depends_on_question_text to depends_on_question for creation
                    if 'depends_on_question_text' in create_question_data:
                        create_question_data['depends_on_question'] = create_question_data.pop('depends_on_question_text')
                    
                    # Convert next_section_on_answer from index to actual section if needed
                    if 'next_section_on_answer' in create_question_data and isinstance(create_question_data['next_section_on_answer'], int):
                        # For new questions, we'll handle this in the resolution phase
                        pass
                    
                    create_question_data['section'] = instance
                    QuestionCreateSerializer().create(create_question_data)
            
            # Delete questions that were not updated (removed from the request)
            questions_to_delete = existing_question_ids - updated_question_ids
            if questions_to_delete:
                instance.questions.filter(id__in=questions_to_delete).delete()
        
        return instance


class SurveyUpdateSerializer(serializers.ModelSerializer):
    sections = SectionUpdateSerializer(many=True, required=False)
    
    class Meta:
        model = Survey
        fields = ['title', 'description', 'config_json', 'status', 'sections']
        read_only_fields = ('created_by',)
    
    def to_internal_value(self, data):
        try:
            # If sections is a string, try to parse it as JSON
            if 'sections' in data and isinstance(data['sections'], str):
                try:
                    sections_data = json.loads(data['sections'])
                    # Create a mutable copy of data
                    mutable_data = data.copy()
                    mutable_data['sections'] = sections_data
                    data = mutable_data
                except json.JSONDecodeError as e:
                    raise serializers.ValidationError({
                        'sections': 'Invalid JSON string in sections field'
                    })
            
            # Ensure data is a proper dictionary before calling super()
            if not isinstance(data, dict):
                # Convert QueryDict or other data types to dict
                if hasattr(data, 'dict'):
                    data = data.dict()
                else:
                    data = dict(data) if data else {}
            
            # Process file fields and attach them to the appropriate nested data
            file_fields = {}
            filtered_data = {}
            
            for key, value in data.items():
                if key.startswith('sections.') and (key.endswith('.file') or key.endswith('.image')):
                    # Extract the path to attach the file to the correct nested object
                    file_fields[key] = value
                else:
                    filtered_data[key] = value
            
            result = super().to_internal_value(filtered_data)
            
            # Manually validate sections if present in data and not in result
            if 'sections' in data and not result.get('sections'):
                sections_serializer = SectionUpdateSerializer(many=True, context=self.context)
                validated_sections = sections_serializer.run_validation(data['sections'])
                result['sections'] = validated_sections
            
            # Attach file fields to the appropriate nested objects
            if 'sections' in result and file_fields:
                for file_key, file_value in file_fields.items():
                    # Parse the key like "sections.0.questions.1.options.2.image"
                    parts = file_key.split('.')
                    if len(parts) >= 4:  # sections.0.questions.1.options.2.image
                        section_index = int(parts[1])
                        if parts[2] == 'questions':
                            question_index = int(parts[3])
                            if len(parts) >= 6 and parts[4] == 'options':
                                option_index = int(parts[5])
                                field_name = parts[6]  # 'image'
                                
                                # Attach file to the option
                                if (section_index < len(result['sections']) and
                                    question_index < len(result['sections'][section_index].get('questions', [])) and
                                    option_index < len(result['sections'][section_index]['questions'][question_index].get('options', []))):
                                    result['sections'][section_index]['questions'][question_index]['options'][option_index][field_name] = file_value
                            else:
                                field_name = parts[4]  # 'file'
                                # Attach file to the question
                                if (section_index < len(result['sections']) and
                                    question_index < len(result['sections'][section_index].get('questions', []))):
                                    result['sections'][section_index]['questions'][question_index][field_name] = file_value
            
            return result
        except Exception as e:
            # Provide a clean error message if something goes wrong
            raise serializers.ValidationError({
                'non_field_errors': f'Failed to process request data: {str(e)}'
            })

    def update(self, instance, validated_data):
        sections_data = validated_data.pop('sections', None)
        
        # Update survey fields
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()
        
        # Handle sections - update existing, create new, delete missing
        if sections_data is not None:
            existing_section_ids = set(instance.sections.values_list('id', flat=True))
            updated_section_ids = set()
            
            for section_data in sections_data:
                section_id = section_data.get('id')
                if section_id and section_id in existing_section_ids:
                    # Update existing section
                    section = instance.sections.get(id=section_id)
                    section_serializer = SectionUpdateSerializer(section, data=section_data, context=self.context)
                    if section_serializer.is_valid():
                        section_serializer.save()
                    updated_section_ids.add(section_id)
                else:
                    # Create new section
                    section_data['survey'] = instance
                    SectionCreateSerializer().create(section_data)
            
            # Delete sections that were not updated (removed from the request)
            sections_to_delete = existing_section_ids - updated_section_ids
            if sections_to_delete:
                instance.sections.filter(id__in=sections_to_delete).delete()
        
        # Resolve references after all sections are processed
        if sections_data is not None:
            # Get all sections for this survey
            all_sections = list(instance.sections.all().order_by('order'))
            
            # Resolve section next_section_default references
            for section in all_sections:
                if hasattr(section, '_next_section_default_index'):
                    target_index = section._next_section_default_index
                    if 0 <= target_index < len(all_sections):
                        section.next_section_default = all_sections[target_index]
                        section.save()
            
            # Resolve question and option references
            # First, create a mapping of all questions by their text for depends_on_question resolution
            all_questions = {}
            for section in all_sections:
                for question in section.questions.all():
                    all_questions[question.question_text] = question
            
            for section in all_sections:
                for question in section.questions.all():
                    question_id = question.id
                    
                    # Resolve question next_section_on_answer
                    if question_id in _question_attributes and '_next_section_index' in _question_attributes[question_id]:
                        target_index = _question_attributes[question_id]['_next_section_index']
                        if 0 <= target_index < len(all_sections):
                            question.next_section_on_answer = all_sections[target_index]
                            question.save()
                    
                    # Resolve question depends_on_question using question text
                    if question_id in _question_attributes and '_depends_on_question_text' in _question_attributes[question_id]:
                        depends_text = _question_attributes[question_id]['_depends_on_question_text']
                        if depends_text in all_questions:
                            question.depends_on_question = all_questions[depends_text]
                            question.save()
                    
                    # Resolve option linked_section
                    for option in question.options.all():
                        option_id = option.id
                        if ('options' in _question_attributes and 
                            option_id in _question_attributes['options'] and 
                            '_linked_section_index' in _question_attributes['options'][option_id]):
                            target_index = _question_attributes['options'][option_id]['_linked_section_index']
                            if 0 <= target_index < len(all_sections):
                                option.linked_section = all_sections[target_index]
                                option.save()
            
            # Clear the global variable for next use
            _question_attributes.clear()
        
        # Re-fetch the survey from the database to ensure all related fields are up to date
        return Survey.objects.prefetch_related(
            'sections__questions__options',
            'sections__questions__depends_on_question'
        ).select_related(
        ).get(pk=instance.pk) 