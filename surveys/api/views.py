from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from ..models import Survey, Section, Question, QuestionOption
from .serializers import (
    SurveySerializer, SectionSerializer, 
    QuestionSerializer, QuestionOptionSerializer,
    SurveyCreateSerializer, SurveyUpdateSerializer
)
from voter.contrib.audit import AuditLogMixin, SoftDeleteAuditMixin, MultiModelAuditMixin, log_consolidated_multi_model_operation
from voter.contrib.pagination import PaginatedViewSetMixin
from assignments.models import SurveyAssignment
from auditlog.models import LogEntry
from voter.users.permissions import (
    IsAdmin, IsCompanyAdmin, IsAdminOrCompanyAdmin, IsSurveyor, 
    IsVendorSurveyor, IsQCReviewer, CanManageSurveys
)


class SurveyViewSet(PaginatedViewSetMixin, MultiModelAuditMixin, viewsets.ModelViewSet):
    queryset = Survey.objects.filter(is_deleted=False)
    permission_classes = [IsAuthenticated]
    search_fields = ['title', 'description', 'status']
    filterset_fields = ['status', 'created_by', 'created_at']
    date_filter_field = 'created_at'
    
    def get_permissions(self):
        """Return appropriate permissions based on action"""
        if self.action in ['create', 'update', 'partial_update', 'destroy', 'create_complete_survey', 'update_complete_survey']:
            return [IsAuthenticated(), IsAdminOrCompanyAdmin(), CanManageSurveys()]
        return [IsAuthenticated()]
    
    def get_queryset(self):
        """Filter queryset based on user role and permissions"""
        queryset = Survey.objects.filter(is_deleted=False)
        
        # Admin can see all surveys
        if self.request.user.is_admin():
            return queryset
        
        # Company Admin can see surveys from their company
        if self.request.user.is_company_admin():
            return queryset.filter(created_by__company=self.request.user.company)
        
        # Surveyor and Vendor Surveyor can only see surveys assigned to them
        if self.request.user.is_surveyor() or self.request.user.is_vendor_surveyor():
            # Get surveys assigned to this user
            assigned_surveys = SurveyAssignment.objects.filter(
                assignee=self.request.user,
                is_deleted=False
            ).values_list('survey_id', flat=True)
            return queryset.filter(id__in=assigned_surveys)
        
        # QC Reviewer can see surveys from their company
        if self.request.user.is_qc_reviewer():
            return queryset.filter(created_by__company=self.request.user.company)
        
        # Default: return empty queryset for unknown roles
        return queryset.none()
    
    def get_serializer_class(self):
        if self.action == 'create_complete_survey':
            return SurveyCreateSerializer
        elif self.action == 'update_complete_survey':
            return SurveyUpdateSerializer
        return SurveySerializer
    
    def get_serializer_context(self):
        context = super().get_serializer_context()
        context['request'] = self.request
        return context
    
    def perform_create(self, serializer):
        """Override to log survey creation with consolidated audit logging."""
        # Start consolidated operation to prevent individual audit logging
        self._start_consolidated_operation()
        
        survey = serializer.save(created_by=self.request.user)
        
        # Get all related models for audit logging
        related_instances = []
        
        # Collect sections
        for section in survey.sections.all():
            related_instances.append(section)
            
            # Collect questions in each section
            for question in section.questions.all():
                related_instances.append(question)
                
                # Collect options for each question
                for option in question.options.all():
                    related_instances.append(option)
        
        # Create consolidated audit log if there are related instances
        if related_instances:
            self._create_consolidated_audit_log(
                action=LogEntry.Action.CREATE,
                primary_instance=survey,
                related_instances=related_instances,
                operation_type='create',
                description=f"Complete survey creation with {len(related_instances)} related models"
            )
        
        # End consolidated operation
        self._end_consolidated_operation()
        
        return survey
    
    @action(detail=False, methods=['post'], url_path='create-complete')
    def create_complete_survey(self, request):
        """
        Create a complete survey with sections, questions, and options in a single request
        Enhanced with consolidated audit logging for multi-model operations
        """
        try:
            # Start consolidated operation to prevent individual audit logging
            self._start_consolidated_operation()
            
            serializer = self.get_serializer(data=request.data)
            if serializer.is_valid():
                survey = serializer.save(created_by=request.user)
                
                # Get all related models for audit logging
                related_instances = []
                
                # Collect sections
                for section in survey.sections.all():
                    related_instances.append(section)
                    
                    # Collect questions in each section
                    for question in section.questions.all():
                        related_instances.append(question)
                        
                        # Collect options for each question
                        for option in question.options.all():
                            related_instances.append(option)
                
                # Create consolidated audit log if there are related instances
                if related_instances:
                    log_consolidated_multi_model_operation(
                        user=request.user,
                        primary_instance=survey,
                        related_instances=related_instances,
                        operation_type='create',
                        description=f"Complete survey creation via API with {len(related_instances)} related models",
                        request=request,
                        operation_context='survey_creation_complete_api'
                    )
                
                # End consolidated operation
                self._end_consolidated_operation()
                
                # Return the created survey with all nested data
                response_serializer = SurveySerializer(survey, context={'request': request})
                return Response(response_serializer.data, status=status.HTTP_201_CREATED)
            
            # End consolidated operation on error
            self._end_consolidated_operation()
            
            # Handle serializer errors safely
            try:
                errors = serializer.errors
                return Response(errors, status=status.HTTP_400_BAD_REQUEST)
            except Exception as e:
                # Fallback error response if serializer.errors fails
                return Response(
                    {'error': 'Invalid data provided', 'detail': str(e)},
                    status=status.HTTP_400_BAD_REQUEST
                )
        except Exception as e:
            # End consolidated operation on exception
            self._end_consolidated_operation()
            
            return Response(
                {'error': 'Failed to create survey', 'detail': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )
    
    @action(detail=True, methods=['put', 'patch'], url_path='update-complete')
    def update_complete_survey(self, request, pk=None):
        """
        Update a complete survey with sections, questions, and options in a single request
        Uses the same structure as GET response - just modify the returned object and send it back
        
        Supports:
        - Updating existing sections/questions/options (using their IDs)
        - Adding new sections/questions/options (without IDs)
        - Deleting sections/questions/options (by removing them from the request)
        - File uploads for questions and options
        Enhanced with consolidated audit logging for multi-model operations
        """
        try:
            # Start consolidated operation to prevent individual audit logging
            self._start_consolidated_operation()
            
            survey = self.get_object()
            
            # Get primary instance before update
            survey_before = Survey.objects.get(pk=survey.pk)
            
            # Get all related models before update for audit logging
            related_instances_before = []
            for section in survey.sections.all():
                related_instances_before.append(section)
                for question in section.questions.all():
                    related_instances_before.append(question)
                    for option in question.options.all():
                        related_instances_before.append(option)
            
            serializer = self.get_serializer(survey, data=request.data, partial=True)
            if serializer.is_valid():
                updated_survey = serializer.save()
                
                # Get all related models after update for audit logging
                related_instances_after = []
                for section in updated_survey.sections.all():
                    related_instances_after.append(section)
                    for question in section.questions.all():
                        related_instances_after.append(question)
                        for option in question.options.all():
                            related_instances_after.append(option)
                
                # Detect changes for UPDATE operation
                changes = self._detect_multi_model_changes(
                    survey_before, 
                    updated_survey,
                    related_instances_before, 
                    related_instances_after
                )
                
                # Create consolidated audit log
                log_consolidated_multi_model_operation(
                    user=request.user,
                    primary_instance=updated_survey,
                    related_instances=related_instances_after,
                    operation_type='update',
                    description=f"Complete survey update via API with {len(related_instances_after)} related models",
                    request=request,
                    operation_context='survey_update_complete_api',
                    changes=changes
                )
                
                # End consolidated operation
                self._end_consolidated_operation()
                
                # Return the updated survey with all nested data
                response_serializer = SurveySerializer(updated_survey, context={'request': request})
                return Response(response_serializer.data, status=status.HTTP_200_OK)
            
            # End consolidated operation on error
            self._end_consolidated_operation()
            
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            # End consolidated operation on exception
            self._end_consolidated_operation()
            
            return Response(
                {'error': 'Failed to update survey', 'detail': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )
    
    @action(detail=False, methods=['get'], url_path='summary')
    def survey_summary(self, request):
        """
        Get summary statistics for all surveys including user counts
        """
        from assignments.models import SurveyAssignment
        from django.utils import timezone
        from django.db.models import Count, Q
        
        queryset = self.get_queryset()
        
        # Get summary data for all surveys
        summary_data = []
        for survey in queryset:
            # Get total users who have collected samples for this survey
            total_users = survey.sample_set.filter(
                user__isnull=False
            ).values('user').distinct().count()
            
            # Get users who collected samples today for this survey
            today_users = survey.sample_set.filter(
                user__isnull=False,
                timestamp__date=timezone.now().date()
            ).values('user').distinct().count()
            
            # Get sample counts
            total_samples = survey.sample_set.count()
            today_samples = survey.sample_set.filter(
                timestamp__date=timezone.now().date()
            ).count()
            
            summary_data.append({
                'id': survey.id,
                'title': survey.title,
                'status': survey.status,
                'total_users_count': total_users,
                'today_users_count': today_users,
                'total_samples_count': total_samples,
                'today_samples_count': today_samples,
                'created_at': survey.created_at,
                'created_by_name': survey.created_by.name if survey.created_by else None
            })
        
        return Response({
            'count': len(summary_data),
            'results': summary_data
        })


class SectionViewSet(PaginatedViewSetMixin, AuditLogMixin, viewsets.ModelViewSet):
    queryset = Section.objects.all()
    serializer_class = SectionSerializer
    permission_classes = [IsAuthenticated]
    search_fields = ['title', 'survey__title']
    filterset_fields = ['survey', 'order']
    ordering_fields = ['order', 'title', 'created_at']
    
    def get_permissions(self):
        """Return appropriate permissions based on action"""
        if self.action in ['create', 'update', 'partial_update', 'destroy']:
            return [IsAuthenticated(), IsAdminOrCompanyAdmin(), CanManageSurveys()]
        return [IsAuthenticated()]
    
    def get_queryset(self):
        """Filter queryset based on user role and survey permissions"""
        queryset = Section.objects.all()
        
        # Admin can see all sections
        if self.request.user.is_admin():
            return queryset
        
        # Company Admin can see sections from surveys in their company
        if self.request.user.is_company_admin():
            return queryset.filter(survey__created_by__company=self.request.user.company)
        
        # Surveyor and Vendor Surveyor can only see sections from surveys assigned to them
        if self.request.user.is_surveyor() or self.request.user.is_vendor_surveyor():
            from assignments.models import SurveyAssignment
            assigned_surveys = SurveyAssignment.objects.filter(
                assignee=self.request.user,
                is_deleted=False
            ).values_list('survey_id', flat=True)
            return queryset.filter(survey_id__in=assigned_surveys)
        
        # QC Reviewer can see sections from surveys in their company
        if self.request.user.is_qc_reviewer():
            return queryset.filter(survey__created_by__company=self.request.user.company)
        
        # Default: return empty queryset for unknown roles
        return queryset.none()
    
    def get_serializer_context(self):
        context = super().get_serializer_context()
        context['request'] = self.request
        return context


class QuestionViewSet(PaginatedViewSetMixin, AuditLogMixin, viewsets.ModelViewSet):
    queryset = Question.objects.all()
    serializer_class = QuestionSerializer
    permission_classes = [IsAuthenticated]
    search_fields = ['question_text', 'section__title', 'type']
    filterset_fields = ['section', 'type', 'is_mandatory']
    ordering_fields = ['order', 'question_text', 'created_at']
    
    def get_permissions(self):
        """Return appropriate permissions based on action"""
        if self.action in ['create', 'update', 'partial_update', 'destroy']:
            return [IsAuthenticated(), IsAdminOrCompanyAdmin(), CanManageSurveys()]
        return [IsAuthenticated()]
    
    def get_queryset(self):
        """Filter queryset based on user role and survey permissions"""
        queryset = Question.objects.all()
        
        # Admin can see all questions
        if self.request.user.is_admin():
            return queryset
        
        # Company Admin can see questions from surveys in their company
        if self.request.user.is_company_admin():
            return queryset.filter(section__survey__created_by__company=self.request.user.company)
        
        # Surveyor and Vendor Surveyor can only see questions from surveys assigned to them
        if self.request.user.is_surveyor() or self.request.user.is_vendor_surveyor():
            from assignments.models import SurveyAssignment
            assigned_surveys = SurveyAssignment.objects.filter(
                assignee=self.request.user,
                is_deleted=False
            ).values_list('survey_id', flat=True)
            return queryset.filter(section__survey_id__in=assigned_surveys)
        
        # QC Reviewer can see questions from surveys in their company
        if self.request.user.is_qc_reviewer():
            return queryset.filter(section__survey__created_by__company=self.request.user.company)
        
        # Default: return empty queryset for unknown roles
        return queryset.none()
    
    def get_serializer_context(self):
        context = super().get_serializer_context()
        context['request'] = self.request
        return context


class QuestionOptionViewSet(PaginatedViewSetMixin, AuditLogMixin, viewsets.ModelViewSet):
    queryset = QuestionOption.objects.all()
    serializer_class = QuestionOptionSerializer
    permission_classes = [IsAuthenticated]
    search_fields = ['option_text', 'question__question_text']
    filterset_fields = ['question', 'order']
    ordering_fields = ['order', 'option_text', 'created_at']
    
    def get_permissions(self):
        """Return appropriate permissions based on action"""
        if self.action in ['create', 'update', 'partial_update', 'destroy']:
            return [IsAuthenticated(), IsAdminOrCompanyAdmin(), CanManageSurveys()]
        return [IsAuthenticated()]
    
    def get_queryset(self):
        """Filter queryset based on user role and survey permissions"""
        queryset = QuestionOption.objects.all()
        
        # Admin can see all question options
        if self.request.user.is_admin():
            return queryset
        
        # Company Admin can see question options from surveys in their company
        if self.request.user.is_company_admin():
            return queryset.filter(question__section__survey__created_by__company=self.request.user.company)
        
        # Surveyor and Vendor Surveyor can only see question options from surveys assigned to them
        if self.request.user.is_surveyor() or self.request.user.is_vendor_surveyor():
            from assignments.models import SurveyAssignment
            assigned_surveys = SurveyAssignment.objects.filter(
                assignee=self.request.user,
                is_deleted=False
            ).values_list('survey_id', flat=True)
            return queryset.filter(question__section__survey_id__in=assigned_surveys)
        
        # QC Reviewer can see question options from surveys in their company
        if self.request.user.is_qc_reviewer():
            return queryset.filter(question__section__survey__created_by__company=self.request.user.company)
        
        # Default: return empty queryset for unknown roles
        return queryset.none()
    
    def get_serializer_context(self):
        context = super().get_serializer_context()
        context['request'] = self.request
        return context 