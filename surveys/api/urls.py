from django.urls import path, include
from rest_framework.routers import DefaultRouter
from .views import SurveyViewSet, SectionViewSet, QuestionViewSet, QuestionOptionViewSet

router = DefaultRouter()
router.register(r'surveys', SurveyViewSet)
router.register(r'sections', SectionViewSet)
router.register(r'questions', QuestionViewSet)
router.register(r'question-options', QuestionOptionViewSet)

urlpatterns = [
    path('', include(router.urls)),
] 