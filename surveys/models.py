import uuid
from django.db import models
from django.db.models import <PERSON><PERSON><PERSON><PERSON>, TextField, JSONField, BooleanField, ForeignKey, CASCADE, SET_NULL, PositiveIntegerField, FileField
from django.utils.translation import gettext_lazy as _
from django.contrib.auth import get_user_model
from auditlog.registry import auditlog
from config.storage_backends import OptionImageStorage, QuestionStorage

User = get_user_model()


class Survey(models.Model):
    """
    Survey model for managing survey forms
    """
    STATUS_CHOICES = [
        ("Draft", "Draft"),
        ("Active", "Active"),
        ("Closed", "Closed")
    ]
    
    title = CharField(_("Survey Title"), max_length=200)
    description = TextField(_("Description"), blank=True, null=True)
    config_json = JSONField(_("Configuration JSON"), null=True, blank=True)
    status = CharField(
        _("Status"),
        max_length=20,
        choices=STATUS_CHOICES,
        default="Draft"
    )
    created_by = Foreign<PERSON><PERSON>(User, on_delete=CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    is_deleted = BooleanField(default=False)
    
    def __str__(self):
        return f"{self.title} ({self.status})"
    
    class Meta:
        verbose_name = _("Survey")
        verbose_name_plural = _("Surveys")


class Section(models.Model):
    """
    Section model for organizing questions within surveys
    """
    survey = ForeignKey(Survey, on_delete=CASCADE, related_name='sections')
    title = CharField(_("Section Title"), max_length=255)
    order = PositiveIntegerField(_("Order"))
    next_section_default = ForeignKey(
        'self',
        null=True,
        blank=True,
        on_delete=SET_NULL,
        related_name='previous_sections'
    )
    
    class Meta:
        ordering = ['order']
        verbose_name = _("Section")
        verbose_name_plural = _("Sections")
    
    def __str__(self):
        return f"{self.survey.title} - {self.title}"


class Question(models.Model):
    """
    Question model for survey questions
    """
    QUESTION_TYPES = [
        ("Text", "Text"),
        ("MCQ", "Multiple Choice"),
        ("Checkbox", "Checkbox"),
        ("Boolean", "Yes/No"),
        ("Audio", "Audio"),
        ("ImageCapture", "Image Capture"),
    ]
    
    section = ForeignKey(Section, on_delete=CASCADE, related_name='questions')
    question_text = TextField(_("Question Text"))
    type = CharField(
        _("Question Type"),
        max_length=20,
        choices=QUESTION_TYPES
    )
    file = FileField(storage=QuestionStorage(), blank=True, null=True)
    is_mandatory = BooleanField(_("Is Mandatory"), default=False)
    depends_on_question = ForeignKey(
        'self',
        null=True,
        blank=True,
        on_delete=SET_NULL,
        related_name='dependent_questions'
    )
    condition_value = TextField(_("Condition Value"), null=True, blank=True)
    next_section_on_answer = ForeignKey(
        Section,
        null=True,
        blank=True,
        on_delete=SET_NULL,
        related_name='conditional_questions'
    )
    order = PositiveIntegerField(_("Order"), default=0)
    
    class Meta:
        ordering = ['order']
        verbose_name = _("Question")
        verbose_name_plural = _("Questions")
    
    def __str__(self):
        return f"{self.section.title} - {self.question_text[:50]}"


class QuestionOption(models.Model):
    """
    QuestionOption model for MCQ and Checkbox question options
    """
    question = ForeignKey(Question, on_delete=CASCADE, related_name='options')
    option_text = CharField(_("Option Text"), max_length=255)
    image = FileField(storage=OptionImageStorage(), blank=True, null=True)
    linked_section = ForeignKey(
        Section,
        null=True,
        blank=True,
        on_delete=SET_NULL,
        related_name='linked_options'
    )
    order = PositiveIntegerField(_("Order"), default=0)
    
    class Meta:
        ordering = ['order']
        verbose_name = _("Question Option")
        verbose_name_plural = _("Question Options")
    
    def __str__(self):
        return f"{self.question.question_text[:30]} - {self.option_text}"


# Register models for audit logging
# auditlog.register(Survey)
# auditlog.register(Section)
# auditlog.register(Question)
# auditlog.register(QuestionOption)
