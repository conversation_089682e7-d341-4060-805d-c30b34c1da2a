#!/usr/bin/env python
"""
Test script for creating a complete survey with sections, questions, and images
using the new /api/surveys/create-complete/ endpoint
"""

import os
import sys
import django
import requests
import json
from pathlib import Path

# Add the project directory to Python path
project_dir = Path(__file__).parent
sys.path.insert(0, str(project_dir))

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.local')
django.setup()

from django.core.files.uploadedfile import SimpleUploadedFile
from django.contrib.auth import get_user_model
from rest_framework.test import APIClient
from rest_framework.authtoken.models import Token

User = get_user_model()


def create_test_images():
    """Create test image files for the survey"""
    images = {}
    
    # Create test image content (fake JPEG data)
    image_content = b'\xff\xd8\xff\xe0\x00\x10JFIF\x00\x01\x01\x01\x00H\x00H\x00\x00\xff\xdb\x00C\x00\x08\x06\x06\x07\x06\x05\x08\x07\x07\x07\t\t\x08\n\x0c\x14\r\x0c\x0b\x0b\x0c\x19\x12\x13\x0f\x14\x1d\x1a\x1f\x1e\x1d\x1a\x1c\x1c $.\' ",#\x1c\x1c(7),01444\x1f\'9=82<.342\xff\xc0\x00\x11\x08\x00\x01\x00\x01\x01\x01\x11\x00\x02\x11\x01\x03\x11\x01\xff\xc4\x00\x14\x00\x01\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x08\xff\xc4\x00\x14\x10\x01\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\xff\xda\x00\x0c\x03\x01\x00\x02\x11\x03\x11\x00\x3f\x00\xaa\xff\xd9'
    
    # Create different test images
    test_images = [
        'pizza_margherita.jpg',
        'burger_classic.jpg', 
        'pasta_carbonara.jpg',
        'salad_caesar.jpg',
        'steak_grilled.jpg',
        'sushi_rolls.jpg',
        'ice_cream_vanilla.jpg',
        'chocolate_cake.jpg'
    ]
    
    for image_name in test_images:
        images[image_name] = SimpleUploadedFile(
            name=image_name,
            content=image_content,
            content_type='image/jpeg'
        )
    
    return images


def create_test_user():
    """Create a test user for authentication"""
    user, created = User.objects.get_or_create(
        username='test_admin',
        defaults={
            'email': '<EMAIL>',
            'name': 'Test Admin',
            'role': 'Admin',
            'is_staff': True,
            'is_superuser': True
        }
    )
    
    if created:
        user.set_password('testpass123')
        user.save()
        print(f"Created test user: {user.username}")
    else:
        print(f"Using existing test user: {user.username}")
    
    return user


def get_auth_token(user):
    """Get authentication token for the user"""
    token, created = Token.objects.get_or_create(user=user)
    return token.key


def create_survey_with_json_api():
    """Create a complete survey using JSON API (without images)"""
    
    # Create test user and get token
    user = create_test_user()
    token = get_auth_token(user)
    
    # Setup API client
    client = APIClient()
    client.credentials(HTTP_AUTHORIZATION=f'Token {token}')
    
    # Prepare survey data (JSON format - no binary images)
    survey_data = {
        'title': 'Restaurant Menu Preference Survey (JSON)',
        'description': 'Help us understand your food preferences and design the perfect menu for our new restaurant',
        'status': 'Draft',
        'config_json': {
            'theme': 'food',
            'allow_anonymous': False,
            'estimated_time': '10 minutes'
        },
        'sections': [
            {
                'title': 'Demographics',
                'order': 1,
                'questions': [
                    {
                        'question_text': 'What is your age group?',
                        'type': 'MCQ',
                        'is_mandatory': True,
                        'order': 1,
                        'options': [
                            {'option_text': '18-25 years', 'order': 1},
                            {'option_text': '26-35 years', 'order': 2},
                            {'option_text': '36-45 years', 'order': 3},
                            {'option_text': '46-55 years', 'order': 4},
                            {'option_text': '56+ years', 'order': 5}
                        ]
                    },
                    {
                        'question_text': 'What are your dietary preferences?',
                        'type': 'Checkbox',
                        'is_mandatory': False,
                        'order': 2,
                        'options': [
                            {'option_text': 'Vegetarian', 'order': 1},
                            {'option_text': 'Vegan', 'order': 2},
                            {'option_text': 'Gluten-Free', 'order': 3},
                            {'option_text': 'Dairy-Free', 'order': 4},
                            {'option_text': 'No restrictions', 'order': 5}
                        ]
                    }
                ]
            },
            {
                'title': 'Appetizer Selection',
                'order': 2,
                'questions': [
                    {
                        'question_text': 'Which appetizer would you prefer to see on our menu?',
                        'type': 'MCQ',
                        'is_mandatory': True,
                        'order': 1,
                        'options': [
                            {'option_text': 'Margherita Pizza', 'order': 1},
                            {'option_text': 'Classic Burger', 'order': 2},
                            {'option_text': 'Caesar Salad', 'order': 3}
                        ]
                    },
                    {
                        'question_text': 'What price range do you expect for appetizers?',
                        'type': 'MCQ',
                        'is_mandatory': True,
                        'order': 2,
                        'options': [
                            {'option_text': '$5-8', 'order': 1},
                            {'option_text': '$8-12', 'order': 2},
                            {'option_text': '$12-16', 'order': 3},
                            {'option_text': '$16+', 'order': 4}
                        ]
                    }
                ]
            },
            {
                'title': 'Main Course Selection',
                'order': 3,
                'questions': [
                    {
                        'question_text': 'Which main course appeals to you most?',
                        'type': 'MCQ',
                        'is_mandatory': True,
                        'order': 1,
                        'options': [
                            {'option_text': 'Pasta Carbonara', 'order': 1},
                            {'option_text': 'Grilled Steak', 'order': 2},
                            {'option_text': 'Sushi Rolls', 'order': 3}
                        ]
                    },
                    {
                        'question_text': 'How spicy do you like your food?',
                        'type': 'MCQ',
                        'is_mandatory': True,
                        'order': 2,
                        'options': [
                            {'option_text': 'Mild - No spice', 'order': 1},
                            {'option_text': 'Medium - Light spice', 'order': 2},
                            {'option_text': 'Hot - Spicy', 'order': 3},
                            {'option_text': 'Extra Hot - Very spicy', 'order': 4}
                        ]
                    },
                    {
                        'question_text': 'Do you prefer indoor or outdoor dining?',
                        'type': 'Boolean',
                        'is_mandatory': True,
                        'order': 3
                    }
                ]
            },
            {
                'title': 'Dessert Selection',
                'order': 4,
                'questions': [
                    {
                        'question_text': 'Which dessert would you choose?',
                        'type': 'MCQ',
                        'is_mandatory': False,
                        'order': 1,
                        'options': [
                            {'option_text': 'Vanilla Ice Cream', 'order': 1},
                            {'option_text': 'Chocolate Cake', 'order': 2}
                        ]
                    },
                    {
                        'question_text': 'Any additional comments or suggestions for our menu?',
                        'type': 'Text',
                        'is_mandatory': False,
                        'order': 2
                    },
                    {
                        'question_text': 'Based on this menu selection, would you visit our restaurant?',
                        'type': 'Boolean',
                        'is_mandatory': True,
                        'order': 3
                    }
                ]
            }
        ]
    }
    
    print("🚀 Creating complete survey with JSON API...")
    print(f"📋 Survey Title: {survey_data['title']}")
    print(f"📝 Description: {survey_data['description']}")
    print(f"📊 Sections: {len(survey_data['sections'])}")
    
    # Count total questions and options
    total_questions = sum(len(section['questions']) for section in survey_data['sections'])
    total_options = sum(
        len(question.get('options', [])) 
        for section in survey_data['sections'] 
        for question in section['questions']
    )
    
    print(f"❓ Total Questions: {total_questions}")
    print(f"🔘 Total Options: {total_options}")
    print(f"🖼️  Total Images: 0 (JSON format)")
    print("-" * 50)
    
    # Make API request
    url = '/api/surveys/create-complete/'
    response = client.post(url, survey_data, format='json')
    
    if response.status_code == 201:
        print("✅ Survey created successfully!")
        print(f"📊 Response Status: {response.status_code}")
        
        # Parse response
        survey_response = response.json()
        survey_id = survey_response['id']
        
        print(f"🆔 Survey ID: {survey_id}")
        print(f"📋 Survey Title: {survey_response['title']}")
        print(f"👤 Created By: {survey_response['created_by_name']}")
        print(f"📅 Created At: {survey_response['created_at']}")
        print(f"📊 Sections Created: {len(survey_response['sections'])}")
        
        # Show section details
        for i, section in enumerate(survey_response['sections'], 1):
            print(f"\n📑 Section {i}: {section['title']}")
            print(f"   📝 Questions: {len(section['questions'])}")
            
            for j, question in enumerate(section['questions'], 1):
                print(f"   ❓ Question {j}: {question['question_text'][:50]}...")
                print(f"      🏷️  Type: {question['type']}")
                print(f"      📋 Options: {len(question['options'])}")
        
        print("\n🎉 JSON survey creation test completed successfully!")
        return survey_response
        
    else:
        print("❌ Failed to create survey!")
        print(f"📊 Response Status: {response.status_code}")
        print(f"📝 Response: {response.text}")
        return None


def create_survey_with_multipart():
    """Create a survey using multipart form data with images"""
    
    # Create test user and get token
    user = create_test_user()
    token = get_auth_token(user)
    
    # Setup API client
    client = APIClient()
    client.credentials(HTTP_AUTHORIZATION=f'Token {token}')
    
    # Create test images
    test_images = create_test_images()
    
    print("🚀 Creating survey with multipart form data (with images)...")
    
    # Prepare the nested structure for sections/questions/options
    sections = [
        {
            'title': 'Food Preferences',
            'order': 1,
            'questions': [
                {
                    'question_text': 'What type of cuisine do you prefer?',
                    'type': 'MCQ',
                    'is_mandatory': True,
                    'order': 1,
                    'options': [
                        {'option_text': 'Italian', 'order': 1},
                        {'option_text': 'Mexican', 'order': 2},
                        {'option_text': 'Asian', 'order': 3},
                        {'option_text': 'American', 'order': 4},
                    ]
                },
                {
                    'question_text': 'Which dish looks most appealing to you?',
                    'type': 'MCQ',
                    'is_mandatory': True,
                    'order': 2,
                    'options': [
                        {'option_text': 'Pizza Margherita', 'order': 1},
                        {'option_text': 'Classic Burger', 'order': 2},
                        {'option_text': 'Pasta Carbonara', 'order': 3},
                    ]
                },
                {
                    'question_text': 'Do you like spicy food?',
                    'type': 'Boolean',
                    'is_mandatory': True,
                    'order': 3,
                },
                {
                    'question_text': 'Any additional comments?',
                    'type': 'Text',
                    'is_mandatory': False,
                    'order': 4,
                },
            ]
        }
    ]
    
    # Prepare multipart data
    data = {
        'title': 'Restaurant Menu Survey with Images',
        'description': 'Survey with food images to test image upload functionality',
        'status': 'Draft',
        'sections': json.dumps(sections),  # Pass as JSON string
    }
    
    # Attach images to the correct options (by index path)
    files = {
        'sections.0.questions.1.options.0.image': test_images['pizza_margherita.jpg'],
        'sections.0.questions.1.options.1.image': test_images['burger_classic.jpg'],
        'sections.0.questions.1.options.2.image': test_images['pasta_carbonara.jpg'],
    }
    
    # Combine files into data for DRF test client multipart handling
    data_with_files = data.copy()
    data_with_files.update(files)
    
    # Make API request
    url = '/api/surveys/create-complete/'
    response = client.post(url, data_with_files, format='multipart')
    
    if response.status_code == 201:
        print("✅ Multipart survey created successfully!")
        survey_response = response.json()
        print(f"🆔 Survey ID: {survey_response['id']}")
        print(f"📋 Survey Title: {survey_response['title']}")
        print(f"👤 Created By: {survey_response['created_by_name']}")
        print(f"📅 Created At: {survey_response['created_at']}")
        print(f"📊 Sections Created: {len(survey_response['sections'])}")
        
        # Show image details
        for section in survey_response['sections']:
            for question in section['questions']:
                image_options = [opt for opt in question['options'] if opt.get('image')]
                if image_options:
                    print(f"   🖼️  Question '{question['question_text'][:30]}...' has {len(image_options)} images")
        
        return survey_response
    else:
        print("❌ Failed to create multipart survey!")
        print(f"📊 Response Status: {response.status_code}")
        # Print the error details for debugging
        try:
            print(f"📝 Response: {response.data}")
        except Exception:
            print(f"📝 Raw Content: {response.content}")
        # Print the request data for debugging
        print("\n🔎 Debug: Sent multipart data keys:")
        for k in data.keys():
            print(f"   {k}")
        print("🔎 Debug: Sent files:")
        for k in files.keys():
            print(f"   {k}")
        return None


def main():
    """Main function to run the test"""
    print("🧪 Starting Survey Creation Test")
    print("=" * 60)
    
    try:
        # Test 1: JSON API (without images)
        print("\n📋 TEST 1: Creating survey with JSON API (no images)")
        print("-" * 50)
        result1 = create_survey_with_json_api()
        
        # Test 2: Multipart API (with images)
        print("\n📋 TEST 2: Creating survey with Multipart API (with images)")
        print("-" * 50)
        result2 = create_survey_with_multipart()
        
        # Summary
        print("\n" + "=" * 60)
        print("📊 TEST SUMMARY")
        print("=" * 60)
        
        if result1:
            print("✅ JSON API Test: PASSED")
        else:
            print("❌ JSON API Test: FAILED")
            
        if result2:
            print("✅ Multipart API Test: PASSED")
        else:
            print("❌ Multipart API Test: FAILED")
        
        if result1 and result2:
            print("\n🎉 All tests passed! Survey creation API is working correctly.")
            print("📋 Both JSON and multipart form data methods are functional.")
            print("🖼️  Image upload functionality is working.")
        elif result1:
            print("\n⚠️  JSON API works, but multipart API failed.")
            print("📋 Check image upload implementation.")
        elif result2:
            print("\n⚠️  Multipart API works, but JSON API failed.")
            print("📋 Check JSON serialization.")
        else:
            print("\n❌ Both tests failed. Please check the implementation.")
            
    except Exception as e:
        print(f"\n❌ Test failed with error: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main() 