# All packages from virtual environment (VS)
# Generated from: source VS && pip freeze
# 
# Core dependencies
# ------------------------------------------------------------------------------
alabaster==1.0.0
anyio==4.9.0
argon2-cffi==25.1.0
argon2-cffi-bindings==21.2.0
asgiref==3.8.1
asttokens==3.0.0
attrs==25.3.0
babel==2.17.0
certifi==2025.6.15
cffi==1.17.1
cfgv==3.4.0
charset-normalizer==3.4.2
click==8.2.1
colorama==0.4.6
coverage==7.9.1
crispy-bootstrap5==2025.6
cryptography==45.0.4
cssbeautifier==1.15.4
decorator==5.2.1
distlib==0.3.9
Django==5.1.11
django-allauth==65.9.0
django-anymail==13.0
django-auditlog==3.2.0
django-cors-headers==4.7.0
django-crispy-forms==2.4
django-debug-toolbar==5.2.0
django-environ==0.12.0
django-extensions==4.1
django-jsonfield==1.4.1
django-model-utils==5.0.0
django-redis==6.0.0
django-stubs==5.2.1
django-stubs-ext==5.2.1
django_coverage_plugin==3.1.1
djangorestframework==3.16.0
djangorestframework-stubs==3.16.0
djlint==1.36.4
docutils==0.21.2
drf-spectacular==0.28.0
drf-spectacular-sidecar==2025.6.1
EditorConfig==0.17.1
executing==2.2.0
factory_boy==3.3.2
Faker==37.4.0
fido2==2.0.0
filelock==3.18.0
gunicorn==23.0.0
h11==0.16.0
hiredis==3.2.1
identify==2.6.12
idna==3.10
imagesize==1.4.1
inflection==0.5.1
iniconfig==2.1.0
ipdb==0.13.13
ipython==9.3.0
ipython_pygments_lexers==1.1.1
jedi==0.19.2
Jinja2==3.1.6
jsbeautifier==1.15.4
json5==0.12.0
jsonschema==4.24.0
jsonschema-specifications==2025.4.1
MarkupSafe==3.0.2
matplotlib-inline==0.1.7
mypy==1.15.0
mypy_extensions==1.1.0
nodeenv==1.9.1
packaging==25.0
parso==0.8.4
pathspec==0.12.1
pexpect==4.9.0
pillow==11.2.1
platformdirs==4.3.8
pluggy==1.6.0
pre_commit==4.2.0
prompt_toolkit==3.0.51
psycopg==3.2.9
psycopg-c==3.2.9
ptyprocess==0.7.0
pure_eval==0.2.3
pycparser==2.22
Pygments==2.19.2
pytest==8.4.1
pytest-django==4.11.1
pytest-sugar==1.0.0
python-dateutil==2.9.0.post0
python-slugify==8.0.4
PyYAML==6.0.2
qrcode==8.2
redis==6.2.0
referencing==0.36.2
regex==2024.11.6
requests==2.32.4
roman-numerals-py==3.1.0
rpds-py==0.25.1
ruff==0.12.1
six==1.17.0
sniffio==1.3.1
snowballstemmer==3.0.1
Sphinx==8.2.3
sphinx-autobuild==2024.10.3
sphinxcontrib-applehelp==2.0.0
sphinxcontrib-devhelp==2.0.0
sphinxcontrib-htmlhelp==2.1.0
sphinxcontrib-jsmath==1.0.1
sphinxcontrib-qthelp==2.0.0
sphinxcontrib-serializinghtml==2.0.0
sqlparse==0.5.3
stack-data==0.6.3
starlette==0.47.1
termcolor==3.1.0
text-unidecode==1.3
tqdm==4.67.1
traitlets==5.14.3
types-PyYAML==6.0.12.20250516
types-requests==2.32.4.20250611
typing_extensions==4.14.0
tzdata==2025.2
uritemplate==4.2.0
urllib3==2.5.0
uvicorn==0.34.3
virtualenv==20.31.2
watchdog==6.0.0
watchfiles==1.1.0
wcwidth==0.2.13
websockets==15.0.1
Werkzeug==3.1.3
whitenoise==6.9.0
django-filter==25.1