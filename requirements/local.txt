-r production.txt

Werkzeug[watchdog]==3.1.3 # https://github.com/pallets/werkzeug
ipdb==0.13.13  # https://github.com/gotcha/ipdb
# psycopg[binary]==3.2.9  # https://github.com/psycopg/psycopg

# Development tools
# ------------------------------------------------------------------------------
ipython==9.3.0  # https://github.com/ipython/ipython
jedi==0.19.2  # https://github.com/davidhalter/jedi
prompt_toolkit==3.0.51  # https://github.com/prompt-toolkit/python-prompt-toolkit
Pygments==2.19.2  # https://github.com/pygments/pygments
traitlets==5.14.3  # https://github.com/ipython/traitlets
matplotlib-inline==0.1.7  # https://github.com/ipython/matplotlib-inline
stack-data==0.6.3  # https://github.com/alexmojaki/stack_data
pure_eval==0.2.3  # https://github.com/alexmojaki/pure_eval
asttokens==3.0.0  # https://github.com/gristlabs/asttokens
executing==2.2.0  # https://github.com/alexmojaki/executing
wcwidth==0.2.13  # https://github.com/jquast/wcwidth
ptyprocess==0.7.0  # https://github.com/pexpect/ptyprocess
pexpect==4.9.0  # https://github.com/pexpect/pexpect
decorator==5.2.1  # https://github.com/micheles/decorator
parso==0.8.4  # https://github.com/davidhalter/parso

# Testing
# ------------------------------------------------------------------------------
mypy==1.15.0  # https://github.com/python/mypy
django-stubs[compatible-mypy]==5.2.1  # https://github.com/typeddjango/django-stubs
pytest==8.4.1  # https://github.com/pytest-dev/pytest
pytest-sugar==1.0.0  # https://github.com/Teemu/pytest-sugar
djangorestframework-stubs==3.16.0  # https://github.com/typeddjango/djangorestframework-stubs

# Documentation
# ------------------------------------------------------------------------------
sphinx==8.2.3 # pyup: != 8.3.0  # https://github.com/sphinx-doc/sphinx
sphinx-autobuild==2024.10.3 # https://github.com/GaretJax/sphinx-autobuild

# Code quality
# ------------------------------------------------------------------------------
ruff==0.12.1  # https://github.com/astral-sh/ruff
coverage==7.9.1  # https://github.com/nedbat/coveragepy
djlint==1.36.4  # https://github.com/Riverside-Healthcare/djLint
pre-commit==4.2.0  # https://github.com/pre-commit/pre-commit

# Django
# ------------------------------------------------------------------------------
factory-boy==3.3.2  # https://github.com/FactoryBoy/factory_boy

django-debug-toolbar==5.2.0  # https://github.com/jazzband/django-debug-toolbar
django-extensions==4.1  # https://github.com/django-extensions/django-extensions
django-coverage-plugin==3.1.1  # https://github.com/nedbat/django_coverage_plugin
pytest-django==4.11.1  # https://github.com/pytest-dev/pytest-django
