import factory
from collections.abc import Sequence
from typing import Any

from factory import Faker, SubFactory
from factory.django import DjangoModelFactory

from companies.models import Company, Team, TeamUser
from voter.users.tests.factories import UserFactory


class CompanyFactory(DjangoModelFactory[Company]):
    name = Faker("company")
    code = Faker("bothify", text="COMP-####")
    type = Faker("random_element", elements=["Parent", "QC", "Vendor"])
    created_by = SubFactory(UserFactory, role="Admin")
    
    class Meta:
        model = Company
        django_get_or_create = ["code"]


class TeamFactory(DjangoModelFactory[Team]):
    name = Faker("word")
    company = SubFactory(CompanyFactory)
    type = Faker("random_element", elements=["Survey", "QC"])
    created_by = SubFactory(UserFactory, role="CompanyAdmin", company=factory.SelfAttribute('..company'))
    
    class Meta:
        model = Team


class TeamUserFactory(DjangoModelFactory[TeamUser]):
    team = SubFactory(TeamFactory)
    user = SubFactory(UserFactory, company=factory.SelfAttribute('..team.company'))
    
    class Meta:
        model = TeamUser 