import pytest
from django.urls import reverse
from rest_framework import status
from rest_framework.test import APIClient
from rest_framework.authtoken.models import Token

from companies.models import Company, Team, TeamUser
from companies.tests.factories import CompanyFactory, TeamFactory, TeamUserFactory
from voter.users.models import User
from voter.users.tests.factories import UserFactory


@pytest.fixture
def api_client():
    return APIClient()


@pytest.fixture
def admin_user(db):
    user = UserFactory(role="Admin")
    return user


@pytest.fixture
def company_admin_user(db):
    company = CompanyFactory()
    user = UserFactory(role="CompanyAdmin", company=company)
    return user


@pytest.fixture
def surveyor_user(db):
    company = CompanyFactory()
    user = UserFactory(role="Surveyor", company=company)
    return user


@pytest.fixture
def company(db, admin_user):
    return CompanyFactory(created_by=admin_user)


@pytest.fixture
def team(db, company, company_admin_user):
    return TeamFactory(company=company, created_by=company_admin_user)


class TestCompanyViewSet:
    """Test cases for CompanyViewSet"""
    
    def test_list_companies_admin(self, api_client, db, admin_user, company):
        """Test listing companies as admin"""
        token, _ = Token.objects.get_or_create(user=admin_user)
        api_client.credentials(HTTP_AUTHORIZATION=f'Token {token.key}')
        
        # Create another company
        CompanyFactory(created_by=admin_user)
        
        url = reverse('api:company-list')
        response = api_client.get(url)
        
        assert response.status_code == status.HTTP_200_OK
        assert len(response.data) >= 2
    
    def test_list_companies_company_admin(self, api_client, db, company_admin_user):
        """Test listing companies as company admin (should be forbidden)"""
        token, _ = Token.objects.get_or_create(user=company_admin_user)
        api_client.credentials(HTTP_AUTHORIZATION=f'Token {token.key}')
        
        url = reverse('api:company-list')
        response = api_client.get(url)
        
        assert response.status_code == status.HTTP_403_FORBIDDEN
    
    def test_list_companies_unauthorized(self, api_client, db):
        """Test listing companies without authentication"""
        url = reverse('api:company-list')
        response = api_client.get(url)
        
        assert response.status_code == status.HTTP_401_UNAUTHORIZED
    
    def test_retrieve_company_admin(self, api_client, db, admin_user, company):
        """Test retrieving a company as admin"""
        token, _ = Token.objects.get_or_create(user=admin_user)
        api_client.credentials(HTTP_AUTHORIZATION=f'Token {token.key}')
        
        url = reverse('api:company-detail', kwargs={'pk': company.id})
        response = api_client.get(url)
        
        assert response.status_code == status.HTTP_200_OK
        assert response.data['id'] == company.id
        assert response.data['name'] == company.name
        assert response.data['code'] == company.code
    
    def test_create_company_admin(self, api_client, db, admin_user):
        """Test creating a company as admin"""
        token, _ = Token.objects.get_or_create(user=admin_user)
        api_client.credentials(HTTP_AUTHORIZATION=f'Token {token.key}')
        
        url = reverse('api:company-list')
        data = {
            'name': 'Test Company',
            'code': 'TEST-001',
            'type': 'Parent'
        }
        
        response = api_client.post(url, data)
        
        assert response.status_code == status.HTTP_201_CREATED
        assert response.data['name'] == 'Test Company'
        assert response.data['code'] == 'TEST-001'
        assert response.data['type'] == 'Parent'
        
        # Check if company was created in database
        company = Company.objects.get(code='TEST-001')
        assert company.created_by == admin_user
    
    def test_create_company_invalid_data(self, api_client, db, admin_user):
        """Test creating a company with invalid data"""
        token, _ = Token.objects.get_or_create(user=admin_user)
        api_client.credentials(HTTP_AUTHORIZATION=f'Token {token.key}')
        
        url = reverse('api:company-list')
        data = {
            'name': '',  # Invalid empty name
            'code': 'TEST-001',
            'type': 'InvalidType'
        }
        
        response = api_client.post(url, data)
        
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert 'name' in response.data
        assert 'type' in response.data
    
    def test_create_company_duplicate_code(self, api_client, db, admin_user, company):
        """Test creating a company with duplicate code"""
        token, _ = Token.objects.get_or_create(user=admin_user)
        api_client.credentials(HTTP_AUTHORIZATION=f'Token {token.key}')
        
        url = reverse('api:company-list')
        data = {
            'name': 'Another Company',
            'code': company.code,  # Duplicate code
            'type': 'Parent'
        }
        
        response = api_client.post(url, data)
        
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert 'code' in response.data
    
    def test_update_company_admin(self, api_client, db, admin_user, company):
        """Test updating a company as admin"""
        token, _ = Token.objects.get_or_create(user=admin_user)
        api_client.credentials(HTTP_AUTHORIZATION=f'Token {token.key}')
        
        url = reverse('api:company-detail', kwargs={'pk': company.id})
        data = {
            'name': 'Updated Company Name',
            'type': 'QC'
        }
        
        response = api_client.patch(url, data)
        
        assert response.status_code == status.HTTP_200_OK
        assert response.data['name'] == 'Updated Company Name'
        assert response.data['type'] == 'QC'
        
        # Check if company was updated in database
        company.refresh_from_db()
        assert company.name == 'Updated Company Name'
        assert company.type == 'QC'
    
    def test_delete_company_admin(self, api_client, db, admin_user, company):
        """Test deleting a company as admin"""
        token, _ = Token.objects.get_or_create(user=admin_user)
        api_client.credentials(HTTP_AUTHORIZATION=f'Token {token.key}')
        
        url = reverse('api:company-detail', kwargs={'pk': company.id})
        response = api_client.delete(url)
        
        assert response.status_code == status.HTTP_204_NO_CONTENT
        
        # Check if company is soft deleted
        company.refresh_from_db()
        assert company.is_deleted is True
    
    def test_company_users_action(self, api_client, db, admin_user, company):
        """Test getting users in a company"""
        token, _ = Token.objects.get_or_create(user=admin_user)
        api_client.credentials(HTTP_AUTHORIZATION=f'Token {token.key}')
        
        # Create some users in the company
        UserFactory(company=company)
        UserFactory(company=company)
        
        url = reverse('api:company-users', kwargs={'pk': company.id})
        response = api_client.get(url)
        
        assert response.status_code == status.HTTP_200_OK
        assert len(response.data) >= 2  # At least 2 users created
    
    def test_company_teams_action(self, api_client, db, admin_user, company):
        """Test getting teams in a company"""
        token, _ = Token.objects.get_or_create(user=admin_user)
        api_client.credentials(HTTP_AUTHORIZATION=f'Token {token.key}')
        
        # Create some teams in the company
        TeamFactory(company=company, created_by=admin_user)
        TeamFactory(company=company, created_by=admin_user)
        
        url = reverse('api:company-teams', kwargs={'pk': company.id})
        response = api_client.get(url)
        
        assert response.status_code == status.HTTP_200_OK
        assert len(response.data) >= 2  # At least 2 teams created
    
    def test_company_audit_logs_action(self, api_client, db, admin_user, company):
        """Test getting audit logs for a company"""
        token, _ = Token.objects.get_or_create(user=admin_user)
        api_client.credentials(HTTP_AUTHORIZATION=f'Token {token.key}')
        
        # Create some audit logs by updating the company
        company.name = 'Updated Name'
        company.save()
        
        url = reverse('api:company-audit-logs', kwargs={'pk': company.id})
        response = api_client.get(url)
        
        assert response.status_code == status.HTTP_200_OK
        # Should have at least the update log
        assert len(response.data) >= 1


class TestTeamViewSet:
    """Test cases for TeamViewSet"""
    
    def test_list_teams_admin(self, api_client, db, admin_user, team):
        """Test listing teams as admin"""
        token, _ = Token.objects.get_or_create(user=admin_user)
        api_client.credentials(HTTP_AUTHORIZATION=f'Token {token.key}')
        
        # Create another team
        TeamFactory(created_by=admin_user)
        
        url = reverse('api:team-list')
        response = api_client.get(url)
        
        assert response.status_code == status.HTTP_200_OK
        assert len(response.data) >= 2
    
    def test_list_teams_company_admin(self, api_client, db, company_admin_user, team):
        """Test listing teams as company admin"""
        token, _ = Token.objects.get_or_create(user=company_admin_user)
        api_client.credentials(HTTP_AUTHORIZATION=f'Token {token.key}')
        
        url = reverse('api:team-list')
        response = api_client.get(url)
        
        assert response.status_code == status.HTTP_200_OK
        # Should only see teams from their company
        assert len(response.data) >= 1
    
    def test_list_teams_unauthorized(self, api_client, db):
        """Test listing teams without authentication"""
        url = reverse('api:team-list')
        response = api_client.get(url)
        
        assert response.status_code == status.HTTP_401_UNAUTHORIZED
    
    def test_retrieve_team_admin(self, api_client, db, admin_user, team):
        """Test retrieving a team as admin"""
        token, _ = Token.objects.get_or_create(user=admin_user)
        api_client.credentials(HTTP_AUTHORIZATION=f'Token {token.key}')
        
        url = reverse('api:team-detail', kwargs={'pk': team.id})
        response = api_client.get(url)
        
        assert response.status_code == status.HTTP_200_OK
        assert response.data['id'] == team.id
        assert response.data['name'] == team.name
        assert response.data['type'] == team.type
    
    def test_create_team_admin(self, api_client, db, admin_user, company):
        """Test creating a team as admin"""
        token, _ = Token.objects.get_or_create(user=admin_user)
        api_client.credentials(HTTP_AUTHORIZATION=f'Token {token.key}')
        
        url = reverse('api:team-list')
        data = {
            'name': 'Test Team',
            'type': 'Survey',
            'company': company.id
        }
        
        response = api_client.post(url, data)
        
        assert response.status_code == status.HTTP_201_CREATED
        assert response.data['name'] == 'Test Team'
        assert response.data['type'] == 'Survey'
        assert response.data['company'] == company.id
        
        # Check if team was created in database
        team = Team.objects.get(name='Test Team')
        assert team.created_by == admin_user
    
    def test_create_team_invalid_data(self, api_client, db, admin_user, company):
        """Test creating a team with invalid data"""
        token, _ = Token.objects.get_or_create(user=admin_user)
        api_client.credentials(HTTP_AUTHORIZATION=f'Token {token.key}')
        
        url = reverse('api:team-list')
        data = {
            'name': '',  # Invalid empty name
            'type': 'InvalidType',
            'company': company.id
        }
        
        response = api_client.post(url, data)
        
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert 'name' in response.data
        assert 'type' in response.data
    
    def test_update_team_admin(self, api_client, db, admin_user, team):
        """Test updating a team as admin"""
        token, _ = Token.objects.get_or_create(user=admin_user)
        api_client.credentials(HTTP_AUTHORIZATION=f'Token {token.key}')
        
        url = reverse('api:team-detail', kwargs={'pk': team.id})
        data = {
            'name': 'Updated Team Name',
            'type': 'QC'
        }
        
        response = api_client.patch(url, data)
        
        assert response.status_code == status.HTTP_200_OK
        assert response.data['name'] == 'Updated Team Name'
        assert response.data['type'] == 'QC'
        
        # Check if team was updated in database
        team.refresh_from_db()
        assert team.name == 'Updated Team Name'
        assert team.type == 'QC'
    
    def test_delete_team_admin(self, api_client, db, admin_user, team):
        """Test deleting a team as admin"""
        token, _ = Token.objects.get_or_create(user=admin_user)
        api_client.credentials(HTTP_AUTHORIZATION=f'Token {token.key}')
        
        url = reverse('api:team-detail', kwargs={'pk': team.id})
        response = api_client.delete(url)
        
        assert response.status_code == status.HTTP_204_NO_CONTENT
        
        # Check if team is soft deleted
        team.refresh_from_db()
        assert team.is_deleted is True
    
    def test_team_members_action(self, api_client, db, admin_user, team):
        """Test getting team members"""
        token, _ = Token.objects.get_or_create(user=admin_user)
        api_client.credentials(HTTP_AUTHORIZATION=f'Token {token.key}')
        
        # Create some team members
        TeamUserFactory(team=team)
        TeamUserFactory(team=team)
        
        url = reverse('api:team-members', kwargs={'pk': team.id})
        response = api_client.get(url)
        
        assert response.status_code == status.HTTP_200_OK
        assert len(response.data) >= 2  # At least 2 members
    
    def test_team_add_member_action(self, api_client, db, admin_user, team):
        """Test adding a member to a team"""
        token, _ = Token.objects.get_or_create(user=admin_user)
        api_client.credentials(HTTP_AUTHORIZATION=f'Token {token.key}')
        
        # Create a user to add to the team
        user = UserFactory(company=team.company)
        
        url = reverse('api:team-add-member', kwargs={'pk': team.id})
        data = {'user_id': user.id}
        
        response = api_client.post(url, data)
        
        assert response.status_code == status.HTTP_200_OK
        assert response.data['success'] is True
        
        # Check if team user was created
        assert TeamUser.objects.filter(team=team, user=user).exists()
    
    def test_team_add_member_already_exists(self, api_client, db, admin_user, team):
        """Test adding a member who is already in the team"""
        token, _ = Token.objects.get_or_create(user=admin_user)
        api_client.credentials(HTTP_AUTHORIZATION=f'Token {token.key}')
        
        # Create a team user
        team_user = TeamUserFactory(team=team)
        
        url = reverse('api:team-add-member', kwargs={'pk': team.id})
        data = {'user_id': team_user.user.id}
        
        response = api_client.post(url, data)
        
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert response.data['success'] is False
    
    def test_team_remove_member_action(self, api_client, db, admin_user, team):
        """Test removing a member from a team"""
        token, _ = Token.objects.get_or_create(user=admin_user)
        api_client.credentials(HTTP_AUTHORIZATION=f'Token {token.key}')
        
        # Create a team user
        team_user = TeamUserFactory(team=team)
        
        url = reverse('api:team-remove-member', kwargs={'pk': team.id})
        data = {'user_id': team_user.user.id}
        
        response = api_client.delete(url, data)
        
        assert response.status_code == status.HTTP_200_OK
        assert response.data['success'] is True
        
        # Check if team user was deleted
        assert not TeamUser.objects.filter(team=team, user=team_user.user).exists()
    
    def test_team_audit_logs_action(self, api_client, db, admin_user, team):
        """Test getting audit logs for a team"""
        token, _ = Token.objects.get_or_create(user=admin_user)
        api_client.credentials(HTTP_AUTHORIZATION=f'Token {token.key}')
        
        # Create some audit logs by updating the team
        team.name = 'Updated Team Name'
        team.save()
        
        url = reverse('api:team-audit-logs', kwargs={'pk': team.id})
        response = api_client.get(url)
        
        assert response.status_code == status.HTTP_200_OK
        # Should have at least the update log
        assert len(response.data) >= 1


class TestTeamUserViewSet:
    """Test cases for TeamUserViewSet"""
    
    def test_list_team_users_admin(self, api_client, db, admin_user):
        """Test listing team users as admin"""
        token, _ = Token.objects.get_or_create(user=admin_user)
        api_client.credentials(HTTP_AUTHORIZATION=f'Token {token.key}')
        
        # Create some team users
        TeamUserFactory()
        TeamUserFactory()
        
        url = reverse('api:teamuser-list')
        response = api_client.get(url)
        
        assert response.status_code == status.HTTP_200_OK
        assert len(response.data) >= 2
    
    def test_list_team_users_unauthorized(self, api_client, db):
        """Test listing team users without authentication"""
        url = reverse('api:teamuser-list')
        response = api_client.get(url)
        
        assert response.status_code == status.HTTP_401_UNAUTHORIZED
    
    def test_retrieve_team_user_admin(self, api_client, db, admin_user):
        """Test retrieving a team user as admin"""
        token, _ = Token.objects.get_or_create(user=admin_user)
        api_client.credentials(HTTP_AUTHORIZATION=f'Token {token.key}')
        
        team_user = TeamUserFactory()
        
        url = reverse('api:teamuser-detail', kwargs={'pk': team_user.id})
        response = api_client.get(url)
        
        assert response.status_code == status.HTTP_200_OK
        assert response.data['id'] == team_user.id
        assert response.data['team'] == team_user.team.id
        assert response.data['user'] == team_user.user.id
    
    def test_create_team_user_admin(self, api_client, db, admin_user, team):
        """Test creating a team user as admin"""
        token, _ = Token.objects.get_or_create(user=admin_user)
        api_client.credentials(HTTP_AUTHORIZATION=f'Token {token.key}')
        
        user = UserFactory(company=team.company)
        
        url = reverse('api:teamuser-list')
        data = {
            'team': team.id,
            'user': user.id
        }
        
        response = api_client.post(url, data)
        
        assert response.status_code == status.HTTP_201_CREATED
        assert response.data['team'] == team.id
        assert response.data['user'] == user.id
        
        # Check if team user was created in database
        assert TeamUser.objects.filter(team=team, user=user).exists()
    
    def test_create_team_user_duplicate(self, api_client, db, admin_user):
        """Test creating a duplicate team user"""
        token, _ = Token.objects.get_or_create(user=admin_user)
        api_client.credentials(HTTP_AUTHORIZATION=f'Token {token.key}')
        
        team_user = TeamUserFactory()
        
        url = reverse('api:teamuser-list')
        data = {
            'team': team_user.team.id,
            'user': team_user.user.id
        }
        
        response = api_client.post(url, data)
        
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert 'non_field_errors' in response.data
    
    def test_delete_team_user_admin(self, api_client, db, admin_user):
        """Test deleting a team user as admin"""
        token, _ = Token.objects.get_or_create(user=admin_user)
        api_client.credentials(HTTP_AUTHORIZATION=f'Token {token.key}')
        
        team_user = TeamUserFactory()
        
        url = reverse('api:teamuser-detail', kwargs={'pk': team_user.id})
        response = api_client.delete(url)
        
        assert response.status_code == status.HTTP_204_NO_CONTENT
        
        # Check if team user was deleted
        assert not TeamUser.objects.filter(id=team_user.id).exists()


class TestAuditLogViewSet:
    """Test cases for AuditLogViewSet"""
    
    def test_list_audit_logs_admin(self, api_client, db, admin_user):
        """Test listing audit logs as admin"""
        token, _ = Token.objects.get_or_create(user=admin_user)
        api_client.credentials(HTTP_AUTHORIZATION=f'Token {token.key}')
        
        url = reverse('api:logentry-list')
        response = api_client.get(url)
        
        assert response.status_code == status.HTTP_200_OK
    
    def test_list_audit_logs_company_admin(self, api_client, db, company_admin_user):
        """Test listing audit logs as company admin"""
        token, _ = Token.objects.get_or_create(user=company_admin_user)
        api_client.credentials(HTTP_AUTHORIZATION=f'Token {token.key}')
        
        url = reverse('api:logentry-list')
        response = api_client.get(url)
        
        assert response.status_code == status.HTTP_200_OK
    
    def test_list_audit_logs_unauthorized(self, api_client, db):
        """Test listing audit logs without authentication"""
        url = reverse('api:logentry-list')
        response = api_client.get(url)
        
        assert response.status_code == status.HTTP_401_UNAUTHORIZED
    
    def test_retrieve_audit_log_admin(self, api_client, db, admin_user):
        """Test retrieving an audit log as admin"""
        token, _ = Token.objects.get_or_create(user=admin_user)
        api_client.credentials(HTTP_AUTHORIZATION=f'Token {token.key}')
        
        # Create an audit log
        company = CompanyFactory(created_by=admin_user)
        from auditlog.models import LogEntry
        log_entry = LogEntry.objects.filter(content_type__model='company').first()
        
        url = reverse('api:logentry-detail', kwargs={'pk': log_entry.id})
        response = api_client.get(url)
        
        assert response.status_code == status.HTTP_200_OK
    
    def test_audit_logs_summary_action(self, api_client, db, admin_user):
        """Test getting audit log summary"""
        token, _ = Token.objects.get_or_create(user=admin_user)
        api_client.credentials(HTTP_AUTHORIZATION=f'Token {token.key}')
        
        # Create some audit logs
        CompanyFactory(created_by=admin_user)
        TeamFactory(created_by=admin_user)
        
        url = reverse('api:logentry-summary')
        response = api_client.get(url)
        
        assert response.status_code == status.HTTP_200_OK
    
    def test_audit_logs_filter_by_content_type(self, api_client, db, admin_user):
        """Test filtering audit logs by content type"""
        token, _ = Token.objects.get_or_create(user=admin_user)
        api_client.credentials(HTTP_AUTHORIZATION=f'Token {token.key}')
        
        # Create some audit logs
        CompanyFactory(created_by=admin_user)
        TeamFactory(created_by=admin_user)
        
        url = reverse('api:logentry-list')
        response = api_client.get(url, {'content_type': 'company'})
        
        assert response.status_code == status.HTTP_200_OK
    
    def test_audit_logs_filter_by_action(self, api_client, db, admin_user):
        """Test filtering audit logs by action"""
        token, _ = Token.objects.get_or_create(user=admin_user)
        api_client.credentials(HTTP_AUTHORIZATION=f'Token {token.key}')
        
        # Create some audit logs
        CompanyFactory(created_by=admin_user)
        
        url = reverse('api:logentry-list')
        response = api_client.get(url, {'action': 'create'})
        
        assert response.status_code == status.HTTP_200_OK
    
    def test_audit_logs_filter_by_date_range(self, api_client, db, admin_user):
        """Test filtering audit logs by date range"""
        token, _ = Token.objects.get_or_create(user=admin_user)
        api_client.credentials(HTTP_AUTHORIZATION=f'Token {token.key}')
        
        # Create some audit logs
        CompanyFactory(created_by=admin_user)
        
        from django.utils import timezone
        today = timezone.now().date()
        
        url = reverse('api:logentry-list')
        response = api_client.get(url, {
            'start_date': today,
            'end_date': today
        })
        
        assert response.status_code == status.HTTP_200_OK 