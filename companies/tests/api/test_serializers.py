import pytest
from django.test import TestCase
from rest_framework.test import APITestCase

from companies.models import Company, Team, TeamUser
from companies.tests.factories import CompanyFactory, TeamFactory, TeamUserFactory
from companies.api.serializers import (
    CompanySerializer, TeamSerializer, TeamUserSerializer,
    CompanyCreateSerializer, TeamCreateSerializer, TeamUserCreateSerializer,
    AuditLogSerializer
)
from voter.users.tests.factories import UserFactory


class TestCompanySerializer(TestCase):
    """Test cases for CompanySerializer"""
    
    def setUp(self):
        self.admin_user = UserFactory(role="Admin")
        self.company = CompanyFactory(created_by=self.admin_user)
    
    def test_company_serializer_valid_data(self):
        """Test CompanySerializer with valid data"""
        serializer = CompanySerializer(self.company)
        data = serializer.data
        
        assert data['id'] == self.company.id
        assert data['name'] == self.company.name
        assert data['code'] == self.company.code
        assert data['type'] == self.company.type
        assert data['created_at'] is not None
        assert data['is_deleted'] == self.company.is_deleted
    
    def test_company_serializer_includes_created_by(self):
        """Test CompanySerializer includes created_by information"""
        serializer = CompanySerializer(self.company)
        data = serializer.data
        
        assert 'created_by' in data
        assert data['created_by']['id'] == self.admin_user.id
        assert data['created_by']['username'] == self.admin_user.username


class TestCompanyCreateSerializer(TestCase):
    """Test cases for CompanyCreateSerializer"""
    
    def setUp(self):
        self.admin_user = UserFactory(role="Admin")
    
    def test_company_create_serializer_valid_data(self):
        """Test CompanyCreateSerializer with valid data"""
        data = {
            'name': 'Test Company',
            'code': 'TEST-001',
            'type': 'Parent'
        }
        
        serializer = CompanyCreateSerializer(data=data)
        assert serializer.is_valid()
        
        company = serializer.save(created_by=self.admin_user)
        assert company.name == 'Test Company'
        assert company.code == 'TEST-001'
        assert company.type == 'Parent'
        assert company.created_by == self.admin_user
    
    def test_company_create_serializer_invalid_data(self):
        """Test CompanyCreateSerializer with invalid data"""
        data = {
            'name': '',  # Empty name
            'code': 'TEST-001',
            'type': 'InvalidType'
        }
        
        serializer = CompanyCreateSerializer(data=data)
        assert not serializer.is_valid()
        assert 'name' in serializer.errors
        assert 'type' in serializer.errors
    
    def test_company_create_serializer_duplicate_code(self):
        """Test CompanyCreateSerializer with duplicate code"""
        # Create a company first
        existing_company = CompanyFactory(code='TEST-001')
        
        data = {
            'name': 'Another Company',
            'code': 'TEST-001',  # Duplicate code
            'type': 'Parent'
        }
        
        serializer = CompanyCreateSerializer(data=data)
        assert not serializer.is_valid()
        assert 'code' in serializer.errors


class TestTeamSerializer(TestCase):
    """Test cases for TeamSerializer"""
    
    def setUp(self):
        self.admin_user = UserFactory(role="Admin")
        self.company = CompanyFactory(created_by=self.admin_user)
        self.team = TeamFactory(company=self.company, created_by=self.admin_user)
    
    def test_team_serializer_valid_data(self):
        """Test TeamSerializer with valid data"""
        serializer = TeamSerializer(self.team)
        data = serializer.data
        
        assert data['id'] == self.team.id
        assert data['name'] == self.team.name
        assert data['type'] == self.team.type
        assert data['company'] == self.company.id
        assert data['created_at'] is not None
        assert data['is_deleted'] == self.team.is_deleted
    
    def test_team_serializer_includes_company_info(self):
        """Test TeamSerializer includes company information"""
        serializer = TeamSerializer(self.team)
        data = serializer.data
        
        assert 'company' in data
        assert data['company'] == self.company.id
    
    def test_team_serializer_includes_created_by(self):
        """Test TeamSerializer includes created_by information"""
        serializer = TeamSerializer(self.team)
        data = serializer.data
        
        assert 'created_by' in data
        assert data['created_by']['id'] == self.admin_user.id
        assert data['created_by']['username'] == self.admin_user.username


class TestTeamCreateSerializer(TestCase):
    """Test cases for TeamCreateSerializer"""
    
    def setUp(self):
        self.admin_user = UserFactory(role="Admin")
        self.company = CompanyFactory(created_by=self.admin_user)
    
    def test_team_create_serializer_valid_data(self):
        """Test TeamCreateSerializer with valid data"""
        data = {
            'name': 'Test Team',
            'type': 'Survey',
            'company': self.company.id
        }
        
        serializer = TeamCreateSerializer(data=data)
        assert serializer.is_valid()
        
        team = serializer.save(created_by=self.admin_user)
        assert team.name == 'Test Team'
        assert team.type == 'Survey'
        assert team.company == self.company
        assert team.created_by == self.admin_user
    
    def test_team_create_serializer_invalid_data(self):
        """Test TeamCreateSerializer with invalid data"""
        data = {
            'name': '',  # Empty name
            'type': 'InvalidType',
            'company': self.company.id
        }
        
        serializer = TeamCreateSerializer(data=data)
        assert not serializer.is_valid()
        assert 'name' in serializer.errors
        assert 'type' in serializer.errors
    
    def test_team_create_serializer_invalid_company(self):
        """Test TeamCreateSerializer with invalid company"""
        data = {
            'name': 'Test Team',
            'type': 'Survey',
            'company': 99999  # Non-existent company
        }
        
        serializer = TeamCreateSerializer(data=data)
        assert not serializer.is_valid()
        assert 'company' in serializer.errors


class TestTeamUserSerializer(TestCase):
    """Test cases for TeamUserSerializer"""
    
    def setUp(self):
        self.admin_user = UserFactory(role="Admin")
        self.company = CompanyFactory(created_by=self.admin_user)
        self.team = TeamFactory(company=self.company, created_by=self.admin_user)
        self.user = UserFactory(company=self.company)
        self.team_user = TeamUserFactory(team=self.team, user=self.user)
    
    def test_team_user_serializer_valid_data(self):
        """Test TeamUserSerializer with valid data"""
        serializer = TeamUserSerializer(self.team_user)
        data = serializer.data
        
        assert data['id'] == self.team_user.id
        assert data['team'] == self.team.id
        assert data['user'] == self.user.id
        assert data['added_at'] is not None
    
    def test_team_user_serializer_includes_team_info(self):
        """Test TeamUserSerializer includes team information"""
        serializer = TeamUserSerializer(self.team_user)
        data = serializer.data
        
        assert 'team' in data
        assert data['team'] == self.team.id
    
    def test_team_user_serializer_includes_user_info(self):
        """Test TeamUserSerializer includes user information"""
        serializer = TeamUserSerializer(self.team_user)
        data = serializer.data
        
        assert 'user' in data
        assert data['user'] == self.user.id


class TestTeamUserCreateSerializer(TestCase):
    """Test cases for TeamUserCreateSerializer"""
    
    def setUp(self):
        self.admin_user = UserFactory(role="Admin")
        self.company = CompanyFactory(created_by=self.admin_user)
        self.team = TeamFactory(company=self.company, created_by=self.admin_user)
        self.user = UserFactory(company=self.company)
    
    def test_team_user_create_serializer_valid_data(self):
        """Test TeamUserCreateSerializer with valid data"""
        data = {
            'team': self.team.id,
            'user': self.user.id
        }
        
        serializer = TeamUserCreateSerializer(data=data)
        assert serializer.is_valid()
        
        team_user = serializer.save()
        assert team_user.team == self.team
        assert team_user.user == self.user
    
    def test_team_user_create_serializer_invalid_team(self):
        """Test TeamUserCreateSerializer with invalid team"""
        data = {
            'team': 99999,  # Non-existent team
            'user': self.user.id
        }
        
        serializer = TeamUserCreateSerializer(data=data)
        assert not serializer.is_valid()
        assert 'team' in serializer.errors
    
    def test_team_user_create_serializer_invalid_user(self):
        """Test TeamUserCreateSerializer with invalid user"""
        data = {
            'team': self.team.id,
            'user': 99999  # Non-existent user
        }
        
        serializer = TeamUserCreateSerializer(data=data)
        assert not serializer.is_valid()
        assert 'user' in serializer.errors
    
    def test_team_user_create_serializer_duplicate(self):
        """Test TeamUserCreateSerializer with duplicate team-user combination"""
        # Create a team user first
        TeamUserFactory(team=self.team, user=self.user)
        
        data = {
            'team': self.team.id,
            'user': self.user.id
        }
        
        serializer = TeamUserCreateSerializer(data=data)
        assert not serializer.is_valid()
        assert 'non_field_errors' in serializer.errors


class TestAuditLogSerializer(TestCase):
    """Test cases for AuditLogSerializer"""
    
    def setUp(self):
        self.admin_user = UserFactory(role="Admin")
        self.company = CompanyFactory(created_by=self.admin_user)
        
        # Create an audit log by updating the company
        self.company.name = 'Updated Name'
        self.company.save()
        
        from auditlog.models import LogEntry
        self.log_entry = LogEntry.objects.filter(
            content_type__model='company'
        ).first()
    
    def test_audit_log_serializer_valid_data(self):
        """Test AuditLogSerializer with valid data"""
        if not self.log_entry:
            self.skipTest("No audit log entry found")
        
        serializer = AuditLogSerializer(self.log_entry)
        data = serializer.data
        
        assert data['id'] == self.log_entry.id
        assert data['action'] == self.log_entry.action
        assert data['timestamp'] is not None
        assert 'content_type' in data
        assert 'actor' in data
    
    def test_audit_log_serializer_includes_content_type(self):
        """Test AuditLogSerializer includes content type information"""
        if not self.log_entry:
            self.skipTest("No audit log entry found")
        
        serializer = AuditLogSerializer(self.log_entry)
        data = serializer.data
        
        assert 'content_type' in data
        assert data['content_type']['model'] == 'company'
        assert data['content_type']['app_label'] == 'companies'
    
    def test_audit_log_serializer_includes_actor(self):
        """Test AuditLogSerializer includes actor information"""
        if not self.log_entry:
            self.skipTest("No audit log entry found")
        
        serializer = AuditLogSerializer(self.log_entry)
        data = serializer.data
        
        assert 'actor' in data
        # Actor might be None for some audit log entries
        if data['actor'] is not None:
            assert data['actor']['id'] == self.admin_user.id
            assert data['actor']['username'] == self.admin_user.username
    
    def test_audit_log_serializer_includes_changes(self):
        """Test AuditLogSerializer includes changes information"""
        if not self.log_entry:
            self.skipTest("No audit log entry found")
        
        serializer = AuditLogSerializer(self.log_entry)
        data = serializer.data
        
        assert 'changes' in data
        # Changes should be a dictionary or None
        assert data['changes'] is None or isinstance(data['changes'], dict) 