from django.db import models
from django.db.models import <PERSON>r<PERSON><PERSON>, Model, DateTimeField, IntegerField, BooleanField, ForeignKey, CASCADE, SET_NULL
from django.utils.translation import gettext_lazy as _
from django.contrib.auth import get_user_model
from auditlog.registry import auditlog

User = get_user_model()


class Company(models.Model):
    """
    Company model for multi-tenancy support
    """
    name = CharField(_("Company Name"), max_length=255)
    code = CharField(_("Company Code"), max_length=50, unique=True)
    
    COMPANY_TYPE_CHOICES = [
        ("Parent", "Parent"),
        ("QC", "QC"),
        ("Vendor", "Vendor")
    ]
    
    type = CharField(
        _("Company Type"),
        max_length=20,
        choices=COMPANY_TYPE_CHOICES,
        default="Parent"
    )
    
    created_by = ForeignKey(
        User,
        on_delete=SET_NULL,
        null=True,
        related_name='created_companies'
    )
    
    created_at = DateTimeField(auto_now_add=True)
    is_deleted = BooleanField(default=False)
    
    def __str__(self):
        return f"{self.name} ({self.code})"
    
    class Meta:
        verbose_name = _("Company")
        verbose_name_plural = _("Companies")


class Team(models.Model):
    """
    Team model for organizing users
    """
    TEAM_TYPE_CHOICES = [
        ("Survey", "Survey"),
        ("QC", "Quality Control")
    ]
    
    name = CharField(_("Team Name"), max_length=100)
    company = ForeignKey(
        Company,
        on_delete=CASCADE,
        related_name='teams'
    )
    type = CharField(
        _("Team Type"),
        max_length=20,
        choices=TEAM_TYPE_CHOICES
    )
    created_by = ForeignKey(User, on_delete=CASCADE)
    created_at = DateTimeField(auto_now_add=True)
    is_deleted = BooleanField(default=False)
    
    def __str__(self):
        return f"{self.name} - {self.company.name}"
    
    class Meta:
        verbose_name = _("Team")
        verbose_name_plural = _("Teams")


class TeamUser(models.Model):
    """
    Many-to-many relationship between Team and User
    """
    team = ForeignKey(Team, on_delete=CASCADE)
    user = ForeignKey(User, on_delete=CASCADE)
    added_at = DateTimeField(auto_now_add=True)
    
    class Meta:
        unique_together = ('team', 'user')
        verbose_name = _("Team User")
        verbose_name_plural = _("Team Users")
    
    def __str__(self):
        return f"{self.user.username} - {self.team.name}"
