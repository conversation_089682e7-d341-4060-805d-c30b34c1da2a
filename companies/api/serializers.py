from rest_framework import serializers
from ..models import Company, Team, TeamUser
from django.contrib.auth import get_user_model
from auditlog.models import LogEntry
from drf_spectacular.utils import extend_schema_field
from typing import Any
from django.contrib.contenttypes.models import ContentType

User = get_user_model()


class ContentTypeSerializer(serializers.ModelSerializer):
    """Serializer for content type information"""
    class Meta:
        model = ContentType
        fields = ['id', 'app_label', 'model']


class UserNestedSerializer(serializers.ModelSerializer):
    """Nested serializer for user information"""
    class Meta:
        model = User
        fields = ['id', 'username', 'name', 'email']


class AuditLogSerializer(serializers.ModelSerializer):
    """Serializer for audit log entries"""
    content_type = ContentTypeSerializer(read_only=True)
    actor = UserNestedSerializer(read_only=True, allow_null=True)
    content_type_name = serializers.CharField(source='content_type.name', read_only=True)
    actor_username = serializers.CharField(source='actor.username', read_only=True, allow_null=True)
    actor_email = serializers.CharField(source='actor.email', read_only=True, allow_null=True)
    action_display = serializers.CharField(source='get_action_display', read_only=True)
    
    class Meta:
        model = LogEntry
        fields = [
            'id', 'timestamp', 'action', 'action_display', 'content_type', 
            'content_type_name', 'object_pk', 'object_id', 'object_repr', 
            'actor', 'actor_username', 'actor_email', 'remote_addr', 
            'additional_data', 'changes'
        ]
        read_only_fields = fields


class CompanySerializer(serializers.ModelSerializer):
    created_by = UserNestedSerializer(read_only=True)
    created_by_username = serializers.CharField(source='created_by.username', read_only=True)
    user_count = serializers.SerializerMethodField()
    
    class Meta:
        model = Company
        fields = [
            'id', 'name', 'code', 'type', 'created_by', 'created_by_username',
            'created_at', 'is_deleted', 'user_count'
        ]
        read_only_fields = ['created_by', 'created_at']
    
    @extend_schema_field(int)
    def get_user_count(self, obj: Company) -> int:
        return obj.users.count()


class TeamSerializer(serializers.ModelSerializer):
    created_by = UserNestedSerializer(read_only=True)
    created_by_username = serializers.CharField(source='created_by.username', read_only=True)
    company_name = serializers.CharField(source='company.name', read_only=True)
    member_count = serializers.SerializerMethodField()
    
    class Meta:
        model = Team
        fields = [
            'id', 'name', 'company', 'company_name', 'type', 'created_by',
            'created_by_username', 'created_at', 'is_deleted', 'member_count'
        ]
        read_only_fields = ['created_by', 'created_at']
    
    @extend_schema_field(int)
    def get_member_count(self, obj: Team) -> int:
        return obj.teamuser_set.count()


class TeamUserSerializer(serializers.ModelSerializer):
    user_username = serializers.CharField(source='user.username', read_only=True)
    user_email = serializers.CharField(source='user.email', read_only=True)
    user_role = serializers.CharField(source='user.role', read_only=True)
    team_name = serializers.CharField(source='team.name', read_only=True)
    
    class Meta:
        model = TeamUser
        fields = [
            'id', 'team', 'team_name', 'user', 'user_username', 'user_email',
            'user_role', 'added_at'
        ]
        read_only_fields = ['added_at']


class CompanyCreateSerializer(serializers.ModelSerializer):
    class Meta:
        model = Company
        fields = ['name', 'code', 'type']


class TeamCreateSerializer(serializers.ModelSerializer):
    class Meta:
        model = Team
        fields = ['name', 'company', 'type']


class TeamUserCreateSerializer(serializers.ModelSerializer):
    class Meta:
        model = TeamUser
        fields = ['team', 'user'] 