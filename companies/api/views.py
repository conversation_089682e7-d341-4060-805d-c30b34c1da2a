from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django.contrib.auth import get_user_model
from django.contrib.contenttypes.models import ContentType
from django.db.models import Q
from ..models import Company, Team, TeamUser
from .serializers import (
    CompanySerializer, TeamSerializer, TeamUserSerializer,
    CompanyCreateSerializer, TeamCreateSerializer, TeamUserCreateSerializer,
    AuditLogSerializer
)
from voter.users.permissions import (
    IsAdmin, IsCompanyAdmin, IsAdminOrCompanyAdmin, IsSurveyor, 
    IsVendorSurveyor, IsQCReviewer, CanManageTeams, CanManageUsers
)
from voter.contrib.audit import AuditLogMixin, SoftDeleteAuditMixin
from auditlog.models import LogEntry
from django.db.models import Count
from drf_spectacular.utils import extend_schema_view, extend_schema
from drf_spectacular.openapi import AutoSchema
from voter.contrib.pagination import PaginatedViewSetMixin

User = get_user_model()


class AuditLogViewSet(viewsets.ReadOnlyModelViewSet):
    """
    ViewSet for viewing audit logs
    """
    schema = AutoSchema()
    queryset = LogEntry.objects.all()
    serializer_class = AuditLogSerializer
    permission_classes = [IsAuthenticated, IsAdminOrCompanyAdmin]
    
    def get_queryset(self):
        """Filter audit logs based on user permissions"""
        queryset = LogEntry.objects.all()
        
        # Filter by content type if specified
        content_type = self.request.query_params.get('content_type')
        if content_type:
            try:
                ct = ContentType.objects.get(model=content_type)
                queryset = queryset.filter(content_type=ct)
            except ContentType.DoesNotExist:
                return LogEntry.objects.none()
        
        # Filter by action if specified
        action = self.request.query_params.get('action')
        if action:
            # Map action string to integer if needed
            if not action.isdigit():
                action_choices = dict(LogEntry.Action.choices)
                reverse_map = {v.lower(): k for k, v in action_choices.items() if isinstance(v, str)}
                mapped_action = reverse_map.get(action.lower())
                if mapped_action is None:
                    return LogEntry.objects.none()
                action = mapped_action
            queryset = queryset.filter(action=action)
        
        # Filter by date range if specified
        start_date = self.request.query_params.get('start_date')
        end_date = self.request.query_params.get('end_date')
        if start_date:
            queryset = queryset.filter(timestamp__gte=start_date)
        if end_date:
            queryset = queryset.filter(timestamp__lte=end_date)
        
        # Company admins can only see logs for their company
        if not self.request.user.is_admin():
            if self.request.user.is_company_admin():
                company = self.request.user.company
                # Filter to only show logs related to the user's company
                company_ct = ContentType.objects.get_for_model(Company)
                team_ct = ContentType.objects.get_for_model(Team)
                team_user_ct = ContentType.objects.get_for_model(TeamUser)
                
                queryset = queryset.filter(
                    Q(content_type=company_ct, object_pk=company.id) |
                    Q(content_type=team_ct, object_pk__in=company.teams.values_list('id', flat=True)) |
                    Q(content_type=team_user_ct, object_pk__in=TeamUser.objects.filter(team__company=company).values_list('id', flat=True))
                )
            else:
                # Regular users can only see their own logs
                queryset = queryset.filter(actor=self.request.user)
        
        return queryset.order_by('-timestamp')
    
    @action(detail=False, methods=['get'])
    def summary(self, request):
        """Get audit log summary statistics"""
        queryset = self.get_queryset()
        
        # Use content_type__model instead of content_type__name
        content_type_counts = queryset.values('content_type__model').annotate(count=Count('id')).order_by()
        action_counts = queryset.values('action').annotate(count=Count('id')).order_by()
        return Response({
            'total_logs': queryset.count(),
            'action_counts': action_counts,
            'content_type_counts': content_type_counts,
            'date_range': {
                'earliest': queryset.earliest('timestamp').timestamp if queryset.exists() else None,
                'latest': queryset.latest('timestamp').timestamp if queryset.exists() else None,
            }
        })


class CompanyViewSet(PaginatedViewSetMixin, AuditLogMixin, viewsets.ModelViewSet):
    """
    ViewSet for managing companies with automatic audit logging
    """
    queryset = Company.objects.filter(is_deleted=False)
    serializer_class = CompanySerializer
    permission_classes = [IsAuthenticated, IsAdmin]
    search_fields = ['name', 'code']
    filterset_fields = ['name', 'id']
    ordering_fields = ['name', 'created_at']
    
    def get_serializer_class(self):
        if self.action == 'create':
            return CompanyCreateSerializer
        return CompanySerializer
    
    def perform_create(self, serializer):
        """Override to log company creation with improved audit logging."""
        instance = serializer.save(created_by=self.request.user)
        
        # Get additional context data
        additional_data = self._get_additional_audit_data('create', instance, serializer.validated_data)
        
        # Create audit log
        self._create_audit_log(
            action=LogEntry.Action.CREATE,
            instance=instance,
            additional_data=additional_data,
            operation_context='company_creation'
        )
        
        return instance
    
    def perform_update(self, serializer):
        """Override to log company updates with improved change detection."""
        # Get the instance before saving to capture old values
        instance = serializer.instance
        
        # Save the instance with new data
        updated_instance = serializer.save()
        
        # Get changes by comparing old and new values
        changes = self._get_changes(instance, serializer.validated_data)
        
        # Get additional context data
        additional_data = self._get_additional_audit_data('update', updated_instance, serializer.validated_data)
        
        # Create audit log
        self._create_audit_log(
            action=LogEntry.Action.UPDATE,
            instance=updated_instance,
            changes=changes,
            additional_data=additional_data,
            operation_context='company_update'
        )
        
        return updated_instance
    
    def _get_additional_audit_data(self, action, instance, validated_data):
        """Add custom audit data for company operations."""
        additional_data = {
            'action_source': 'api',
            'company_name': getattr(instance, 'name', None),
        }
        
        if action == 'create':
            additional_data['created_via'] = 'api'
        elif action == 'update':
            additional_data['updated_fields'] = list(validated_data.keys())
        elif action == 'soft_delete':
            additional_data['deleted_via'] = 'soft_delete'
        
        return additional_data
    
    @action(detail=True, methods=['get'])
    def users(self, request, pk=None):
        """Get all users in a company"""
        company = self.get_object()
        users = company.users.filter(is_deleted=False)
        from voter.users.api.serializers import UserSerializer
        serializer = UserSerializer(users, many=True)
        return Response(serializer.data)
    
    @action(detail=True, methods=['get'])
    def teams(self, request, pk=None):
        """Get all teams in a company"""
        company = self.get_object()
        teams = company.teams.filter(is_deleted=False)
        serializer = TeamSerializer(teams, many=True)
        return Response(serializer.data)
    
    @action(detail=True, methods=['get'])
    def audit_logs(self, request, pk=None):
        """Get audit logs for this company"""
        company = self.get_object()
        company_ct = ContentType.objects.get_for_model(Company)
        team_ct = ContentType.objects.get_for_model(Team)
        team_user_ct = ContentType.objects.get_for_model(TeamUser)
        
        # Get team IDs as strings to match object_pk format
        team_ids = [str(id) for id in company.teams.values_list('id', flat=True)]
        
        # Get team user IDs as strings to match object_pk format
        team_user_ids = [str(id) for id in TeamUser.objects.filter(team__company=company).values_list('id', flat=True)]
        
        logs = LogEntry.objects.filter(
            Q(content_type=company_ct, object_pk=str(company.id)) |
            Q(content_type=team_ct, object_pk__in=team_ids) |
            Q(content_type=team_user_ct, object_pk__in=team_user_ids)
        ).order_by('-timestamp')
        
        serializer = AuditLogSerializer(logs, many=True)
        return Response(serializer.data)


class TeamViewSet(PaginatedViewSetMixin, SoftDeleteAuditMixin, viewsets.ModelViewSet):
    """
    ViewSet for managing teams with automatic audit logging
    """
    queryset = Team.objects.filter(is_deleted=False)
    permission_classes = [IsAuthenticated]
    search_fields = ['name', 'company__name', 'description']
    filterset_fields = ['company', 'type']
    ordering_fields = ['name', 'created_at']
    
    def get_permissions(self):
        """Return appropriate permissions based on action"""
        if self.action in ['create', 'update', 'partial_update', 'destroy', 'add_member', 'remove_member', 'bulk_manage_members']:
            return [IsAuthenticated(), IsAdminOrCompanyAdmin(), CanManageTeams()]
        return [IsAuthenticated()]
    
    def get_queryset(self):
        """Filter queryset based on user role and permissions"""
        queryset = Team.objects.filter(is_deleted=False)
        
        # Admin can see all teams
        if self.request.user.is_admin():
            return queryset
        
        # Company Admin can only see teams from their company
        if self.request.user.is_company_admin():
            return queryset.filter(company=self.request.user.company)
        
        # Default: return empty queryset for unknown roles
        return queryset.none()
    
    def get_serializer_class(self):
        if self.action == 'create':
            return TeamCreateSerializer
        return TeamSerializer
    
    def perform_create(self, serializer):
        """Override to log team creation with improved audit logging."""
        instance = serializer.save(created_by=self.request.user)
        
        # Get additional context data
        additional_data = self._get_additional_audit_data('create', instance, serializer.validated_data)
        
        # Create audit log
        self._create_audit_log(
            action=LogEntry.Action.CREATE,
            instance=instance,
            additional_data=additional_data,
            operation_context='team_creation'
        )
        
        return instance
    
    def perform_update(self, serializer):
        """Override to log team updates with improved change detection."""
        # Get the instance before saving to capture old values
        instance = serializer.instance
        
        # Save the instance with new data
        updated_instance = serializer.save()
        
        # Get changes by comparing old and new values
        changes = self._get_changes(instance, serializer.validated_data)
        
        # Get additional context data
        additional_data = self._get_additional_audit_data('update', updated_instance, serializer.validated_data)
        
        # Create audit log
        self._create_audit_log(
            action=LogEntry.Action.UPDATE,
            instance=updated_instance,
            changes=changes,
            additional_data=additional_data,
            operation_context='team_update'
        )
        
        return updated_instance
    
    def _get_additional_audit_data(self, action, instance, validated_data):
        """Add custom audit data for team operations."""
        additional_data = {
            'action_source': 'api',
            'team_name': getattr(instance, 'name', None),
            'company_id': getattr(instance, 'company_id', None),
        }
        
        if action == 'create':
            additional_data['created_via'] = 'api'
        elif action == 'update':
            additional_data['updated_fields'] = list(validated_data.keys())
        elif action == 'soft_delete':
            additional_data['deleted_via'] = 'soft_delete'
        
        return additional_data
    
    @action(detail=True, methods=['get'])
    def members(self, request, pk=None):
        """Get all members in a team"""
        team = self.get_object()
        team_users = team.teamuser_set.all()
        serializer = TeamUserSerializer(team_users, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['get'])
    def team_management(self, request, pk=None):
        """
        Get team management data - existing members and available users
        
        Returns:
        - existing_members: Current team members
        - available_users: Users not in any teams (from same company)
        - team_info: Basic team information
        """
        team = self.get_object()
        
        # Get existing team members
        existing_team_users = TeamUser.objects.filter(team=team)
        existing_members = []
        
        for team_user in existing_team_users:
            user = team_user.user
            if not user.is_deleted:
                existing_members.append({
                    'id': user.id,
                    'name': user.name,
                    'username': user.username,
                    'email': user.email,
                    'role': user.role,
                    'team_user_id': team_user.id,
                    'joined_at': team_user.added_at.isoformat() if team_user.added_at else None
                })
        
        # Get all users in the company who are not in any teams
        company_users = User.objects.filter(
            company=team.company,
            is_deleted=False
        )
        
        # Filter users based on team type
        if team.type == 'Survey':
            if team.company.type == 'Vendor':
                company_users = company_users.filter(role='VendorSurveyor')
            else:
                company_users = company_users.filter(role='Surveyor')

        elif team.type == 'QC':
            # For QC teams, show only QCReviewer users
            company_users = company_users.filter(role='QCReviewer')
        # For other team types, show all users (no role filtering)
        
        # Get all team user IDs for this company
        all_team_user_ids = TeamUser.objects.filter(
            team__company=team.company
        ).values_list('user_id', flat=True)
        
        # Filter out users who are already in any team
        available_users = []
        for user in company_users:
            if user.id not in all_team_user_ids:
                available_users.append({
                    'id': user.id,
                    'name': user.name,
                    'username': user.username,
                    'email': user.email,
                    'role': user.role
                })
        
        # Team information
        team_info = {
            'id': team.id,
            'name': team.name,
            'type': team.type,
            'company': {
                'id': team.company.id,
                'name': team.company.name,
                'type': team.company.type
            },
            'total_members': len(existing_members),
            'available_users_count': len(available_users)
        }
        
        return Response({
            'success': True,
            'team': team_info,
            'existing_members': existing_members,
            'available_users': available_users,
            'summary': {
                'total_existing_members': len(existing_members),
                'total_available_users': len(available_users)
            }
        })
    
    @action(detail=True, methods=['post'])
    def add_member(self, request, pk=None):
        """Add a user to a team"""
        team = self.get_object()
        user_id = request.data.get('user_id')
        
        if not user_id:
            return Response(
                {'success': False, 'error': 'user_id is required'}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        try:
            user = User.objects.get(id=user_id, is_deleted=False)
        except User.DoesNotExist:
            return Response(
                {'success': False, 'error': 'User not found'}, 
                status=status.HTTP_404_NOT_FOUND
            )
        
        team_user, created = TeamUser.objects.get_or_create(
            team=team, 
            user=user
        )
        
        if not created:
            return Response(
                {'success': True, 'message': 'User is already a member of this team'}, 
                status=status.HTTP_200_OK
            )
        
        # Log the team member addition
        from voter.contrib.audit import log_custom_action
        log_custom_action(
            user=request.user,
            action_name='add_team_member',
            instance=team_user,
            description=f'Added user {user.username} to team {team.name}',
            additional_data={
                'team_id': team.id,
                'user_id': user.id,
                'team_name': team.name,
                'user_name': user.username,
            },
            request=request
        )
        
        serializer = TeamUserSerializer(team_user)
        return Response({
            'success': True,
            'message': 'User added to team successfully',
            'team_user': serializer.data
        }, status=status.HTTP_200_OK)
    
    @action(detail=True, methods=['delete'])
    def remove_member(self, request, pk=None):
        """Remove a user from a team"""
        team = self.get_object()
        user_id = request.data.get('user_id')
        
        if not user_id:
            return Response(
                {'success': False, 'error': 'user_id is required'}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        try:
            team_user = TeamUser.objects.get(team=team, user_id=user_id)
        except TeamUser.DoesNotExist:
            return Response(
                {'success': False, 'error': 'User is not a member of this team'}, 
                status=status.HTTP_404_NOT_FOUND
            )
        
        # Log the team member removal
        from voter.contrib.audit import log_custom_action
        log_custom_action(
            user=request.user,
            action_name='remove_team_member',
            instance=team_user,
            description=f'Removed user {team_user.user.username} from team {team.name}',
            additional_data={
                'team_id': team.id,
                'user_id': team_user.user.id,
                'team_name': team.name,
                'user_name': team_user.user.username,
            },
            request=request
        )
        
        team_user.delete()
        
        return Response({
            'success': True,
            'message': 'User removed from team successfully'
        }, status=status.HTTP_200_OK)

    @action(detail=True, methods=['post'])
    def bulk_manage_members(self, request, pk=None):
        """
        Bulk add/remove team members
        
        Request body:
        {
            "add_user_ids": [1, 2, 3],      # Optional: Users to add
            "remove_user_ids": [4, 5, 6],   # Optional: Users to remove
            "remove_team_user_ids": [10, 11] # Optional: Remove by team_user_id
        }
        """
        team = self.get_object()
        add_user_ids = request.data.get('add_user_ids', [])
        remove_user_ids = request.data.get('remove_user_ids', [])
        remove_team_user_ids = request.data.get('remove_team_user_ids', [])
        
        if not any([add_user_ids, remove_user_ids, remove_team_user_ids]):
            return Response({
                'success': False,
                'error': 'Must provide at least one of: add_user_ids, remove_user_ids, or remove_team_user_ids'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        results = {
            'added': [],
            'removed': [],
            'failed_adds': [],
            'failed_removes': []
        }
        
        # Add users to team
        if add_user_ids:
            # Validate users exist and belong to the same company
            users_to_add = User.objects.filter(
                id__in=add_user_ids,
                company=team.company,
                is_deleted=False
            )
            
            # Filter users based on team type
            if team.type == 'Survey':
                # For Survey teams, only allow Surveyor users
                users_to_add = users_to_add.filter(role='Surveyor')
            elif team.type == 'QC':
                # For QC teams, only allow QCReviewer users
                users_to_add = users_to_add.filter(role='QCReviewer')
            # For other team types, allow all users (no role filtering)
            
            for user in users_to_add:
                try:
                    team_user, created = TeamUser.objects.get_or_create(
                        team=team,
                        user=user
                    )
                    
                    if created:
                        results['added'].append({
                            'user_id': user.id,
                            'user_name': user.name,
                            'username': user.username
                        })
                        
                        # Log the addition
                        from voter.contrib.audit import log_custom_action
                        log_custom_action(
                            user=request.user,
                            action_name='add_team_member',
                            instance=team_user,
                            description=f'Added user {user.username} to team {team.name}',
                            additional_data={
                                'team_id': team.id,
                                'user_id': user.id,
                                'team_name': team.name,
                                'user_name': user.username,
                            },
                            request=request
                        )
                    else:
                        results['failed_adds'].append({
                            'user_id': user.id,
                            'user_name': user.name,
                            'error': 'User is already a member of this team'
                        })
                        
                except Exception as e:
                    results['failed_adds'].append({
                        'user_id': user.id,
                        'user_name': user.name,
                        'error': str(e)
                    })
            
            # Check for users not found
            found_user_ids = [user.id for user in users_to_add]
            not_found_ids = [uid for uid in add_user_ids if uid not in found_user_ids]
            for uid in not_found_ids:
                results['failed_adds'].append({
                    'user_id': uid,
                    'error': 'User not found or does not belong to this company'
                })
        
        # Remove users by user_id
        if remove_user_ids:
            team_users_to_remove = TeamUser.objects.filter(
                team=team,
                user_id__in=remove_user_ids
            )
            
            for team_user in team_users_to_remove:
                try:
                    user_name = team_user.user.username
                    user_id = team_user.user.id
                    
                    # Log the removal
                    from voter.contrib.audit import log_custom_action
                    log_custom_action(
                        user=request.user,
                        action_name='remove_team_member',
                        instance=team_user,
                        description=f'Removed user {user_name} from team {team.name}',
                        additional_data={
                            'team_id': team.id,
                            'user_id': user_id,
                            'team_name': team.name,
                            'user_name': user_name,
                        },
                        request=request
                    )
                    
                    team_user.delete()
                    
                    results['removed'].append({
                        'user_id': user_id,
                        'user_name': user_name,
                        'username': team_user.user.username
                    })
                    
                except Exception as e:
                    results['failed_removes'].append({
                        'user_id': team_user.user.id,
                        'user_name': team_user.user.username,
                        'error': str(e)
                    })
        
        # Remove users by team_user_id
        if remove_team_user_ids:
            team_users_to_remove = TeamUser.objects.filter(
                id__in=remove_team_user_ids,
                team=team
            )
            
            for team_user in team_users_to_remove:
                try:
                    user_name = team_user.user.username
                    user_id = team_user.user.id
                    
                    # Log the removal
                    from voter.contrib.audit import log_custom_action
                    log_custom_action(
                        user=request.user,
                        action_name='remove_team_member',
                        instance=team_user,
                        description=f'Removed user {user_name} from team {team.name}',
                        additional_data={
                            'team_id': team.id,
                            'user_id': user_id,
                            'team_name': team.name,
                            'user_name': user_name,
                        },
                        request=request
                    )
                    
                    team_user.delete()
                    
                    results['removed'].append({
                        'team_user_id': team_user.id,
                        'user_id': user_id,
                        'user_name': user_name,
                        'username': team_user.user.username
                    })
                    
                except Exception as e:
                    results['failed_removes'].append({
                        'team_user_id': team_user.id,
                        'user_id': team_user.user.id,
                        'user_name': team_user.user.username,
                        'error': str(e)
                    })
        
        # Prepare response message
        message_parts = []
        if results['added']:
            message_parts.append(f"Added {len(results['added'])} member(s)")
        if results['removed']:
            message_parts.append(f"Removed {len(results['removed'])} member(s)")
        
        message = " and ".join(message_parts) if message_parts else "No changes made"
        
        return Response({
            'success': True,
            'message': message,
            'team': {
                'id': team.id,
                'name': team.name
            },
            'results': results,
            'summary': {
                'total_added': len(results['added']),
                'total_removed': len(results['removed']),
                'total_failed_adds': len(results['failed_adds']),
                'total_failed_removes': len(results['failed_removes'])
            }
        }, status=status.HTTP_200_OK)
    
    @action(detail=True, methods=['get'])
    def audit_logs(self, request, pk=None):
        """Get audit logs for this team"""
        team = self.get_object()
        team_ct = ContentType.objects.get_for_model(Team)
        team_user_ct = ContentType.objects.get_for_model(TeamUser)
        
        # Get team user IDs as strings to match object_pk format
        team_user_ids = [str(id) for id in team.teamuser_set.values_list('id', flat=True)]
        
        logs = LogEntry.objects.filter(
            Q(content_type=team_ct, object_pk=str(team.id)) |
            Q(content_type=team_user_ct, object_pk__in=team_user_ids)
        ).order_by('-timestamp')
        
        serializer = AuditLogSerializer(logs, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def ground_teams(self, request):
        """Get all ground teams (Survey type teams)"""
        # Get the base queryset and filter by type
        queryset = super().get_queryset().filter(type='Survey')
        
        # Apply search and filtering
        queryset = self.filter_queryset(queryset)

        # Check if pagination is requested
        if request.query_params.get('pagination', '').lower() == 'true':
            page = self.paginate_queryset(queryset)
            if page is not None:
                serializer = self.get_serializer(page, many=True)
                return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def qc_teams(self, request):
        """Get all QC teams (Quality Control type teams)"""
        # Get the base queryset and filter by type
        queryset = super().get_queryset().filter(type='QC')
        
        # Apply search and filtering
        queryset = self.filter_queryset(queryset)

        # Check if pagination is requested
        if request.query_params.get('pagination', '').lower() == 'true':
            page = self.paginate_queryset(queryset)
            if page is not None:
                serializer = self.get_serializer(page, many=True)
                return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)


class TeamUserViewSet(PaginatedViewSetMixin, AuditLogMixin, viewsets.ModelViewSet):
    """
    ViewSet for managing team-user relationships with automatic audit logging
    """
    queryset = TeamUser.objects.all()
    serializer_class = TeamUserSerializer
    permission_classes = [IsAuthenticated]
    search_fields = ['team__name', 'user__name', 'user__username']
    filterset_fields = ['team', 'user']
    ordering_fields = ['added_at', 'team__name', 'user__name']
    
    def get_permissions(self):
        """Return appropriate permissions based on action"""
        if self.action in ['create', 'update', 'partial_update', 'destroy']:
            return [IsAuthenticated(), IsAdminOrCompanyAdmin(), CanManageTeams()]
        return [IsAuthenticated()]
    
    def get_serializer_class(self):
        if self.action == 'create':
            return TeamUserCreateSerializer
        return TeamUserSerializer
    
    def perform_create(self, serializer):
        """Override to log team user creation with improved audit logging."""
        instance = serializer.save()
        
        # Get additional context data
        additional_data = self._get_additional_audit_data('create', instance, serializer.validated_data)
        
        # Create audit log
        self._create_audit_log(
            action=LogEntry.Action.CREATE,
            instance=instance,
            additional_data=additional_data,
            operation_context='team_user_creation'
        )
        
        return instance
    
    def perform_update(self, serializer):
        """Override to log team user updates with improved change detection."""
        # Get the instance before saving to capture old values
        instance = serializer.instance
        
        # Save the instance with new data
        updated_instance = serializer.save()
        
        # Get changes by comparing old and new values
        changes = self._get_changes(instance, serializer.validated_data)
        
        # Get additional context data
        additional_data = self._get_additional_audit_data('update', updated_instance, serializer.validated_data)
        
        # Create audit log
        self._create_audit_log(
            action=LogEntry.Action.UPDATE,
            instance=updated_instance,
            changes=changes,
            additional_data=additional_data,
            operation_context='team_user_update'
        )
        
        return updated_instance
    
    def _get_additional_audit_data(self, action, instance, validated_data):
        """Add custom audit data for team user operations."""
        additional_data = {
            'action_source': 'api',
            'team_id': getattr(instance, 'team_id', None),
            'user_id': getattr(instance, 'user_id', None),
        }
        
        if hasattr(instance, 'team'):
            additional_data['team_name'] = getattr(instance.team, 'name', None)
        if hasattr(instance, 'user'):
            additional_data['user_name'] = getattr(instance.user, 'username', None)
        
        if action == 'create':
            additional_data['created_via'] = 'api'
        elif action == 'update':
            additional_data['updated_fields'] = list(validated_data.keys())
        elif action == 'delete':
            additional_data['deleted_via'] = 'hard_delete'
        
        return additional_data 