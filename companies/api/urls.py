from rest_framework.routers import DefaultRouter
from django.urls import path
from .views import CompanyViewSet, TeamViewSet, TeamUserViewSet, AuditLogViewSet

router = DefaultRouter()
router.register(r'companies', CompanyViewSet)
router.register(r'teams', TeamViewSet)
router.register(r'team-users', TeamUserViewSet)
router.register(r'audit-logs', AuditLogViewSet)

# Custom URL patterns to support both underscore and hyphen formats
custom_urlpatterns = [
    # Team management endpoints with hyphens
    path('teams/<int:pk>/team_management/', TeamViewSet.as_view({'get': 'team_management'}), name='team-team-management-hyphen'),
    path('teams/<int:pk>/add-member/', TeamViewSet.as_view({'post': 'add_member'}), name='team-add-member-hyphen'),
    path('teams/<int:pk>/remove-member/', TeamViewSet.as_view({'delete': 'remove_member'}), name='team-remove-member-hyphen'),
    path('teams/<int:pk>/bulk-manage-members/', TeamViewSet.as_view({'post': 'bulk_manage_members'}), name='team-bulk-manage-members-hyphen'),
    path('teams/<int:pk>/audit-logs/', TeamViewSet.as_view({'get': 'audit_logs'}), name='team-audit-logs-hyphen'),
    
    # Team type endpoints with hyphens
    path('teams/ground-teams/', TeamViewSet.as_view({'get': 'ground_teams'}), name='team-ground-teams-hyphen'),
    path('teams/qc-teams/', TeamViewSet.as_view({'get': 'qc_teams'}), name='team-qc-teams-hyphen'),
    
    # Company endpoints with hyphens
    path('companies/<int:pk>/audit-logs/', CompanyViewSet.as_view({'get': 'audit_logs'}), name='company-audit-logs-hyphen'),
]

urlpatterns = router.urls + custom_urlpatterns 