from django.contrib import admin
from .models import Company, Team, TeamUser
from auditlog.models import LogEntry
from auditlog.registry import auditlog

# Unregister the default LogEntry admin if already registered
try:
    admin.site.unregister(LogEntry)
except admin.sites.NotRegistered:
    pass

@admin.register(Company)
class CompanyAdmin(admin.ModelAdmin):
    list_display = ('name', 'code', 'type', 'created_by', 'created_at', 'is_deleted')
    list_filter = ('type', 'is_deleted', 'created_at')
    search_fields = ('name', 'code')
    readonly_fields = ('created_at',)
    ordering = ('-created_at',)


@admin.register(Team)
class TeamAdmin(admin.ModelAdmin):
    list_display = ('name', 'company', 'type', 'created_by', 'created_at', 'is_deleted')
    list_filter = ('type', 'company', 'is_deleted', 'created_at')
    search_fields = ('name', 'company__name')
    readonly_fields = ('created_at',)
    ordering = ('-created_at',)


@admin.register(TeamUser)
class TeamUserAdmin(admin.ModelAdmin):
    list_display = ('user', 'team', 'added_at')
    list_filter = ('team__company', 'team__type', 'added_at')
    search_fields = ('user__username', 'user__email', 'team__name')
    readonly_fields = ('added_at',)
    ordering = ('-added_at',)


@admin.register(LogEntry)
class LogEntryAdmin(admin.ModelAdmin):
    """Admin for audit log entries"""
    list_display = ('timestamp', 'action', 'content_type', 'object_pk', 'actor', 'remote_addr')
    list_filter = ('action', 'content_type', 'timestamp')
    search_fields = ('object_pk', 'actor__username', 'remote_addr')
    readonly_fields = ('timestamp', 'action', 'content_type', 'object_pk', 'object_id', 
                      'object_repr', 'actor', 'remote_addr', 'additional_data', 'changes')
    ordering = ('-timestamp',)
    
    def has_add_permission(self, request):
        return False
    
    def has_change_permission(self, request, obj=None):
        return False
    
    def has_delete_permission(self, request, obj=None):
        return False
