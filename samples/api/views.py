from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from ..models import Sample, QuestionResponse
from .serializers import SampleSerializer, QuestionResponseSerializer, CompleteSurveyResponseSerializer
from voter.contrib.audit import AuditLogMixin, MultiModelAuditMixin, log_consolidated_multi_model_operation
from voter.contrib.pagination import PaginatedViewSetMixin
from assignments.services import AssignmentTrackingService
from auditlog.models import LogEntry
from voter.users.permissions import (
    IsAdmin, IsCompanyAdmin, IsAdminOrCompanyAdmin, IsSurveyor, 
    IsVendorSurveyor, IsQCReviewer, IsSurveyorOrVendorSurveyor,
    CanCollectSamples, CanReviewSamples
)


class CanSubmitCompleteResponse(IsAuthenticated):
    """
    Permission to submit complete survey responses.
    Allows Admin, Company Admin, Surveyor, or Vendor Surveyor.
    """
    
    def has_permission(self, request, view):
        if not super().has_permission(request, view):
            return False
        
        user = request.user
        return (
            user.is_admin() or 
            user.is_company_admin() or 
            user.is_surveyor() or 
            user.is_vendor_surveyor()
        )


class SampleViewSet(PaginatedViewSetMixin, MultiModelAuditMixin, viewsets.ModelViewSet):
    queryset = Sample.objects.all()
    serializer_class = SampleSerializer
    permission_classes = [IsAuthenticated]
    search_fields = ['survey__title', 'user__name', 'address']
    filterset_fields = ['survey', 'user', 'is_offline', 'is_uploaded', 'is_qc_done']
    date_filter_field = 'timestamp'
    ordering_fields = ['timestamp', 'created_at', 'survey__title']
    
    def get_permissions(self):
        """Return appropriate permissions based on action"""
        if self.action == 'submit_complete_response':
            return [CanSubmitCompleteResponse()]
        elif self.action in ['create', 'update', 'partial_update', 'destroy']:
            return [IsAuthenticated(), IsAdminOrCompanyAdmin(), IsSurveyorOrVendorSurveyor()]
        return [IsAuthenticated()]
    
    def get_queryset(self):
        """Filter queryset based on user role and permissions"""
        queryset = Sample.objects.all()
        
        # Admin can see all samples
        if self.request.user.is_admin():
            return queryset
        
        # Company Admin can see samples from their company
        if self.request.user.is_company_admin():
            return queryset.filter(user__company=self.request.user.company)
        
        # Surveyor and Vendor Surveyor can only see samples they created
        if self.request.user.is_surveyor() or self.request.user.is_vendor_surveyor():
            return queryset.filter(user=self.request.user)
        
        # QC Reviewer can see samples from their company
        if self.request.user.is_qc_reviewer():
            return queryset.filter(user__company=self.request.user.company)
        
        # Default: return empty queryset for unknown roles
        return queryset.none()
    
    def get_serializer_class(self):
        if self.action == 'submit_complete_response':
            return CompleteSurveyResponseSerializer
        return SampleSerializer
    
    def perform_create(self, serializer):
        """Override to log sample creation with consolidated audit logging."""
        # Start consolidated operation to prevent individual audit logging
        self._start_consolidated_operation()
        
        sample = serializer.save(user=self.request.user)
        
        # Get all related models for audit logging
        related_instances = []
        
        # Collect question responses
        for response in sample.responses.all():
            related_instances.append(response)
        
        # Create consolidated audit log if there are related instances
        if related_instances:
            self._create_consolidated_audit_log(
                action=LogEntry.Action.CREATE,
                primary_instance=sample,
                related_instances=related_instances,
                operation_type='create',
                description=f"Sample creation with {len(related_instances)} question responses"
            )
        
        # End consolidated operation
        self._end_consolidated_operation()
        
        # Automatically track daily progress and performance
        self._track_sample_progress(sample)
        
        return sample
    
    def perform_update(self, serializer):
        """Override to log sample updates with improved change detection."""
        # Start consolidated operation to prevent individual audit logging
        self._start_consolidated_operation()
        
        # Get the instance before saving to capture old values
        sample = serializer.instance
        
        # Get all related models before update for audit logging
        related_instances_before = []
        for response in sample.responses.all():
            related_instances_before.append(response)
        
        # Save the instance with new data
        updated_sample = serializer.save()
        
        # Get all related models after update for audit logging
        related_instances_after = []
        for response in updated_sample.responses.all():
            related_instances_after.append(response)
        
        # Detect changes for UPDATE operation
        changes = self._detect_multi_model_changes(
            sample, 
            updated_sample,
            related_instances_before, 
            related_instances_after
        )
        
        # Create consolidated audit log
        self._create_consolidated_audit_log(
            action=LogEntry.Action.UPDATE,
            primary_instance=updated_sample,
            related_instances=related_instances_after,
            operation_type='update',
            description=f"Sample update with {len(related_instances_after)} question responses",
            changes=changes
        )
        
        # End consolidated operation
        self._end_consolidated_operation()
        
        return updated_sample
    
    def _track_sample_progress(self, sample):
        """
        Automatically track progress when a sample is created
        """
        try:
            # Update daily progress
            daily_target = AssignmentTrackingService.update_daily_progress(
                user=sample.user,
                survey=sample.survey,
                sample_count=1
            )
            
            # Update assignment progress
            assignment = AssignmentTrackingService.update_assignment_progress(
                user=sample.user,
                survey=sample.survey
            )
            
            # Generate performance report if assignment exists
            if assignment:
                AssignmentTrackingService.generate_performance_report(assignment)
                
        except Exception as e:
            print(f"Error tracking sample progress: {e}")
    
    @action(detail=False, methods=['post'], url_path='submit-complete')
    def submit_complete_response(self, request):
        """
        Submit a complete survey response with all questions in a single request
        Perfect for field survey applications with offline support
        Enhanced with consolidated audit logging for multi-model operations
        """
        try:
            # Start consolidated operation to prevent individual audit logging
            self._start_consolidated_operation()
            
            serializer = self.get_serializer(data=request.data)
            if serializer.is_valid():
                sample = serializer.save()
                
                # Get all related models for audit logging
                related_instances = []
                
                # Collect question responses
                for response in sample.responses.all():
                    related_instances.append(response)
                
                # Create consolidated audit log if there are related instances
                if related_instances:
                    log_consolidated_multi_model_operation(
                        user=request.user,
                        primary_instance=sample,
                        related_instances=related_instances,
                        operation_type='create',
                        description=f"Complete survey response submission via API with {len(related_instances)} question responses",
                        request=request,
                        operation_context='sample_submission_complete_api'
                    )
                
                # End consolidated operation
                self._end_consolidated_operation()
                
                # Automatically track daily progress and performance
                self._track_sample_progress(sample)
                
                # Return the created sample with all nested data
                response_serializer = SampleSerializer(sample, context={'request': request})
                return Response(response_serializer.data, status=status.HTTP_201_CREATED)
            
            # End consolidated operation on error
            self._end_consolidated_operation()
            
            # Handle serializer errors safely
            try:
                errors = serializer.errors
                return Response(errors, status=status.HTTP_400_BAD_REQUEST)
            except Exception as e:
                # Fallback error response if serializer.errors fails
                return Response(
                    {'error': 'Invalid data provided', 'detail': str(e)},
                    status=status.HTTP_400_BAD_REQUEST
                )
        except Exception as e:
            # End consolidated operation on exception
            self._end_consolidated_operation()
            
            return Response(
                {'error': 'Failed to submit survey response', 'detail': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )
    
    @action(detail=False, methods=['get'], url_path='my-performance')
    def my_performance(self, request):
        """Get current user's performance summary"""
        start_date_str = request.query_params.get('start_date')
        end_date_str = request.query_params.get('end_date')
        
        start_date = None
        end_date = None
        
        if start_date_str:
            try:
                from django.utils import timezone
                start_date = timezone.datetime.strptime(start_date_str, '%Y-%m-%d').date()
            except ValueError:
                return Response(
                    {'error': 'Invalid start_date format. Use YYYY-MM-DD'},
                    status=status.HTTP_400_BAD_REQUEST
                )
        
        if end_date_str:
            try:
                from django.utils import timezone
                end_date = timezone.datetime.strptime(end_date_str, '%Y-%m-%d').date()
            except ValueError:
                return Response(
                    {'error': 'Invalid end_date format. Use YYYY-MM-DD'},
                    status=status.HTTP_400_BAD_REQUEST
                )
        
        performance_summary = AssignmentTrackingService.get_user_performance_summary(
            user=request.user,
            start_date=start_date,
            end_date=end_date
        )
        
        return Response(performance_summary)
    
    @action(detail=False, methods=['get'], url_path='daily-status')
    def daily_status(self, request):
        """Get current user's daily target status for all surveys"""
        survey_id = request.query_params.get('survey_id')
        
        if survey_id:
            # Get status for specific survey
            from surveys.models import Survey
            try:
                survey = Survey.objects.get(id=survey_id)
                daily_status = AssignmentTrackingService.check_daily_target_status(
                    user=request.user,
                    survey=survey
                )
                return Response(daily_status)
            except Survey.DoesNotExist:
                return Response(
                    {'error': 'Survey not found'},
                    status=status.HTTP_404_NOT_FOUND
                )
        else:
            # Get status for all assigned surveys
            from assignments.models import SurveyAssignment
            assignments = SurveyAssignment.objects.filter(
                assignee=request.user,
                is_deleted=False
            )
            
            status_list = []
            for assignment in assignments:
                daily_status = AssignmentTrackingService.check_daily_target_status(
                    user=request.user,
                    survey=assignment.survey
                )
                if daily_status:
                    daily_status['survey_id'] = assignment.survey.id
                    daily_status['survey_title'] = assignment.survey.title
                    status_list.append(daily_status)
            
            return Response(status_list)


class QuestionResponseViewSet(PaginatedViewSetMixin, AuditLogMixin, viewsets.ModelViewSet):
    queryset = QuestionResponse.objects.all()
    serializer_class = QuestionResponseSerializer
    permission_classes = [IsAuthenticated]
    search_fields = ['response', 'question__question_text', 'sample__survey__title']
    filterset_fields = ['sample', 'question', 'question__type']
    date_filter_field = 'created_at'
    ordering_fields = ['created_at', 'question__order']
    
    def get_permissions(self):
        """Return appropriate permissions based on action"""
        if self.action in ['create', 'update', 'partial_update', 'destroy']:
            return [IsAuthenticated(), IsAdminOrCompanyAdmin(), IsSurveyorOrVendorSurveyor()]
        return [IsAuthenticated()]
    
    def get_queryset(self):
        """Filter queryset based on user role and permissions"""
        queryset = QuestionResponse.objects.all()
        
        # Admin can see all question responses
        if self.request.user.is_admin():
            return queryset
        
        # Company Admin can see question responses from their company
        if self.request.user.is_company_admin():
            return queryset.filter(sample__user__company=self.request.user.company)
        
        # Surveyor and Vendor Surveyor can only see question responses from their samples
        if self.request.user.is_surveyor() or self.request.user.is_vendor_surveyor():
            return queryset.filter(sample__user=self.request.user)
        
        # QC Reviewer can see question responses from their company
        if self.request.user.is_qc_reviewer():
            return queryset.filter(sample__user__company=self.request.user.company)
        
        # Default: return empty queryset for unknown roles
        return queryset.none() 