import json
import logging
from django.core.exceptions import ObjectDoesNotExist
from django.db import IntegrityError
from rest_framework import serializers
from ..models import Sample, QuestionResponse
from config.storage_backends import handle_file_storage

logger = logging.getLogger(__name__)


class QuestionResponseSerializer(serializers.ModelSerializer):
    question_text = serializers.CharField(source='question.question_text', read_only=True)
    question_type = serializers.CharField(source='question.type', read_only=True)
    selected_options_text = serializers.SerializerMethodField()
    
    class Meta:
        model = QuestionResponse
        fields = '__all__'
        read_only_fields = ('response_timestamp',)
    
    def get_selected_options_text(self, obj):
        return [option.option_text for option in obj.selected_options.all()]
    
    def create(self, validated_data):
        # Handle file upload
        file = validated_data.pop('file', None)
        if file is not None:
            folder = 'responses'
            success, message = handle_file_storage(validated_data, file, folder, 'file')
            if not success:
                raise serializers.ValidationError(message)
        
        return super().create(validated_data)
    
    def update(self, instance, validated_data):
        # Handle file upload
        file = validated_data.pop('file', None)
        if file is not None:
            folder = 'responses'
            success, message = handle_file_storage(validated_data, file, folder, 'file')
            if not success:
                raise serializers.ValidationError(message)
        
        return super().update(instance, validated_data)


class SampleSerializer(serializers.ModelSerializer):
    responses = QuestionResponseSerializer(many=True, read_only=True)
    survey_title = serializers.CharField(source='survey.title', read_only=True)
    user_name = serializers.CharField(source='user.name', read_only=True)
    team_name = serializers.CharField(source='team.name', read_only=True)
    
    class Meta:
        model = Sample
        fields = '__all__'
        read_only_fields = ('id', 'timestamp', 'start_time', 'end_time')
    
    def create(self, validated_data):
        # Handle file upload
        file = validated_data.pop('file', None)
        if file is not None:
            folder = 'samples'
            success, message = handle_file_storage(validated_data, file, folder, 'file')
            if not success:
                raise serializers.ValidationError(message)
        
        return super().create(validated_data)
    
    def update(self, instance, validated_data):
        # Handle file upload
        file = validated_data.pop('file', None)
        if file is not None:
            folder = 'samples'
            success, message = handle_file_storage(validated_data, file, folder, 'file')
            if not success:
                raise serializers.ValidationError(message)
        
        return super().update(instance, validated_data) 


# New serializer for submitting a single question response
class QuestionResponseSubmitSerializer(serializers.Serializer):
    """
    Serializer for submitting a single question response
    """
    question_id = serializers.IntegerField()
    response = serializers.CharField(required=False, allow_blank=True)
    selected_option_ids = serializers.ListField(
        child=serializers.IntegerField(),
        required=False,
        default=list
    )
    audio_response = serializers.CharField(required=False, allow_blank=True)
    image_response = serializers.CharField(required=False, allow_blank=True)
    file = serializers.FileField(required=False, allow_null=True)


class CompleteSurveyResponseSerializer(serializers.ModelSerializer):
    """
    Serializer for submitting a complete survey response with all questions at once
    Perfect for field survey applications with offline support
    Supports file uploads for question responses
    """
    
    class Meta:
        model = Sample
        fields = [
            'survey', 'team', 'latitude', 'longitude', 'address', 
            'device_info', 'audio_url', 'file', 'is_offline'
        ]
        read_only_fields = ('id', 'timestamp', 'start_time', 'end_time', 'user', 'is_uploaded', 'is_qc_done')
    
    def to_internal_value(self, data):
        # Convert QueryDict to mutable dict
        if hasattr(data, 'copy') and not isinstance(data, dict):
            data = data.copy()
            data._mutable = True
        
        # Store responses for later processing
        self._responses_data = None
        if 'responses' in data:
            responses_str = data.get('responses')
            if isinstance(responses_str, str):
                try:
                    self._responses_data = json.loads(responses_str)
                except json.JSONDecodeError as e:
                    raise serializers.ValidationError({
                        'responses': 'Invalid JSON string in responses field'
                    })
            else:
                self._responses_data = responses_str
        
        # Handle file uploads from multipart request
        files = getattr(self.context.get('request'), 'FILES', {})
        if files:
            # Handle sample file upload
            if 'file' in files:
                data['file'] = files['file']
            
            # Handle response file uploads
            if self._responses_data:
                for file_key, file_obj in files.items():
                    if file_key.startswith('responses.') and file_key.endswith('.file'):
                        path_parts = file_key.split('.')
                        if len(path_parts) >= 3:
                            response_idx = int(path_parts[1])
                            if isinstance(self._responses_data, list) and response_idx < len(self._responses_data):
                                # Ensure the response dict exists and is mutable
                                if not isinstance(self._responses_data[response_idx], dict):
                                    self._responses_data[response_idx] = {}
                                self._responses_data[response_idx]['file'] = file_obj
        
        result = super().to_internal_value(data)
        return result
    
    def validate_responses(self, value):
        """
        Validate that all responses have valid question IDs and no duplicates
        """
        if not value:
            raise serializers.ValidationError("At least one response is required")
        
        question_ids = [resp['question_id'] for resp in value]
        if len(question_ids) != len(set(question_ids)):
            raise serializers.ValidationError("Duplicate question IDs found")
        
        return value
    
    def create(self, validated_data):
        responses_data = getattr(self, '_responses_data', [])
        
        # Handle sample file upload
        file = validated_data.pop('file', None)
        if file is not None:
            folder = 'samples'
            success, message = handle_file_storage(validated_data, file, folder, 'file')
            if not success:
                raise serializers.ValidationError(f"Sample file upload failed: {message}")
        
        # Create the sample first
        sample = Sample.objects.create(
            user=self.context['request'].user,
            **validated_data
        )
        
        try:
            # Create all question responses
            for response_data in responses_data:
                question_id = response_data['question_id']
                selected_option_ids = response_data.get('selected_option_ids', [])
                file = response_data.get('file', None)
                
                # Handle file upload for this response
                file_path = None
                if file is not None:
                    folder = 'responses'
                    file_data = {}
                    success, message = handle_file_storage(file_data, file, folder, 'file')
                    if not success:
                        raise serializers.ValidationError(f"File upload failed for question {question_id}: {message}")
                    file_path = file_data.get('file')
                
                # Create the question response
                question_response = QuestionResponse.objects.create(
                    sample=sample,
                    question_id=question_id,
                    response=response_data.get('response', ''),
                    audio_response=response_data.get('audio_response', ''),
                    image_response=response_data.get('image_response', ''),
                    file=file_path
                )
                
                # Add selected options if any
                if selected_option_ids:
                    question_response.selected_options.set(selected_option_ids)
        
        except IntegrityError as e:
            # Handle sequence issues specifically
            if 'duplicate key value violates unique constraint' in str(e) and 'pkey' in str(e):
                logger.error(f"Database sequence issue detected: {str(e)}")
                # Try to fix the sequence and retry once
                if QuestionResponse.fix_sequence():
                    logger.info("Sequence fixed, but sample creation failed. Cleaning up.")

                # Clean up the created sample
                sample.delete()
                raise serializers.ValidationError({
                    'error': 'Failed to submit survey response',
                    'detail': 'Database sequence issue detected. Please try again. If the problem persists, contact support.'
                })
            else:
                # Other integrity errors
                sample.delete()
                raise serializers.ValidationError({
                    'error': 'Failed to submit survey response',
                    'detail': f'Database integrity error: {str(e)}'
                })
        except (ValueError, TypeError, ObjectDoesNotExist) as e:
            # If any error occurs, delete the created sample to avoid orphaned data
            sample.delete()
            raise serializers.ValidationError({
                'error': 'Invalid data for responses',
                'detail': f'An error occurred while processing responses: {str(e)}'
            })
        
        return sample
    
    def to_representation(self, instance):
        """
        Return the created sample with all responses
        """
        return SampleSerializer(instance, context=self.context).data 