from django.test import TestCase
from django.core.files.uploadedfile import SimpleUploadedFile
from django.contrib.auth import get_user_model
from .models import Sample, QuestionResponse
from surveys.models import Survey, Section, Question
from companies.models import Company, Team

User = get_user_model()


class SampleFileFieldTest(TestCase):
    """Test that the Sample model's file field works correctly"""
    
    def setUp(self):
        # Create test data
        self.company = Company.objects.create(name="Test Company")
        self.team = Team.objects.create(name="Test Team", company=self.company)
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123',
            company=self.company
        )
        
        # Create survey structure
        self.survey = Survey.objects.create(
            title="Test Survey",
            created_by=self.user
        )
        self.section = Section.objects.create(
            title="Test Section",
            survey=self.survey,
            order=1
        )
        self.question = Question.objects.create(
            section=self.section,
            question_text="Test Question",
            type="Text",
            order=1
        )
    
    def test_sample_file_field_creation(self):
        """Test creating a sample with a file"""
        # Create a simple test file
        test_file = SimpleUploadedFile(
            "test_file.txt",
            b"Test file content",
            content_type="text/plain"
        )
        
        # Create sample with file
        sample = Sample.objects.create(
            survey=self.survey,
            user=self.user,
            team=self.team,
            latitude="12.9716",
            longitude="77.5946",
            address="Test Address",
            file=test_file
        )
        
        # Verify the file was saved
        self.assertIsNotNone(sample.file)
        self.assertTrue(sample.file.name.startswith('test_file_samples_'))
        
        # Verify the file URL is generated correctly
        self.assertIn('samples', sample.file.url)
        
        print(f"✅ Sample file field test passed: {sample.file.name}")
    
    def test_sample_file_field_optional(self):
        """Test that file field is optional"""
        # Create sample without file
        sample = Sample.objects.create(
            survey=self.survey,
            user=self.user,
            team=self.team,
            latitude="12.9716",
            longitude="77.5946",
            address="Test Address"
        )
        
        # Verify the file field is None
        self.assertIsNone(sample.file)
        
        print("✅ Sample file field optional test passed")
