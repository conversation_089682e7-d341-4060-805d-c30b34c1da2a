"""
Data migration to fix PostgreSQL sequence for QuestionResponse model.

This migration fixes the issue where the PostgreSQL sequence for auto-generating
QuestionResponse IDs gets out of sync with the actual data in the table, causing
"duplicate key value violates unique constraint" errors.
"""

from django.db import migrations, connection


def fix_questionresponse_sequence(apps, schema_editor):
    """Fix the PostgreSQL sequence for QuestionResponse primary key."""
    if 'postgresql' not in schema_editor.connection.settings_dict['ENGINE']:
        # Skip for non-PostgreSQL databases
        return

    QuestionResponse = apps.get_model('samples', 'QuestionResponse')
    table_name = QuestionResponse._meta.db_table
    sequence_name = f'{table_name}_id_seq'

    with connection.cursor() as cursor:
        try:
            # Get the maximum ID from the table
            cursor.execute(f'SELECT MAX(id) FROM {table_name}')
            max_id_result = cursor.fetchone()
            max_id = max_id_result[0] if max_id_result[0] is not None else 0

            # Get the current sequence value
            cursor.execute(f"SELECT last_value FROM {sequence_name}")
            current_seq_result = cursor.fetchone()
            current_seq = current_seq_result[0] if current_seq_result else 0

            # Check if sequence needs fixing
            if current_seq <= max_id:
                new_seq_value = max_id + 1
                # Reset the sequence
                cursor.execute(
                    f"ALTER SEQUENCE {sequence_name} RESTART WITH %s",
                    [new_seq_value]
                )
                print(f"Fixed QuestionResponse sequence: reset to {new_seq_value}")
            else:
                print("QuestionResponse sequence is already correct")

        except Exception as e:
            print(f"Error fixing QuestionResponse sequence: {str(e)}")
            # Don't raise the exception to avoid breaking the migration
            # The management command can be used as a fallback


def reverse_fix_questionresponse_sequence(apps, schema_editor):
    """Reverse operation - no action needed."""
    pass


class Migration(migrations.Migration):

    dependencies = [
        ('samples', '0002_questionresponse_file'),
    ]

    operations = [
        migrations.RunPython(
            fix_questionresponse_sequence,
            reverse_fix_questionresponse_sequence
        ),
    ]
