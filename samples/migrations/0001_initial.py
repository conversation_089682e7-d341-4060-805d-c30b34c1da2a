# Generated by Django 5.1.11 on 2025-06-29 05:53

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('companies', '0001_initial'),
        ('surveys', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Sample',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('timestamp', models.DateTimeField(auto_now_add=True)),
                ('start_time', models.DateTimeField(blank=True, null=True, verbose_name='Start Time')),
                ('end_time', models.DateTimeField(blank=True, null=True, verbose_name='End Time')),
                ('latitude', models.CharField(max_length=50, verbose_name='Latitude')),
                ('longitude', models.Char<PERSON>ield(max_length=50, verbose_name='Longitude')),
                ('address', models.TextField(blank=True, null=True, verbose_name='Address')),
                ('device_info', models.JSONField(blank=True, null=True, verbose_name='Device Info')),
                ('audio_url', models.TextField(blank=True, null=True, verbose_name='Audio URL')),
                ('is_offline', models.BooleanField(default=False, verbose_name='Is Offline')),
                ('is_uploaded', models.BooleanField(default=False, verbose_name='Is Uploaded')),
                ('is_qc_done', models.BooleanField(default=False, verbose_name='Is QC Done')),
                ('survey', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='surveys.survey')),
                ('team', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='companies.team')),
                ('user', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Sample',
                'verbose_name_plural': 'Samples',
            },
        ),
        migrations.CreateModel(
            name='QuestionResponse',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('response', models.TextField(blank=True, null=True, verbose_name='Response')),
                ('audio_response', models.TextField(blank=True, null=True, verbose_name='Audio Response URL')),
                ('image_response', models.TextField(blank=True, null=True, verbose_name='Image Response URL')),
                ('response_timestamp', models.DateTimeField(auto_now_add=True)),
                ('question', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='surveys.question')),
                ('selected_options', models.ManyToManyField(blank=True, related_name='responses', to='surveys.questionoption')),
                ('sample', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='responses', to='samples.sample')),
            ],
            options={
                'verbose_name': 'Question Response',
                'verbose_name_plural': 'Question Responses',
                'unique_together': {('sample', 'question')},
            },
        ),
    ]
