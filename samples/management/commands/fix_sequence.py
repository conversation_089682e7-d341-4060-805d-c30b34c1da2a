"""
Django management command to fix PostgreSQL sequence issues for auto-incrementing primary keys.

This command fixes the common issue where PostgreSQL sequences get out of sync with the actual
data in the table, causing "duplicate key value violates unique constraint" errors.

Usage:
    python manage.py fix_sequence
    python manage.py fix_sequence --model QuestionResponse
    python manage.py fix_sequence --app samples
"""

from django.core.management.base import BaseCommand, CommandError
from django.db import connection, models
from django.apps import apps
from django.conf import settings


class Command(BaseCommand):
    help = 'Fix PostgreSQL sequence issues for auto-incrementing primary keys'

    def add_arguments(self, parser):
        parser.add_argument(
            '--model',
            type=str,
            help='Specific model name to fix (e.g., QuestionResponse)',
        )
        parser.add_argument(
            '--app',
            type=str,
            help='Specific app to fix all models in (e.g., samples)',
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be done without making changes',
        )

    def handle(self, *args, **options):
        if 'postgresql' not in settings.DATABASES['default']['ENGINE']:
            raise CommandError('This command is only for PostgreSQL databases')

        dry_run = options['dry_run']
        model_name = options['model']
        app_name = options['app']

        if model_name and app_name:
            raise CommandError('Cannot specify both --model and --app options')

        models_to_fix = []

        if model_name:
            # Fix specific model
            try:
                model = apps.get_model('samples', model_name)
                models_to_fix.append(model)
            except LookupError:
                # Try other apps
                for app_config in apps.get_app_configs():
                    try:
                        model = apps.get_model(app_config.label, model_name)
                        models_to_fix.append(model)
                        break
                    except LookupError:
                        continue
                else:
                    raise CommandError(f'Model {model_name} not found')
        elif app_name:
            # Fix all models in specific app
            try:
                app_config = apps.get_app_config(app_name)
                models_to_fix = [model for model in app_config.get_models()]
            except LookupError:
                raise CommandError(f'App {app_name} not found')
        else:
            # Fix all models with auto-incrementing primary keys
            from samples.models import Sample, QuestionResponse
            models_to_fix = [Sample, QuestionResponse]

        self.stdout.write(f'Found {len(models_to_fix)} models to check')

        fixed_count = 0
        for model in models_to_fix:
            if self.fix_model_sequence(model, dry_run):
                fixed_count += 1

        if dry_run:
            self.stdout.write(
                self.style.WARNING(f'DRY RUN: Would fix {fixed_count} models')
            )
        else:
            self.stdout.write(
                self.style.SUCCESS(f'Successfully fixed {fixed_count} models')
            )

    def fix_model_sequence(self, model, dry_run=False):
        """Fix the sequence for a specific model."""
        # Check if model has an auto-incrementing primary key
        pk_field = model._meta.pk
        if not isinstance(pk_field, (models.AutoField, models.BigAutoField)):
            self.stdout.write(f'Skipping {model.__name__}: No auto-incrementing primary key')
            return False

        table_name = model._meta.db_table
        pk_column = pk_field.column

        # Get the sequence name (PostgreSQL convention)
        sequence_name = f'{table_name}_{pk_column}_seq'

        with connection.cursor() as cursor:
            try:
                # Get the maximum ID from the table
                cursor.execute(f'SELECT MAX({pk_column}) FROM {table_name}')
                max_id_result = cursor.fetchone()
                max_id = max_id_result[0] if max_id_result[0] is not None else 0

                # Get the current sequence value
                cursor.execute(f"SELECT last_value FROM {sequence_name}")
                current_seq_result = cursor.fetchone()
                current_seq = current_seq_result[0] if current_seq_result else 0

                self.stdout.write(
                    f'{model.__name__}: max_id={max_id}, current_seq={current_seq}'
                )

                # Check if sequence needs fixing
                if current_seq <= max_id:
                    new_seq_value = max_id + 1
                    
                    if dry_run:
                        self.stdout.write(
                            self.style.WARNING(
                                f'  Would reset {sequence_name} to {new_seq_value}'
                            )
                        )
                    else:
                        # Reset the sequence
                        cursor.execute(
                            f"ALTER SEQUENCE {sequence_name} RESTART WITH %s",
                            [new_seq_value]
                        )
                        self.stdout.write(
                            self.style.SUCCESS(
                                f'  Fixed {sequence_name}: reset to {new_seq_value}'
                            )
                        )
                    return True
                else:
                    self.stdout.write(f'  {model.__name__}: sequence is already correct')
                    return False

            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(
                        f'Error fixing {model.__name__}: {str(e)}'
                    )
                )
                return False
