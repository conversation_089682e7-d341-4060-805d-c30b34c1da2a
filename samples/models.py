import uuid
from django.db import models
from django.db.models import U<PERSON><PERSON>ield, ForeignKey, CASCADE, SET_NULL, DateTimeField, CharField, TextField, BooleanField, JSONField, FileField
from django.utils.translation import gettext_lazy as _
from django.contrib.auth import get_user_model
from auditlog.registry import auditlog
from config.storage_backends import ResponseStorage, SampleStorage

User = get_user_model()


class Sample(models.Model):
    """
    Sample model for individual survey submissions
    """
    id = UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    survey = ForeignKey('surveys.Survey', on_delete=CASCADE)
    user = ForeignKey(User, on_delete=SET_NULL, null=True)
    team = ForeignKey('companies.Team', on_delete=SET_NULL, null=True)
    timestamp = DateTimeField(auto_now_add=True)
    start_time = DateTimeField(_("Start Time"), null=True, blank=True)
    end_time = DateTimeField(_("End Time"), null=True, blank=True)
    latitude = CharField(_("Latitude"), max_length=50)
    longitude = CharField(_("Longitude"), max_length=50)
    address = TextField(_("Address"), null=True, blank=True)
    device_info = JSONField(_("Device Info"), null=True, blank=True)
    audio_url = TextField(_("Audio URL"), null=True, blank=True)
    file = FileField(storage=SampleStorage(), blank=True, null=True)
    is_offline = BooleanField(_("Is Offline"), default=False)
    is_uploaded = BooleanField(_("Is Uploaded"), default=False)
    is_qc_done = BooleanField(_("Is QC Done"), default=False)
    
    def __str__(self):
        return f"{self.survey.title} - {self.id} ({self.timestamp})"
    
    class Meta:
        verbose_name = _("Sample")
        verbose_name_plural = _("Samples")


class QuestionResponse(models.Model):
    """
    QuestionResponse model for capturing per-question answers
    """
    sample = ForeignKey(Sample, on_delete=CASCADE, related_name='responses')
    question = ForeignKey('surveys.Question', on_delete=CASCADE)
    response = TextField(_("Response"), null=True, blank=True)
    selected_options = models.ManyToManyField(
        'surveys.QuestionOption',
        blank=True,
        related_name='responses'
    )
    audio_response = TextField(_("Audio Response URL"), null=True, blank=True)
    image_response = TextField(_("Image Response URL"), null=True, blank=True)
    file = FileField(storage=ResponseStorage(), blank=True, null=True)
    response_timestamp = DateTimeField(auto_now_add=True)
    
    def __str__(self):
        return f"{self.sample.id} - {self.question.question_text[:30]}"
    
    class Meta:
        verbose_name = _("Question Response")
        verbose_name_plural = _("Question Responses")
        unique_together = ('sample', 'question')


# Register models for audit logging
# auditlog.register(Sample)
# auditlog.register(QuestionResponse)
