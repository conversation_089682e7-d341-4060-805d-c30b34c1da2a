import uuid
import logging
from django.db import models, IntegrityError
from django.db.models import UUIDField, ForeignKey, CASCADE, SET_NULL, DateTimeField, CharField, TextField, BooleanField, JSONField, FileField
from django.utils.translation import gettext_lazy as _
from django.contrib.auth import get_user_model
from auditlog.registry import auditlog
from config.storage_backends import ResponseStorage, SampleStorage

logger = logging.getLogger(__name__)

User = get_user_model()


class QuestionResponseManager(models.Manager):
    """
    Custom manager for QuestionResponse that handles sequence issues automatically.
    """

    def create(self, **kwargs):
        """
        Override create to handle sequence issues automatically.
        """
        max_retries = 3
        for attempt in range(max_retries):
            try:
                return super().create(**kwargs)
            except IntegrityError as e:
                if 'duplicate key value violates unique constraint' in str(e) and 'pkey' in str(e):
                    logger.warning(f"QuestionResponse sequence issue detected (attempt {attempt + 1}): {str(e)}")
                    # Try to fix the sequence
                    if self.model.fix_sequence():
                        logger.info("QuestionResponse sequence fixed, retrying...")
                        continue
                    else:
                        logger.error("Failed to fix QuestionResponse sequence")
                # Re-raise the error if it's not a sequence issue or we couldn't fix it
                raise

        # If we get here, all retries failed
        raise IntegrityError("Failed to create QuestionResponse after fixing sequence")


class Sample(models.Model):
    """
    Sample model for individual survey submissions
    """
    id = UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    survey = ForeignKey('surveys.Survey', on_delete=CASCADE)
    user = ForeignKey(User, on_delete=SET_NULL, null=True)
    team = ForeignKey('companies.Team', on_delete=SET_NULL, null=True)
    timestamp = DateTimeField(auto_now_add=True)
    start_time = DateTimeField(_("Start Time"), null=True, blank=True)
    end_time = DateTimeField(_("End Time"), null=True, blank=True)
    latitude = CharField(_("Latitude"), max_length=50)
    longitude = CharField(_("Longitude"), max_length=50)
    address = TextField(_("Address"), null=True, blank=True)
    device_info = JSONField(_("Device Info"), null=True, blank=True)
    audio_url = TextField(_("Audio URL"), null=True, blank=True)
    file = FileField(storage=SampleStorage(), blank=True, null=True)
    is_offline = BooleanField(_("Is Offline"), default=False)
    is_uploaded = BooleanField(_("Is Uploaded"), default=False)
    is_qc_done = BooleanField(_("Is QC Done"), default=False)
    
    def __str__(self):
        return f"{self.survey.title} - {self.id} ({self.timestamp})"
    
    class Meta:
        verbose_name = _("Sample")
        verbose_name_plural = _("Samples")


class QuestionResponse(models.Model):
    """
    QuestionResponse model for capturing per-question answers
    """
    sample = ForeignKey(Sample, on_delete=CASCADE, related_name='responses')
    question = ForeignKey('surveys.Question', on_delete=CASCADE)
    response = TextField(_("Response"), null=True, blank=True)
    selected_options = models.ManyToManyField(
        'surveys.QuestionOption',
        blank=True,
        related_name='responses'
    )
    audio_response = TextField(_("Audio Response URL"), null=True, blank=True)
    image_response = TextField(_("Image Response URL"), null=True, blank=True)
    file = FileField(storage=ResponseStorage(), blank=True, null=True)
    response_timestamp = DateTimeField(auto_now_add=True)

    # Use custom manager
    objects = QuestionResponseManager()

    def __str__(self):
        return f"{self.sample.id} - {self.question.question_text[:30]}"

    @classmethod
    def fix_sequence(cls):
        """
        Fix PostgreSQL sequence for this model's primary key.

        This method fixes the common issue where PostgreSQL sequences get out of sync
        with the actual data in the table, causing "duplicate key value violates unique
        constraint" errors.

        Returns:
            bool: True if sequence was fixed, False if no fix was needed
        """
        from django.db import connection
        from django.conf import settings

        if 'postgresql' not in settings.DATABASES['default']['ENGINE']:
            return False

        table_name = cls._meta.db_table
        sequence_name = f'{table_name}_id_seq'

        with connection.cursor() as cursor:
            try:
                # Get the maximum ID from the table
                cursor.execute(f'SELECT MAX(id) FROM {table_name}')
                max_id_result = cursor.fetchone()
                max_id = max_id_result[0] if max_id_result[0] is not None else 0

                # Get the current sequence value
                cursor.execute(f"SELECT last_value FROM {sequence_name}")
                current_seq_result = cursor.fetchone()
                current_seq = current_seq_result[0] if current_seq_result else 0

                # Check if sequence needs fixing
                if current_seq <= max_id:
                    new_seq_value = max_id + 1
                    # Reset the sequence
                    cursor.execute(
                        f"ALTER SEQUENCE {sequence_name} RESTART WITH %s",
                        [new_seq_value]
                    )
                    return True
                return False
            except Exception:
                return False

    class Meta:
        verbose_name = _("Question Response")
        verbose_name_plural = _("Question Responses")
        unique_together = ('sample', 'question')


# Register models for audit logging
# auditlog.register(Sample)
# auditlog.register(QuestionResponse)
