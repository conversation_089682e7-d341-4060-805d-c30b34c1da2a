from django.contrib import admin
from .models import Sample, QuestionResponse


@admin.register(Sample)
class SampleAdmin(admin.ModelAdmin):
    list_display = ('id', 'survey', 'user', 'team', 'timestamp', 'is_uploaded', 'is_qc_done')
    list_filter = ('is_offline', 'is_uploaded', 'is_qc_done', 'timestamp', 'survey')
    search_fields = ('survey__title', 'user__name', 'user__username', 'address')
    readonly_fields = ('id', 'timestamp', 'start_time', 'end_time')
    fieldsets = (
        ('Basic Information', {
            'fields': ('id', 'survey', 'user', 'team', 'timestamp')
        }),
        ('Location & Device', {
            'fields': ('latitude', 'longitude', 'address', 'device_info')
        }),
        ('Files & Media', {
            'fields': ('audio_url', 'file')
        }),
        ('Status', {
            'fields': ('start_time', 'end_time', 'is_offline', 'is_uploaded', 'is_qc_done')
        }),
    )
    ordering = ('-timestamp',)


@admin.register(QuestionResponse)
class QuestionResponseAdmin(admin.ModelAdmin):
    list_display = ('sample', 'question', 'response_timestamp')
    list_filter = ('question__type', 'question__section__survey', 'response_timestamp')
    search_fields = ('sample__id', 'question__question_text', 'response')
    readonly_fields = ('response_timestamp',)
    ordering = ('-response_timestamp',)
