"""
Test settings for Voter-Backend.

This file contains settings for running tests.
"""

import os
from .base import *  # noqa

# Use in-memory SQLite database for tests (faster and no external dependencies)
DATABASES = {
    "default": {
        "ENGINE": "django.db.backends.sqlite3",
        "NAME": ":memory:",
    }
}

# Use console email backend for tests
EMAIL_BACKEND = "django.core.mail.backends.console.EmailBackend"

# Use faster password hashing for tests (but still secure)
PASSWORD_HASHERS = [
    "django.contrib.auth.hashers.PBKDF2PasswordHasher",
]

# Use fast cache backend for tests
CACHES = {
    "default": {
        "BACKEND": "django.core.cache.backends.locmem.LocMemCache",
    }
}

# Disable logging during tests
LOGGING = {
    "version": 1,
    "disable_existing_loggers": True,
    "handlers": {
        "null": {
            "class": "logging.NullHandler",
        },
    },
    "root": {
        "handlers": ["null"],
    },
}

# Use test secret key
SECRET_KEY = "test-secret-key-for-testing-only"

# Disable debug mode for tests
DEBUG = False

# Use test allowed hosts
ALLOWED_HOSTS = ["testserver"]

# Disable CSRF for API tests
MIDDLEWARE = [m for m in MIDDLEWARE if "csrf" not in m.lower()]

# Use test media settings
MEDIA_ROOT = os.path.join(BASE_DIR, "test_media")
MEDIA_URL = "/test_media/"

# Disable static files collection during tests
STATICFILES_STORAGE = "django.contrib.staticfiles.storage.StaticFilesStorage"

# Use test OTP settings
OTP_EXPIRY_MINUTES = 5
OTP_LENGTH = 6

# Disable audit logging during tests for performance
AUDITLOG_DISABLE_ON_REQUEST = True

# Use test REST framework settings
REST_FRAMEWORK = {
    "DEFAULT_AUTHENTICATION_CLASSES": [
        "rest_framework.authentication.TokenAuthentication",
    ],
    "DEFAULT_PERMISSION_CLASSES": [
        "rest_framework.permissions.IsAuthenticated",
    ],
    "DEFAULT_RENDERER_CLASSES": [
        "rest_framework.renderers.JSONRenderer",
    ],
    "DEFAULT_PAGINATION_CLASS": "rest_framework.pagination.PageNumberPagination",
    "PAGE_SIZE": 10,
    "TEST_REQUEST_DEFAULT_FORMAT": "json",
    "DEFAULT_SCHEMA_CLASS": "drf_spectacular.openapi.AutoSchema",
}

# Disable drf-spectacular during tests
SPECTACULAR_SETTINGS = {
    "TITLE": "Voter-Backend API",
    "DESCRIPTION": "API for Voter-Backend",
    "VERSION": "1.0.0",
    "SERVE_INCLUDE_SCHEMA": False,
}

# Test-specific settings
TEST_RUNNER = "django.test.runner.DiscoverRunner"

# Disable migrations for faster tests
MIGRATION_MODULES = {
    'sites': None,
    'voter.contrib.sites': None,
}

# Disable migrations during tests for faster execution
class DisableMigrations:
    def __contains__(self, item):
        return True

    def __getitem__(self, item):
        return None

MIGRATION_MODULES = DisableMigrations()
