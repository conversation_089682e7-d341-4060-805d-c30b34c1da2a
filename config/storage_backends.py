from django.conf import settings
from django.core.files.storage import FileSystemStorage
import os
import random
import string
import re

class BaseCustomStorage(FileSystemStorage):
    """Base storage class with common functionality"""
    
    def __init__(self, subdirectory, *args, **kwargs):
        # <PERSON><PERSON> missing THIS_SERVER setting gracefully
        try:
            self.dynamic_domain = f'http://{settings.THIS_SERVER}/'
        except AttributeError:
            self.dynamic_domain = 'http://localhost:8000/'
        
        self.subdirectory = subdirectory
        self.base_path = f'localmedia/media/surveys/{subdirectory}/'
        self.save_path = f'media/surveys/{subdirectory}/'
        super().__init__(*args, **kwargs)

    def url(self, name):
        """Generate proper URL for the file"""
        try:
            # Always construct the URL using the media path structure
            # Files are saved to media/surveys/{subdirectory}/ but served from media/surveys/{subdirectory}/
            clean_name = name.lstrip('/')
            return f'{self.dynamic_domain}localmedia/media/surveys/{self.subdirectory}/{clean_name}'
        except Exception as e:
            # Fallback to parent implementation
            return super().url(name)

    def exists(self, name):
        return os.path.exists(os.path.join(self.save_path, name))

    def _save(self, name, content):
        try:
            directory = os.path.dirname(os.path.join(self.save_path, name))
            os.makedirs(directory, exist_ok=True)
            
            file_path = os.path.join(self.save_path, name)
            with open(file_path, 'wb') as f:
                f.write(content.read())
            return name
        except Exception as e:
            raise

class OptionImageStorage(BaseCustomStorage):
    def __init__(self, *args, **kwargs):
        super().__init__('options', *args, **kwargs)
        
class QuestionStorage(BaseCustomStorage):
    def __init__(self, *args, **kwargs):
        super().__init__('questions', *args, **kwargs)
        
class ResponseStorage(BaseCustomStorage):
    def __init__(self, *args, **kwargs):
        super().__init__('responses', *args, **kwargs)
        
class SampleStorage(BaseCustomStorage):
    def __init__(self, *args, **kwargs):
        super().__init__('samples', *args, **kwargs)
        
class SurveyImageStorage(BaseCustomStorage):
    def __init__(self, *args, **kwargs):
        super().__init__('images', *args, **kwargs)
        
def _generate_image_name(file_obj, folder):
    """Generate a unique image name based on company ID and current date."""
    igname, extension = os.path.splitext(file_obj.name)
    
    # Remove any brackets or undesired characters from the filename
    igname = re.sub(r'[()\[\]{} ]', '', igname)
    
    random_string = ''.join(random.choice(string.ascii_letters + string.digits) for _ in range(10))
    return f'{igname}_{folder}_{random_string}{extension}'
    
def handle_file_storage(validated_data, file_obj, folder, field_type):
    """Handles image storage, renaming, and saving."""
    storage_map = {
        'options': OptionImageStorage(),
        'images': SurveyImageStorage(),
        'questions': QuestionStorage(),
        'responses': ResponseStorage(),
        'samples': SampleStorage()
    }

    try:
        if not file_obj:
            return False, "No file object provided"
        
        if not hasattr(file_obj, 'name'):
            return False, "Invalid file object - no name attribute"
        
        if not hasattr(file_obj, 'read'):
            return False, "Invalid file object - no read method"
        
        storage = storage_map.get(folder)
        if not storage:
            return False, f"No storage configured for folder: {folder}"
        
        filename = _generate_image_name(file_obj, folder)
        file_obj.name = filename
        
        # Save file once using storage
        saved_name = storage.save(filename, file_obj)
        
        # Update validated_data with the saved file path
        validated_data[field_type] = saved_name
        return True, "File uploaded successfully"

    except Exception as e:
        return False, f"Image upload failed: {str(e)}"