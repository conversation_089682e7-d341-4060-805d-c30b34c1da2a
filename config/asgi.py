"""
ASGI config for Voter-Backend project.

This module contains the ASGI application used for deploying Django with ASGI servers like Uvicorn or Daphne.
It should expose a module-level variable named ``application``.

"""

import os
import sys
from pathlib import Path
from django.core.asgi import get_asgi_application

BASE_DIR = Path(__file__).resolve(strict=True).parent.parent
sys.path.append(str(BASE_DIR / "voter"))

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings.production")

application = get_asgi_application() 