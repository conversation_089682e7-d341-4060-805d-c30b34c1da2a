from django.conf import settings
from rest_framework.routers import <PERSON><PERSON>ult<PERSON>out<PERSON>
from rest_framework.routers import SimpleRouter
from django.urls import path, include

from voter.users.api.views import (
    UserViewSet, send_otp, verify_otp, web_login, LogoutView, ForceLogoutAllDevicesView, 
    check_token_validity, get_user_permissions, get_users_by_role,
    create_user, update_user
)

from companies.api.views import CompanyViewSet, TeamViewSet, TeamUserViewSet, AuditLogViewSet

# Import new app viewsets
from surveys.api.views import SurveyViewSet, SectionViewSet, QuestionViewSet, QuestionOptionViewSet
from assignments.api.views import (
    SurveyAssignmentViewSet, AssignmentTargetViewSet, DailyTargetViewSet, 
    PerformanceReportViewSet, DutyLogViewSet
)
from samples.api.views import SampleViewSet, QuestionResponseViewSet
from qc.api.views import QCScoreViewSet
from voter.contrib.api_views import (
    DashboardOverviewView, SurveyAnalyticsView, SampleAnalyticsView,
    UserAnalyticsView, AssignmentAnalyticsView, QCAnalyticsView,
    CompanyAnalyticsView, QuestionAnalyticsView, RealtimeStatsView,
    ComprehensiveDashboardView, dashboard_data
)

router = DefaultRouter() if settings.DEBUG else SimpleRouter()

router.register("users", UserViewSet, basename="user")
router.register("companies", CompanyViewSet)
router.register("teams", TeamViewSet)
router.register("team-users", TeamUserViewSet)
router.register("audit-logs", AuditLogViewSet)

# Register new app viewsets
router.register("surveys", SurveyViewSet)
router.register("sections", SectionViewSet)
router.register("questions", QuestionViewSet)
router.register("question-options", QuestionOptionViewSet)

router.register("survey-assignments", SurveyAssignmentViewSet)
router.register("assignment-targets", AssignmentTargetViewSet)
router.register("daily-targets", DailyTargetViewSet)
router.register("performance-reports", PerformanceReportViewSet)
router.register("duty-logs", DutyLogViewSet)

router.register("samples", SampleViewSet)
router.register("question-responses", QuestionResponseViewSet)

router.register("qc-scores", QCScoreViewSet)

app_name = "api"
urlpatterns = [
    # Analytics endpoints
    path("analytics/overview/", DashboardOverviewView.as_view(), name="analytics-overview"),
    path("analytics/surveys/", SurveyAnalyticsView.as_view(), name="analytics-surveys"),
    path("analytics/samples/", SampleAnalyticsView.as_view(), name="analytics-samples"),
    path("analytics/users/", UserAnalyticsView.as_view(), name="analytics-users"),
    path("analytics/assignments/", AssignmentAnalyticsView.as_view(), name="analytics-assignments"),
    path("analytics/qc/", QCAnalyticsView.as_view(), name="analytics-qc"),
    path("analytics/companies/", CompanyAnalyticsView.as_view(), name="analytics-companies"),
    path("analytics/questions/", QuestionAnalyticsView.as_view(), name="analytics-questions"),
    path("analytics/realtime/", RealtimeStatsView.as_view(), name="analytics-realtime"),
    path("analytics/dashboard/", ComprehensiveDashboardView.as_view(), name="analytics-dashboard"),
    path("analytics/", dashboard_data, name="analytics-comprehensive"),
    
    # Authentication endpoints
    # Authentication endpoints
    path("auth/send-otp/", send_otp, name="send-otp"),
    path("auth/verify-otp/", verify_otp, name="verify-otp"),
    path("auth/web-login/", web_login, name="web-login"),
    path("auth/logout/", LogoutView.as_view(), name="logout"),
    path("auth/force-logout-all/", ForceLogoutAllDevicesView.as_view(), name="force-logout-all"),
    path("auth/check-token/", check_token_validity, name="check-token"),
    
    # Role and permission endpoints
    path("auth/permissions/", get_user_permissions, name="user-permissions"),
    path("users/by-role/", get_users_by_role, name="users-by-role"),
    path("users/create/", create_user, name="create-user"),
    path("users/<int:user_id>/update/", update_user, name="update-user"),
    # Companies URLs are now registered directly in the router above
] + router.urls
