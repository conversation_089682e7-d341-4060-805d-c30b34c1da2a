from rest_framework import serializers
from ..models import SurveyAssignment, AssignmentTarget, DailyTarget, PerformanceReport, DutyLog
from companies.models import Team, TeamUser
from django.contrib.auth import get_user_model

User = get_user_model()


class DailyTargetSerializer(serializers.ModelSerializer):
    performance_status = serializers.CharField(read_only=True)
    
    class Meta:
        model = DailyTarget
        fields = '__all__'
        read_only_fields = ('created_at', 'updated_at')


class AssignmentTargetSerializer(serializers.ModelSerializer):
    question_text = serializers.CharField(source='question.question_text', read_only=True)
    option_text = serializers.CharField(source='option.option_text', read_only=True)
    
    class Meta:
        model = AssignmentTarget
        fields = '__all__'
        read_only_fields = ('taken_at', 'last_synced_at')


class PerformanceReportSerializer(serializers.ModelSerializer):
    class Meta:
        model = PerformanceReport
        fields = '__all__'
        read_only_fields = ('created_at',)


class SurveyAssignmentSerializer(serializers.ModelSerializer):
    targets = AssignmentTargetSerializer(many=True, read_only=True)
    daily_targets = DailyTargetSerializer(many=True, read_only=True)
    performance_reports = PerformanceReportSerializer(many=True, read_only=True)
    survey_title = serializers.CharField(source='survey.title', read_only=True)
    assignee_name = serializers.CharField(source='assignee.name', read_only=True)
    assigned_by_name = serializers.CharField(source='assigned_by.name', read_only=True)
    
    # Progress fields
    daily_progress = serializers.SerializerMethodField()
    overall_progress = serializers.SerializerMethodField()
    performance_summary = serializers.SerializerMethodField()
    
    class Meta:
        model = SurveyAssignment
        fields = '__all__'
        read_only_fields = ('assigned_at',)
    
    def get_daily_progress(self, obj):
        """Get today's progress"""
        return obj.get_daily_progress()
    
    def get_overall_progress(self, obj):
        """Get overall progress"""
        return obj.get_overall_progress()
    
    def get_performance_summary(self, obj):
        """Get performance summary for last 7 days"""
        return obj.get_performance_summary()


class DailyTargetCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating daily targets"""
    
    class Meta:
        model = DailyTarget
        fields = ['assignment', 'date', 'target_samples', 'notes']


class PerformanceReportCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating performance reports"""
    
    class Meta:
        model = PerformanceReport
        fields = ['assignment', 'report_date', 'total_target', 'total_completed', 'daily_average', 'completion_rate', 'days_on_track', 'days_behind', 'notes']


class AssignmentProgressSerializer(serializers.ModelSerializer):
    """Serializer for assignment progress tracking"""
    survey_title = serializers.CharField(source='survey.title', read_only=True)
    assignee_name = serializers.CharField(source='assignee.name', read_only=True)
    daily_progress = serializers.SerializerMethodField()
    overall_progress = serializers.SerializerMethodField()
    performance_summary = serializers.SerializerMethodField()
    
    class Meta:
        model = SurveyAssignment
        fields = ['id', 'survey_title', 'assignee_name', 'date', 'status', 'daily_progress', 'overall_progress', 'performance_summary']
    
    def get_daily_progress(self, obj):
        return obj.get_daily_progress()
    
    def get_overall_progress(self, obj):
        return obj.get_overall_progress()
    
    def get_performance_summary(self, obj):
        return obj.get_performance_summary()


class DutyLogSerializer(serializers.ModelSerializer):
    """Simple serializer for DutyLog model"""
    user_name = serializers.CharField(source='user.name', read_only=True)
    
    class Meta:
        model = DutyLog
        fields = [
            'id', 'user', 'user_name', 'date', 'status', 'timestamp',
            'location', 'latitude', 'longitude', 'device_info'
        ]
        read_only_fields = ['id', 'date', 'timestamp'] 


class BulkAssignmentSerializer(serializers.Serializer):
    """Serializer for bulk assignment requests"""
    survey_id = serializers.IntegerField(help_text="ID of the survey to assign")
    date = serializers.DateField(help_text="Assignment date (YYYY-MM-DD)")
    role = serializers.ChoiceField(
        choices=[("Surveyor", "Surveyor"), ("VendorSurveyor", "Vendor Surveyor")],
        help_text="Role for the assignment"
    )
    daily_target_samples = serializers.IntegerField(
        min_value=0, 
        default=0,
        help_text="Daily target samples"
    )
    total_target_samples = serializers.IntegerField(
        min_value=0, 
        default=0,
        help_text="Total target samples"
    )
    is_one_time = serializers.BooleanField(
        default=False,
        help_text="Whether this is a one-time assignment"
    )
    assign_to = serializers.ChoiceField(
        choices=[("team", "Team"), ("users", "Users")],
        help_text="Whether to assign to a team or individual users"
    )
    team_id = serializers.IntegerField(
        required=False,
        help_text="Team ID (required when assign_to is 'team')"
    )
    user_ids = serializers.ListField(
        child=serializers.IntegerField(),
        required=False,
        help_text="List of user IDs (required when assign_to is 'users')"
    )
    
    mobile_numbers = serializers.ListField(
        child=serializers.CharField(),
        required=False,
        help_text="List of mobile numbers (required when assign_to is 'users')"
    )
    notes = serializers.CharField(
        max_length=500,
        required=False,
        allow_blank=True,
        help_text="Optional notes for the assignment"
    )
    
    def validate(self, data):
        """Custom validation for bulk assignment"""
        assign_to = data.get('assign_to')
        team_id = data.get('team_id')
        user_ids = data.get('user_ids', [])
        mobile_numbers = data.get('mobile_numbers', [])
        
        if assign_to == 'team':
            if not team_id:
                raise serializers.ValidationError("team_id is required when assign_to is 'team'")
            
            # Validate team exists
            try:
                team = Team.objects.get(id=team_id, is_deleted=False)
                data['team'] = team
            except Team.DoesNotExist:
                raise serializers.ValidationError("Team not found")
            
            # Check if team has members
            team_users = TeamUser.objects.filter(team=team)
            active_users = [team_user.user for team_user in team_users if not team_user.user.is_deleted]
            
            if not active_users:
                raise serializers.ValidationError("No active users found in the team")
            
            data['users_to_assign'] = active_users
            
        elif assign_to == 'users':
            if not user_ids and not mobile_numbers:
                raise serializers.ValidationError("user_ids or mobile_numbers is required when assign_to is 'users'")
            
            # Validate users exist
            if user_ids:
                users = User.objects.filter(id__in=user_ids, is_deleted=False)
                if len(users) != len(user_ids):
                    found_ids = [user.id for user in users]
                    missing_ids = [uid for uid in user_ids if uid not in found_ids]
                    raise serializers.ValidationError(f"Users not found: {missing_ids}")
                
            if mobile_numbers:
                users = User.objects.filter(mobile_number__in=mobile_numbers, is_deleted=False)
                if len(users) != len(mobile_numbers):
                    found_numbers = [user.mobile_number for user in users]
                    missing_numbers = [mn for mn in mobile_numbers if mn not in found_numbers]
                    raise serializers.ValidationError(f"Users not found: {missing_numbers}")
            
            data['users_to_assign'] = list(users)
        
        return data 