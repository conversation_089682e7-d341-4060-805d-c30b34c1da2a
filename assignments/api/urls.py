from django.urls import path, include
from rest_framework.routers import <PERSON>fault<PERSON>out<PERSON>
from .views import (
    SurveyAssignmentViewSet, AssignmentTargetViewSet, 
    DailyTargetViewSet, PerformanceReportViewSet, DutyLogViewSet
)

# Note: These viewsets are now registered in the main API router (config/api_router.py)
# This file is kept for potential future use but URLs are handled centrally

urlpatterns = [
    # All endpoints are now available at the root API level:
    # /api/duty-logs/log-login/
    # /api/duty-logs/log-logout/
    # /api/duty-logs/my-logs/
    # /api/survey-assignments/
    # /api/daily-targets/
    # /api/performance-reports/
] 