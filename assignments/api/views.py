from rest_framework import viewsets, status
from rest_framework.permissions import IsAuthenticated
from rest_framework.decorators import action
from rest_framework.response import Response
from django.utils import timezone
from django.db.models import Sum, Q, F
from django.db import transaction
from ..models import SurveyAssignment, AssignmentTarget, DailyTarget, PerformanceReport, DutyLog
from ..services import AssignmentTrackingService
from .serializers import (
    SurveyAssignmentSerializer, AssignmentTargetSerializer, 
    DailyTargetSerializer, PerformanceReportSerializer,
    DailyTargetCreateSerializer, PerformanceReportCreateSerializer,
    AssignmentProgressSerializer, DutyLogSerializer, BulkAssignmentSerializer
)
from voter.contrib.audit import AuditLogMixin, SoftDeleteAuditMixin
from voter.contrib.pagination import PaginatedViewSetMixin
from companies.models import Team, TeamUser
from django.contrib.auth import get_user_model
from voter.users.permissions import (
    IsAdmin, IsCompanyAdmin, IsAdminOrCompanyAdmin, IsSurveyor, 
    IsVendorSurveyor, IsQCReviewer, IsSurveyorOrVendorSurveyor,
    CanManageSurveys, CanCollectSamples, CanReviewSamples
)

User = get_user_model()


class SurveyAssignmentViewSet(PaginatedViewSetMixin, SoftDeleteAuditMixin, viewsets.ModelViewSet):
    queryset = SurveyAssignment.objects.filter(is_deleted=False)
    serializer_class = SurveyAssignmentSerializer
    permission_classes = [IsAuthenticated]
    search_fields = ['survey__title', 'assigned_by__name', 'assignee__name']
    filterset_fields = ['survey', 'assigned_by', 'assignee', 'status']
    date_filter_field = 'assigned_at'
    ordering_fields = ['assigned_at', 'survey__title', 'assignee__name']
    
    def get_permissions(self):
        """Return appropriate permissions based on action"""
        if self.action in ['create', 'update', 'partial_update', 'destroy', 'bulk_assign', 'assign_team', 'assign_users', 'remove_assignment', 'remove_assignment_by_id', 'remove_survey_assignments']:
            return [IsAuthenticated(), IsAdminOrCompanyAdmin()]
        elif self.action in ['update_progress']:
            return [IsAuthenticated(), IsAdminOrCompanyAdmin(), IsSurveyorOrVendorSurveyor()]
        return [IsAuthenticated()]
    
    def get_queryset(self):
        """Filter queryset based on user role and permissions"""
        queryset = SurveyAssignment.objects.filter(is_deleted=False)
        
        # Admin can see all assignments
        if self.request.user.is_admin():
            return queryset
        
        # Company Admin can see assignments from their company
        if self.request.user.is_company_admin():
            return queryset.filter(assignee__company=self.request.user.company)
        
        # Surveyor and Vendor Surveyor can only see their own assignments
        if self.request.user.is_surveyor() or self.request.user.is_vendor_surveyor():
            return queryset.filter(assignee=self.request.user)
        
        # QC Reviewer can see assignments from their company
        if self.request.user.is_qc_reviewer():
            return queryset.filter(assignee__company=self.request.user.company)
        
        # Default: return empty queryset for unknown roles
        return queryset.none()
    
    def perform_create(self, serializer):
        """Override to log assignment creation with improved audit logging."""
        assignment = serializer.save(assigned_by=self.request.user)
        
        # Get additional context data
        additional_data = self._get_additional_audit_data('create', assignment, serializer.validated_data)
        
        # Create audit log
        self._create_audit_log(
            action=LogEntry.Action.CREATE,
            instance=assignment,
            additional_data=additional_data,
            operation_context='assignment_creation'
        )
        
        return assignment
    
    def perform_update(self, serializer):
        """Override to log assignment updates with improved change detection."""
        # Get the instance before saving to capture old values
        assignment = serializer.instance
        
        # Save the instance with new data
        updated_assignment = serializer.save()
        
        # Get changes by comparing old and new values
        changes = self._get_changes(assignment, serializer.validated_data)
        
        # Get additional context data
        additional_data = self._get_additional_audit_data('update', updated_assignment, serializer.validated_data)
        
        # Create audit log
        self._create_audit_log(
            action=LogEntry.Action.UPDATE,
            instance=updated_assignment,
            changes=changes,
            additional_data=additional_data,
            operation_context='assignment_update'
        )
        
        return updated_assignment
    
    def _get_additional_audit_data(self, action, instance, validated_data):
        """Add custom audit data for assignment operations."""
        additional_data = {
            'action_source': 'api',
            'survey_title': getattr(instance.survey, 'title', None) if hasattr(instance, 'survey') else None,
            'assignee_name': getattr(instance.assignee, 'name', None) if hasattr(instance, 'assignee') else None,
        }
        
        if action == 'create':
            additional_data['created_via'] = 'api'
        elif action == 'update':
            additional_data['updated_fields'] = list(validated_data.keys())
        elif action == 'soft_delete':
            additional_data['deleted_via'] = 'soft_delete'
        
        return additional_data
    
    @action(detail=True, methods=['get'], url_path='progress')
    def get_progress(self, request, pk=None):
        """Get detailed progress for an assignment"""
        assignment = self.get_object()
        serializer = AssignmentProgressSerializer(assignment)
        return Response(serializer.data)
    
    @action(detail=True, methods=['get'], url_path='daily-progress')
    def get_daily_progress(self, request, pk=None):
        """Get daily progress for an assignment"""
        assignment = self.get_object()
        date_str = request.query_params.get('date')
        
        if date_str:
            try:
                date = timezone.datetime.strptime(date_str, '%Y-%m-%d').date()
            except ValueError:
                return Response(
                    {'error': 'Invalid date format. Use YYYY-MM-DD'},
                    status=status.HTTP_400_BAD_REQUEST
                )
        else:
            date = timezone.now().date()
        
        progress = assignment.get_daily_progress(date)
        return Response(progress)
    
    @action(detail=True, methods=['get'], url_path='performance-summary')
    def get_performance_summary(self, request, pk=None):
        """Get performance summary for an assignment"""
        assignment = self.get_object()
        start_date_str = request.query_params.get('start_date')
        end_date_str = request.query_params.get('end_date')
        
        start_date = None
        end_date = None
        
        if start_date_str:
            try:
                start_date = timezone.datetime.strptime(start_date_str, '%Y-%m-%d').date()
            except ValueError:
                return Response(
                    {'error': 'Invalid start_date format. Use YYYY-MM-DD'},
                    status=status.HTTP_400_BAD_REQUEST
                )
        
        if end_date_str:
            try:
                end_date = timezone.datetime.strptime(end_date_str, '%Y-%m-%d').date()
            except ValueError:
                return Response(
                    {'error': 'Invalid end_date format. Use YYYY-MM-DD'},
                    status=status.HTTP_400_BAD_REQUEST
                )
        
        summary = assignment.get_performance_summary(start_date, end_date)
        return Response(summary)
    
    @action(detail=False, methods=['get'], url_path='my-assignments')
    def my_assignments(self, request):
        """Get current user's assignments with progress"""
        queryset = self.get_queryset().filter(assignee=request.user)
        serializer = AssignmentProgressSerializer(queryset, many=True)
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'], url_path='update-progress')
    def update_progress(self, request, pk=None):
        """Manually update assignment progress (for offline sync)"""
        assignment = self.get_object()
        sample_count = request.data.get('sample_count', 1)
        
        try:
            sample_count = int(sample_count)
            if sample_count < 0:
                raise ValueError("Sample count cannot be negative")
        except (ValueError, TypeError):
            return Response(
                {'error': 'sample_count must be a positive integer'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Update daily progress
        daily_target = AssignmentTrackingService.update_daily_progress(
            user=assignment.assignee,
            survey=assignment.survey,
            sample_count=sample_count
        )
        
        # Update assignment progress
        updated_assignment = AssignmentTrackingService.update_assignment_progress(
            user=assignment.assignee,
            survey=assignment.survey
        )
        
        # Generate performance report
        if updated_assignment:
            AssignmentTrackingService.generate_performance_report(updated_assignment)
        
        serializer = AssignmentProgressSerializer(updated_assignment or assignment)
        return Response(serializer.data)
    
    @action(detail=True, methods=['get'], url_path='real-time-status')
    def real_time_status(self, request, pk=None):
        """Get real-time status of an assignment"""
        assignment = self.get_object()
        
        # Get today's progress
        today_progress = assignment.get_daily_progress()
        
        # Get overall progress
        overall_progress = assignment.get_overall_progress()
        
        # Get performance status
        performance_status = AssignmentTrackingService.check_daily_target_status(
            user=assignment.assignee,
            survey=assignment.survey
        )
        
        status_data = {
            'assignment_id': assignment.id,
            'survey_title': assignment.survey.title,
            'assignee_name': assignment.assignee.name,
            'status': assignment.status,
            'today_progress': today_progress,
            'overall_progress': overall_progress,
            'performance_status': performance_status,
            'last_updated': timezone.now()
        }
        
        return Response(status_data)

    @action(detail=False, methods=['post'], url_path='bulk-assign')
    def bulk_assign(self, request):
        """
        Bulk assign surveys to users or teams
        
        Request body:
        {
            "survey_id": 1,
            "date": "2024-01-15",
            "role": "Surveyor",
            "daily_target_samples": 10,
            "total_target_samples": 100,
            "is_one_time": false,
            "assign_to": "team" | "users",
            "team_id": 1,  # if assign_to is "team"
            "user_ids": [1, 2, 3],  # if assign_to is "users"
            "mobile_numbers": ["1234567890", "0987654321"]
            "notes": "Optional notes"
        }
        """
        # Validate request data using serializer
        serializer = BulkAssignmentSerializer(data=request.data)
        if not serializer.is_valid():
            return Response({
                'success': False,
                'error': 'Validation failed',
                'details': serializer.errors
            }, status=status.HTTP_400_BAD_REQUEST)
        
        validated_data = serializer.validated_data
        users_to_assign = validated_data['users_to_assign']
        assignment_date = validated_data['date']
        
        # Validate survey exists
        try:
            from surveys.models import Survey
            survey = Survey.objects.get(id=validated_data['survey_id'])
        except Survey.DoesNotExist:
            return Response({
                'success': False,
                'error': 'Survey not found'
            }, status=status.HTTP_404_NOT_FOUND)
        
        # Check for existing assignments and exclude already assigned users
        existing_assignments = SurveyAssignment.objects.filter(
            survey=survey,
            assignee__in=users_to_assign,
            date=assignment_date,
            is_deleted=False
        )
        
        # Get users who are already assigned
        already_assigned_users = [assignment.assignee for assignment in existing_assignments]
        already_assigned_user_ids = [user.id for user in already_assigned_users]
        
        # Filter out already assigned users
        users_to_assign = [user for user in users_to_assign if user.id not in already_assigned_user_ids]
        
        if not users_to_assign:
            return Response({
                'success': False,
                'error': f'All users are already assigned to this survey for date {assignment_date}'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Create assignments
        created_assignments = []
        failed_assignments = []
        
        with transaction.atomic():
            for user in users_to_assign:
                try:
                    assignment = SurveyAssignment.objects.create(
                        survey=survey,
                        assignee=user,
                        role=validated_data['role'],
                        date=assignment_date,
                        is_one_time=validated_data['is_one_time'],
                        daily_target_samples=validated_data['daily_target_samples'],
                        total_target_samples=validated_data['total_target_samples'],
                        assigned_by=request.user
                    )
                    
                    # Create daily target if daily_target_samples > 0
                    if validated_data['daily_target_samples'] > 0:
                        DailyTarget.objects.create(
                            assignment=assignment,
                            date=assignment_date,
                            target_samples=validated_data['daily_target_samples'],
                            notes=validated_data.get('notes', '')
                        )
                    
                    created_assignments.append(assignment)
                    
                except Exception as e:
                    failed_assignments.append({
                        'user': user.name,
                        'error': str(e)
                    })
        
        # Prepare response
        skipped_users = [user.name for user in already_assigned_users] if already_assigned_users else []
        
        response_data = {
            'success': True,
            'message': f'Successfully created {len(created_assignments)} assignments',
            'created_count': len(created_assignments),
            'failed_count': len(failed_assignments),
            'skipped_count': len(already_assigned_users),
            'assign_to': validated_data['assign_to'],
            'team_id': validated_data.get('team_id'),
            'user_ids': validated_data.get('user_ids'),
            'survey_title': survey.title,
            'assignment_date': assignment_date.isoformat(),
            'role': validated_data['role']
        }
        
        if skipped_users:
            response_data['skipped_users'] = skipped_users
            response_data['message'] += f'. Skipped {len(skipped_users)} already assigned user(s)'
        
        if created_assignments:
            # Serialize created assignments
            assignment_serializer = SurveyAssignmentSerializer(created_assignments, many=True)
            response_data['created_assignments'] = assignment_serializer.data
        
        if failed_assignments:
            response_data['failed_assignments'] = failed_assignments
        
        return Response(response_data, status=status.HTTP_201_CREATED)

    @action(detail=False, methods=['get'], url_path='team-members')
    def get_team_members(self, request):
        """
        Get all members of a team for assignment purposes
        
        Query params:
        - team_id: ID of the team
        """
        team_id = request.query_params.get('team_id')
        
        if not team_id:
            return Response({
                'success': False,
                'error': 'team_id is required'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Validate team access based on user role
        if request.user.is_admin():
            # Admin can access any team
            try:
                team = Team.objects.get(id=team_id, is_deleted=False)
            except Team.DoesNotExist:
                return Response({
                    'success': False,
                    'error': 'Team not found'
                }, status=status.HTTP_404_NOT_FOUND)
        else:
            # CompanyAdmin and others are restricted to their company
            try:
                team = Team.objects.get(id=team_id, company=request.user.company, is_deleted=False)
            except Team.DoesNotExist:
                return Response({
                    'success': False,
                    'error': 'Team not found or access denied'
                }, status=status.HTTP_404_NOT_FOUND)
        
        # Get team members
        team_users = TeamUser.objects.filter(team=team)
        members = []
        
        for team_user in team_users:
            user = team_user.user
            if not user.is_deleted:
                members.append({
                    'id': user.id,
                    'name': user.name,
                    'username': user.username,
                    'email': user.email,
                    'role': user.role,
                    'company': user.company.name if user.company else None
                })
        
        return Response({
            'success': True,
            'team': {
                'id': team.id,
                'name': team.name,
                'type': team.type,
                'company': team.company.name
            },
            'members': members,
            'member_count': len(members)
        })

    @action(detail=False, methods=['get'], url_path='available-users')
    def get_available_users(self, request):
        """
        Get available users for assignment (filtered by role and company)
        
        Query params:
        - role: Filter by user role (Surveyor, VendorSurveyor)
        - company_id: Filter by company
        - exclude_assigned: Exclude users already assigned to this survey on this date
        - survey_id: Survey ID for exclusion check
        - date: Date for exclusion check
        """
        role = request.query_params.get('role')
        company_id = request.query_params.get('company_id')
        exclude_assigned = request.query_params.get('exclude_assigned', 'false').lower() == 'true'
        survey_id = request.query_params.get('survey_id')
        date = request.query_params.get('date')
        
        # Base queryset based on user role
        if request.user.is_admin():
            # Admin can see all users
            users = User.objects.filter(is_deleted=False)
        else:
            # CompanyAdmin and others are restricted to their company
            users = User.objects.filter(
                is_deleted=False,
                company=request.user.company
            )
        
        # Filter by role
        if role:
            valid_roles = ['Surveyor', 'VendorSurveyor']
            if role not in valid_roles:
                return Response({
                    'success': False,
                    'error': f'role must be one of: {", ".join(valid_roles)}'
                }, status=status.HTTP_400_BAD_REQUEST)
            users = users.filter(role=role)
        
        # Filter by company (only for admin users)
        if company_id and request.user.is_admin():
            users = users.filter(company_id=company_id)
        
        # Exclude already assigned users
        if exclude_assigned and survey_id and date:
            try:
                assignment_date = timezone.datetime.strptime(date, '%Y-%m-%d').date()
                assigned_user_ids = SurveyAssignment.objects.filter(
                    survey_id=survey_id,
                    date=assignment_date,
                    is_deleted=False
                ).values_list('assignee_id', flat=True)
                users = users.exclude(id__in=assigned_user_ids)
            except ValueError:
                return Response({
                    'success': False,
                    'error': 'Invalid date format. Use YYYY-MM-DD'
                }, status=status.HTTP_400_BAD_REQUEST)
        
        # Serialize users
        available_users = []
        for user in users:
            available_users.append({
                'id': user.id,
                'name': user.name,
                'username': user.username,
                'email': user.email,
                'role': user.role,
                'company': user.company.name if user.company else None
            })
        
        return Response({
            'success': True,
            'users': available_users,
            'count': len(available_users)
        })

    @action(detail=False, methods=['get'], url_path='assignment-dashboard')
    def get_assignment_dashboard(self, request):
        """
        Get assignment dashboard data for drag-and-drop interface
        
        Query params:
        - survey_id: ID of the survey
        """
        survey_id = request.query_params.get('survey_id')
        
        if not survey_id:
            return Response({
                'success': False,
                'error': 'survey_id is required'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Validate survey exists
        try:
            from surveys.models import Survey
            survey = Survey.objects.get(id=survey_id)
        except Survey.DoesNotExist:
            return Response({
                'success': False,
                'error': 'Survey not found'
            }, status=status.HTTP_404_NOT_FOUND)
        
        # Get teams based on user role
        if request.user.is_admin():
            # Admin can see all teams across all companies
            available_teams = Team.objects.filter(
                is_deleted=False,
                type__in=['Survey', 'QC']  # Both survey and QC teams
            )
        else:
            # CompanyAdmin and others are restricted to their company
            user_company = request.user.company
            available_teams = Team.objects.filter(
                company=user_company,
                is_deleted=False,
                type__in=['Survey', 'QC']  # Both survey and QC teams
            )
        
        # Get currently assigned users for this survey (all dates)
        assigned_assignments = SurveyAssignment.objects.filter(
            survey=survey,
            is_deleted=False
        )
        assigned_user_ids = assigned_assignments.values_list('assignee_id', flat=True)
        
        # Categorize teams based on assignment status
        assigned_teams = []
        available_teams_list = []
        
        for team in available_teams:
            team_members = TeamUser.objects.filter(team=team)
            team_member_ids = [tm.user.id for tm in team_members if not tm.user.is_deleted]
            
            if not team_member_ids:
                continue  # Skip teams with no active members
            
            # Check which team members are assigned
            assigned_member_ids = set(team_member_ids) & set(assigned_user_ids)
            unassigned_member_ids = set(team_member_ids) - assigned_member_ids
            
            if assigned_member_ids:
                # Team has some assigned members - show in assigned teams
                assigned_teams.append({
                    'id': team.id,
                    'name': team.name,
                    'type': team.type,
                    'total_members': len(team_member_ids),
                    'assigned_members': len(assigned_member_ids),
                    'unassigned_members': len(unassigned_member_ids),
                    'assigned_member_ids': list(assigned_member_ids),
                    'unassigned_member_ids': list(unassigned_member_ids)
                })
            
            if unassigned_member_ids:
                # Team has some unassigned members - show in available teams
                available_teams_list.append({
                    'id': team.id,
                    'name': team.name,
                    'type': team.type,
                    'total_members': len(team_member_ids),
                    'available_members': len(unassigned_member_ids),
                    'assigned_members': len(assigned_member_ids),
                    'available_member_ids': list(unassigned_member_ids),
                    'assigned_member_ids': list(assigned_member_ids)
                })
        
        # Get all assigned users (both team members and individual users) with date information
        assigned_users = []
        for assignment in assigned_assignments:
            user = assignment.assignee
            # Get overall progress for this assignment
            overall_progress = assignment.get_overall_progress()
            
            # Get sample counts for this user and survey
            from samples.models import Sample
            total_samples = Sample.objects.filter(
                survey=survey,
                user=user
            ).count()
            
            today_samples = Sample.objects.filter(
                survey=survey,
                user=user,
                timestamp__date=timezone.now().date()
            ).count()
            
            assigned_users.append({
                'id': user.id,
                'name': user.name,
                'username': user.username,
                'email': user.email,
                'role': user.role,
                'assignment_id': assignment.id,
                'assignment_date': assignment.date.isoformat(),
                'daily_target_samples': assignment.daily_target_samples,
                'total_target_samples': assignment.total_target_samples,
                'total_completed_samples': overall_progress['total_completed'],
                'completion_percentage': round(overall_progress['percentage'], 2),
                'status': assignment.status,
                'total_samples_collected': total_samples,
                'today_samples_collected': today_samples
            })
        
        # Get all available users based on user role (excluding already assigned users)
        if request.user.is_admin():
            # Admin can see all users across all companies
            available_users = User.objects.filter(
                is_deleted=False,
                role__in=['Surveyor', 'VendorSurveyor', 'QCReviewer']
            ).exclude(
                id__in=assigned_user_ids
            )
        else:
            # CompanyAdmin and others are restricted to their company
            available_users = User.objects.filter(
                company=user_company,
                is_deleted=False,
                role__in=['Surveyor', 'VendorSurveyor', 'QCReviewer']
            ).exclude(
                id__in=assigned_user_ids
            )
        
        available_users_list = []
        for user in available_users:
            available_users_list.append({
                'id': user.id,
                'name': user.name,
                'username': user.username,
                'email': user.email,
                'role': user.role
            })
        
        # Separate QC teams and users from survey teams and users
        qc_available_teams = []
        qc_assigned_teams = []
        survey_available_teams = []
        survey_assigned_teams = []
        qc_available_users = []
        qc_assigned_users = []
        survey_available_users = []
        survey_assigned_users = []
        
        # Categorize teams
        for team in available_teams_list:
            if team['type'] == 'QC':
                qc_available_teams.append(team)
            else:
                survey_available_teams.append(team)
        
        for team in assigned_teams:
            if team['type'] == 'QC':
                qc_assigned_teams.append(team)
            else:
                survey_assigned_teams.append(team)
        
        # Categorize available users (users not assigned to this survey)
        for user in available_users_list:
            if user['role'] == 'QCReviewer':
                qc_available_users.append(user)
            else:
                survey_available_users.append(user)
        
        # Categorize assigned users (users already assigned to this survey)
        for user in assigned_users:
            if user['role'] == 'QCReviewer':
                qc_assigned_users.append(user)
            else:
                survey_assigned_users.append(user)
        
        return Response({
            'success': True,
            'survey': {
                'id': survey.id,
                'title': survey.title,
                'status': survey.status
            },
            'available_teams': survey_available_teams,  # Keep existing field for backward compatibility
            'assigned_teams': survey_assigned_teams,  # Keep existing field for backward compatibility
            'available_users': survey_available_users,  # Keep existing field for backward compatibility
            'assigned_users': survey_assigned_users,  # Keep existing field for backward compatibility
            'qc_available_teams': qc_available_teams,  # New field for QC available teams
            'qc_assigned_teams': qc_assigned_teams,  # New field for QC assigned teams
            'qc_available_users': qc_available_users,  # New field for QC available users
            'qc_assigned_users': qc_assigned_users,  # New field for QC assigned users
            'summary': {
                'total_available_teams': len(survey_available_teams),
                'total_assigned_teams': len(survey_assigned_teams),
                'total_available_users': len(survey_available_users),
                'total_assigned_users': len(survey_assigned_users),
                'total_available_qc_teams': len(qc_available_teams),
                'total_assigned_qc_teams': len(qc_assigned_teams),
                'total_available_qc_users': len(qc_available_users),
                'total_assigned_qc_users': len(qc_assigned_users)
            }
        })

    @action(detail=False, methods=['post'], url_path='assign-team')
    def assign_team(self, request):
        """
        Assign a team to a survey (all team members)
        
        Request body:
        {
            "survey_id": 1,
            "date": "2024-01-15",
            "team_id": 1,
            "daily_target_samples": 10,
            "total_target_samples": 100,
            "is_one_time": false,
            "notes": "Optional notes"
        }
        
        Note: Role is automatically set to each user's actual role
        """
        survey_id = request.data.get('survey_id')
        date = request.data.get('date')
        role = request.data.get('role')
        team_id = request.data.get('team_id')
        daily_target_samples = request.data.get('daily_target_samples', 0)
        total_target_samples = request.data.get('total_target_samples', 0)
        is_one_time = request.data.get('is_one_time', False)
        notes = request.data.get('notes', '')
        
        # Validate required fields
        if not all([survey_id, date, team_id]):
            return Response({
                'success': False,
                'error': 'survey_id, date, and team_id are required'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Validate date format
        try:
            assignment_date = timezone.datetime.strptime(date, '%Y-%m-%d').date()
        except ValueError:
            return Response({
                'success': False,
                'error': 'Invalid date format. Use YYYY-MM-DD'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Validate survey exists
        try:
            from surveys.models import Survey
            survey = Survey.objects.get(id=survey_id)
        except Survey.DoesNotExist:
            return Response({
                'success': False,
                'error': 'Survey not found'
            }, status=status.HTTP_404_NOT_FOUND)
        
        # Validate team exists based on user role
        if request.user.is_admin():
            # Admin can access any team
            try:
                team = Team.objects.get(id=team_id, is_deleted=False)
            except Team.DoesNotExist:
                return Response({
                    'success': False,
                    'error': 'Team not found'
                }, status=status.HTTP_404_NOT_FOUND)
        else:
            # CompanyAdmin and others are restricted to their company
            try:
                team = Team.objects.get(id=team_id, company=request.user.company, is_deleted=False)
            except Team.DoesNotExist:
                return Response({
                    'success': False,
                    'error': 'Team not found or access denied'
                }, status=status.HTTP_404_NOT_FOUND)
        
        # Get team members
        team_users = TeamUser.objects.filter(team=team)
        users_to_assign = [team_user.user for team_user in team_users if not team_user.user.is_deleted]
        
        if not users_to_assign:
            return Response({
                'success': False,
                'error': 'No active users found in the team'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Check for existing assignments and exclude already assigned users
        existing_assignments = SurveyAssignment.objects.filter(
            survey=survey,
            assignee__in=users_to_assign,
            date=assignment_date,
            is_deleted=False
        )
        
        # Get users who are already assigned
        already_assigned_users = [assignment.assignee for assignment in existing_assignments]
        already_assigned_user_ids = [user.id for user in already_assigned_users]
        
        # Filter out already assigned users
        users_to_assign = [user for user in users_to_assign if user.id not in already_assigned_user_ids]
        
        if not users_to_assign:
            return Response({
                'success': False,
                'error': f'All team members are already assigned to this survey for date {date}'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Create assignments
        created_assignments = []
        failed_assignments = []
        
        with transaction.atomic():
            for user in users_to_assign:
                try:
                    assignment = SurveyAssignment.objects.create(
                        survey=survey,
                        assignee=user,
                        role=user.role,
                        date=assignment_date,
                        is_one_time=is_one_time,
                        daily_target_samples=daily_target_samples,
                        total_target_samples=total_target_samples,
                        assigned_by=request.user
                    )
                    
                    # Create daily target if daily_target_samples > 0
                    if daily_target_samples > 0:
                        DailyTarget.objects.create(
                            assignment=assignment,
                            date=assignment_date,
                            target_samples=daily_target_samples,
                            notes=notes
                        )
                    
                    created_assignments.append(assignment)
                    
                except Exception as e:
                    failed_assignments.append({
                        'user': user.name,
                        'error': str(e)
                    })
        
        # Prepare response
        skipped_users = [user.name for user in already_assigned_users] if already_assigned_users else []
        
        response_data = {
            'success': True,
            'message': f'Successfully assigned team {team.name} to survey',
            'created_count': len(created_assignments),
            'failed_count': len(failed_assignments),
            'skipped_count': len(already_assigned_users),
            'team': {
                'id': team.id,
                'name': team.name,
                'total_members': len(users_to_assign) + len(already_assigned_users)
            },
            'survey_title': survey.title,
            'assignment_date': date,
            'note': 'Role is automatically set to user\'s actual role',
            'failed_assignments': failed_assignments if failed_assignments else None
        }
        
        if skipped_users:
            response_data['skipped_users'] = skipped_users
            response_data['message'] += f'. Skipped {len(skipped_users)} already assigned user(s)'
        
        return Response(response_data, status=status.HTTP_201_CREATED)

    @action(detail=False, methods=['post'], url_path='assign-users')
    def assign_users(self, request):
        """
        Assign specific users to a survey
        
        Request body:
        {
            "survey_id": 1,
            "date": "2024-01-15",
            "user_ids": [1, 2, 3],
            "daily_target_samples": 10,
            "total_target_samples": 100,
            "is_one_time": false,
            "notes": "Optional notes"
        }
        
        Note: Role is automatically set to each user's actual role
        """
        survey_id = request.data.get('survey_id')
        date = request.data.get('date')
        role = request.data.get('role')
        user_ids = request.data.get('user_ids', [])
        daily_target_samples = request.data.get('daily_target_samples', 0)
        total_target_samples = request.data.get('total_target_samples', 0)
        is_one_time = request.data.get('is_one_time', False)
        notes = request.data.get('notes', '')
        
        # Validate required fields
        if not all([survey_id, date, user_ids]):
            return Response({
                'success': False,
                'error': 'survey_id, date, and user_ids are required'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        if not isinstance(user_ids, list):
            return Response({
                'success': False,
                'error': 'user_ids must be a list'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Validate date format
        try:
            assignment_date = timezone.datetime.strptime(date, '%Y-%m-%d').date()
        except ValueError:
            return Response({
                'success': False,
                'error': 'Invalid date format. Use YYYY-MM-DD'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Validate survey exists
        try:
            from surveys.models import Survey
            survey = Survey.objects.get(id=survey_id)
        except Survey.DoesNotExist:
            return Response({
                'success': False,
                'error': 'Survey not found'
            }, status=status.HTTP_404_NOT_FOUND)
        
        # Validate users exist based on user role
        if request.user.is_admin():
            # Admin can access any users
            users = User.objects.filter(
                id__in=user_ids,
                is_deleted=False
            )
        else:
            # CompanyAdmin and others are restricted to their company
            users = User.objects.filter(
                id__in=user_ids,
                company=request.user.company,
                is_deleted=False
            )
        
        if len(users) != len(user_ids):
            found_ids = [user.id for user in users]
            missing_ids = [uid for uid in user_ids if uid not in found_ids]
            if request.user.is_admin():
                return Response({
                    'success': False,
                    'error': f'Users not found: {missing_ids}'
                }, status=status.HTTP_404_NOT_FOUND)
            else:
                return Response({
                    'success': False,
                    'error': f'Users not found or access denied: {missing_ids}'
                }, status=status.HTTP_404_NOT_FOUND)
        
        # Check for existing assignments and exclude already assigned users
        existing_assignments = SurveyAssignment.objects.filter(
            survey=survey,
            assignee__in=users,
            date=assignment_date,
            is_deleted=False
        )
        
        # Get users who are already assigned
        already_assigned_users = [assignment.assignee for assignment in existing_assignments]
        already_assigned_user_ids = [user.id for user in already_assigned_users]
        
        # Filter out already assigned users
        users_to_assign = [user for user in users if user.id not in already_assigned_user_ids]
        
        if not users_to_assign:
            return Response({
                'success': False,
                'error': f'All users are already assigned to this survey for date {date}'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Create assignments
        created_assignments = []
        failed_assignments = []
        
        with transaction.atomic():
            for user in users_to_assign:
                try:
                    assignment = SurveyAssignment.objects.create(
                        survey=survey,
                        assignee=user,
                        role=user.role,
                        date=assignment_date,
                        is_one_time=is_one_time,
                        daily_target_samples=daily_target_samples,
                        total_target_samples=total_target_samples,
                        assigned_by=request.user
                    )
                    
                    # Create daily target if daily_target_samples > 0
                    if daily_target_samples > 0:
                        DailyTarget.objects.create(
                            assignment=assignment,
                            date=assignment_date,
                            target_samples=daily_target_samples,
                            notes=notes
                        )
                    
                    created_assignments.append(assignment)
                    
                except Exception as e:
                    failed_assignments.append({
                        'user': user.name,
                        'error': str(e)
                    })
        
        # Prepare response
        skipped_users = [user.name for user in already_assigned_users] if already_assigned_users else []
        
        response_data = {
            'success': True,
            'message': f'Successfully assigned {len(created_assignments)} users to survey',
            'created_count': len(created_assignments),
            'failed_count': len(failed_assignments),
            'skipped_count': len(already_assigned_users),
            'users': [
                {
                    'id': user.id,
                    'name': user.name,
                    'username': user.username
                } for user in users_to_assign
            ],
            'survey_title': survey.title,
            'assignment_date': date,
            'note': 'Role is automatically set to user\'s actual role',
            'failed_assignments': failed_assignments if failed_assignments else None
        }
        
        if skipped_users:
            response_data['skipped_users'] = skipped_users
            response_data['message'] += f'. Skipped {len(skipped_users)} already assigned user(s)'
        
        return Response(response_data, status=status.HTTP_201_CREATED)

    @action(detail=False, methods=['delete'], url_path='remove-assignment')
    def remove_assignment(self, request):
        """
        Remove assignment(s) from a survey
        
        Request body:
        {
            "survey_id": 1,
            "date": "2024-01-15",
            "assignment_ids": [1, 2, 3]  # Optional: specific assignment IDs
            "user_ids": [1, 2, 3]        # Optional: user IDs to remove
            "team_id": 1                 # Optional: remove all team members
        }
        """
        survey_id = request.data.get('survey_id')
        date = request.data.get('date')
        assignment_ids = request.data.get('assignment_ids', [])
        user_ids = request.data.get('user_ids', [])
        team_id = request.data.get('team_id')
        
        if not all([survey_id, date]):
            return Response({
                'success': False,
                'error': 'survey_id and date are required'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Validate date format
        try:
            assignment_date = timezone.datetime.strptime(date, '%Y-%m-%d').date()
        except ValueError:
            return Response({
                'success': False,
                'error': 'Invalid date format. Use YYYY-MM-DD'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Build queryset for assignments to remove
        assignments_to_remove = SurveyAssignment.objects.filter(
            survey_id=survey_id,
            date=assignment_date,
            is_deleted=False
        )
        
        # Filter by assignment IDs if provided
        if assignment_ids:
            assignments_to_remove = assignments_to_remove.filter(id__in=assignment_ids)
        
        # Filter by user IDs if provided
        if user_ids:
            assignments_to_remove = assignments_to_remove.filter(assignee_id__in=user_ids)
        
        # Filter by team if provided
        if team_id:
            if request.user.is_admin():
                # Admin can access any team
                try:
                    team = Team.objects.get(id=team_id, is_deleted=False)
                except Team.DoesNotExist:
                    return Response({
                        'success': False,
                        'error': 'Team not found'
                    }, status=status.HTTP_404_NOT_FOUND)
            else:
                # CompanyAdmin and others are restricted to their company
                try:
                    team = Team.objects.get(id=team_id, company=request.user.company, is_deleted=False)
                except Team.DoesNotExist:
                    return Response({
                        'success': False,
                        'error': 'Team not found or access denied'
                    }, status=status.HTTP_404_NOT_FOUND)
            
            team_member_ids = TeamUser.objects.filter(team=team).values_list('user_id', flat=True)
            assignments_to_remove = assignments_to_remove.filter(assignee_id__in=team_member_ids)
        
        # Ensure user can only remove assignments based on their role
        if not request.user.is_admin():
            # CompanyAdmin and others are restricted to their company
            assignments_to_remove = assignments_to_remove.filter(assignee__company=request.user.company)
        
        if not assignments_to_remove.exists():
            return Response({
                'success': False,
                'error': 'No assignments found to remove'
            }, status=status.HTTP_404_NOT_FOUND)
        
        # Get assignment details before deletion
        removed_assignments = []
        for assignment in assignments_to_remove:
            removed_assignments.append({
                'id': assignment.id,
                'user_name': assignment.assignee.name,
                'user_id': assignment.assignee.id,
                'role': assignment.role
            })
        
        # Soft delete assignments
        count = assignments_to_remove.update(is_deleted=True)
        
        return Response({
            'success': True,
            'message': f'Successfully removed {count} assignment(s)',
            'removed_count': count,
            'removed_assignments': removed_assignments
        }, status=status.HTTP_200_OK)

    @action(detail=False, methods=['delete'], url_path='remove-assignment-by-id')
    def remove_assignment_by_id(self, request):
        """
        Remove assignment(s) by assignment ID(s) - easier method
        
        Request body:
        {
            "assignment_ids": [1, 2, 3]  # Required: specific assignment IDs to remove
        }
        """
        assignment_ids = request.data.get('assignment_ids', [])
        
        if not assignment_ids:
            return Response({
                'success': False,
                'error': 'assignment_ids is required'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        if not isinstance(assignment_ids, list):
            return Response({
                'success': False,
                'error': 'assignment_ids must be a list'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Get assignments to remove
        assignments_to_remove = SurveyAssignment.objects.filter(
            id__in=assignment_ids,
            is_deleted=False
        )
        
        # Ensure user can only remove assignments based on their role
        if not request.user.is_admin():
            # CompanyAdmin and others are restricted to their company
            assignments_to_remove = assignments_to_remove.filter(assignee__company=request.user.company)
        
        if not assignments_to_remove.exists():
            return Response({
                'success': False,
                'error': 'No assignments found to remove'
            }, status=status.HTTP_404_NOT_FOUND)
        
        # Get assignment details before deletion
        removed_assignments = []
        for assignment in assignments_to_remove:
            removed_assignments.append({
                'id': assignment.id,
                'user_name': assignment.assignee.name,
                'user_id': assignment.assignee.id,
                'role': assignment.role,
                'survey_title': assignment.survey.title,
                'assignment_date': assignment.date.isoformat()
            })
        
        # Soft delete assignments
        count = assignments_to_remove.update(is_deleted=True)
        
        return Response({
            'success': True,
            'message': f'Successfully removed {count} assignment(s)',
            'removed_count': count,
            'removed_assignments': removed_assignments
        }, status=status.HTTP_200_OK)

    @action(detail=False, methods=['delete'], url_path='remove-survey-assignments')
    def remove_survey_assignments(self, request):
        """
        Remove all assignments for a survey - by team or user
        
        Request body:
        {
            "survey_id": 38,                    # Required: Survey ID
            "team_id": 5,                       # Optional: Remove all team members
            "user_ids": [1, 2, 3],              # Optional: Remove specific users
            "all_users": true,                  # Optional: Remove all users for this survey
            "date": "2024-01-15"                # Optional: Specific date only
        }
        
        Examples:
        1. Remove entire team from survey:
           {"survey_id": 38, "team_id": 5}
        
        2. Remove specific users from survey:
           {"survey_id": 38, "user_ids": [1, 2, 3]}
        
        3. Remove all users from survey:
           {"survey_id": 38, "all_users": true}
        
        4. Remove team from specific date:
           {"survey_id": 38, "team_id": 5, "date": "2024-01-15"}
        """
        survey_id = request.data.get('survey_id')
        team_id = request.data.get('team_id')
        user_ids = request.data.get('user_ids', [])
        all_users = request.data.get('all_users', False)
        date = request.data.get('date')
        
        if not survey_id:
            return Response({
                'success': False,
                'error': 'survey_id is required'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Validate survey exists
        try:
            from surveys.models import Survey
            survey = Survey.objects.get(id=survey_id)
        except Survey.DoesNotExist:
            return Response({
                'success': False,
                'error': 'Survey not found'
            }, status=status.HTTP_404_NOT_FOUND)
        
        # Build base queryset for assignments to remove
        assignments_to_remove = SurveyAssignment.objects.filter(
            survey_id=survey_id,
            is_deleted=False
        )
        
        # Filter by date if provided
        if date:
            try:
                assignment_date = timezone.datetime.strptime(date, '%Y-%m-%d').date()
                assignments_to_remove = assignments_to_remove.filter(date=assignment_date)
            except ValueError:
                return Response({
                    'success': False,
                    'error': 'Invalid date format. Use YYYY-MM-DD'
                }, status=status.HTTP_400_BAD_REQUEST)
        
        # Determine what to remove based on parameters
        if team_id:
            # Remove team members
            if request.user.is_admin():
                try:
                    team = Team.objects.get(id=team_id, is_deleted=False)
                except Team.DoesNotExist:
                    return Response({
                        'success': False,
                        'error': 'Team not found'
                    }, status=status.HTTP_404_NOT_FOUND)
            else:
                try:
                    team = Team.objects.get(id=team_id, company=request.user.company, is_deleted=False)
                except Team.DoesNotExist:
                    return Response({
                        'success': False,
                        'error': 'Team not found or access denied'
                    }, status=status.HTTP_404_NOT_FOUND)
            
            team_member_ids = TeamUser.objects.filter(team=team).values_list('user_id', flat=True)
            assignments_to_remove = assignments_to_remove.filter(assignee_id__in=team_member_ids)
            
        elif user_ids:
            # Remove specific users
            if not isinstance(user_ids, list):
                return Response({
                    'success': False,
                    'error': 'user_ids must be a list'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            assignments_to_remove = assignments_to_remove.filter(assignee_id__in=user_ids)
            
        elif all_users:
            # Remove all users (no additional filter needed)
            pass
            
        else:
            return Response({
                'success': False,
                'error': 'Must specify either team_id, user_ids, or all_users=true'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Ensure user can only remove assignments based on their role
        if not request.user.is_admin():
            assignments_to_remove = assignments_to_remove.filter(assignee__company=request.user.company)
        
        if not assignments_to_remove.exists():
            return Response({
                'success': False,
                'error': 'No assignments found to remove'
            }, status=status.HTTP_404_NOT_FOUND)
        
        # Get assignment details before deletion
        removed_assignments = []
        for assignment in assignments_to_remove:
            removed_assignments.append({
                'id': assignment.id,
                'user_name': assignment.assignee.name,
                'user_id': assignment.assignee.id,
                'role': assignment.role,
                'assignment_date': assignment.date.isoformat(),
                'daily_target_samples': assignment.daily_target_samples,
                'total_target_samples': assignment.total_target_samples
            })
        
        # Soft delete assignments
        count = assignments_to_remove.update(is_deleted=True)
        
        # Prepare response message
        if team_id:
            message = f"Successfully removed {count} assignment(s) for team '{team.name}' from survey '{survey.title}'"
        elif user_ids:
            message = f"Successfully removed {count} assignment(s) for {len(user_ids)} user(s) from survey '{survey.title}'"
        elif all_users:
            message = f"Successfully removed {count} assignment(s) for all users from survey '{survey.title}'"
        
        if date:
            message += f" for date {date}"
        
        return Response({
            'success': True,
            'message': message,
            'removed_count': count,
            'survey': {
                'id': survey.id,
                'title': survey.title
            },
            'removed_assignments': removed_assignments
        }, status=status.HTTP_200_OK)


class AssignmentTargetViewSet(PaginatedViewSetMixin, AuditLogMixin, viewsets.ModelViewSet):
    queryset = AssignmentTarget.objects.all()
    serializer_class = AssignmentTargetSerializer
    permission_classes = [IsAuthenticated]
    search_fields = ['assignment__survey__title', 'question__question_text']
    filterset_fields = ['assignment', 'target_type']
    ordering_fields = ['created_at']
    
    def get_permissions(self):
        """Return appropriate permissions based on action"""
        if self.action in ['create', 'update', 'partial_update', 'destroy']:
            return [IsAuthenticated(), IsAdminOrCompanyAdmin()]
        elif hasattr(self, 'action') and self.action in ['update_progress', 'log_login', 'log_logout']:
            # For DailyTargetViewSet, PerformanceReportViewSet, DutyLogViewSet
            if self.action in ['update_progress']:
                return [IsAuthenticated(), IsAdminOrCompanyAdmin(), IsSurveyorOrVendorSurveyor()]
            if self.action in ['log_login', 'log_logout']:
                return [IsAuthenticated(), IsAdminOrCompanyAdmin(), IsSurveyorOrVendorSurveyor()]
        return [IsAuthenticated()]


class DailyTargetViewSet(PaginatedViewSetMixin, AuditLogMixin, viewsets.ModelViewSet):
    queryset = DailyTarget.objects.all()
    serializer_class = DailyTargetSerializer
    permission_classes = [IsAuthenticated]
    search_fields = ['assignment__survey__title', 'assignment__assignee__name']
    filterset_fields = ['assignment', 'date']
    date_filter_field = 'date'
    ordering_fields = ['date', 'target_samples', 'completed_samples']
    
    def get_permissions(self):
        """Return appropriate permissions based on action"""
        if self.action in ['create', 'update', 'partial_update', 'destroy']:
            return [IsAuthenticated(), IsAdminOrCompanyAdmin()]
        elif self.action in ['update_progress']:
            return [IsAuthenticated(), IsAdminOrCompanyAdmin(), IsSurveyorOrVendorSurveyor()]
        return [IsAuthenticated()]
    
    def get_queryset(self):
        """Filter queryset based on user role and permissions"""
        queryset = DailyTarget.objects.all()
        
        # Admin can see all daily targets
        if self.request.user.is_admin():
            return queryset
        
        # Company Admin can see daily targets from their company
        if self.request.user.is_company_admin():
            return queryset.filter(assignment__assignee__company=self.request.user.company)
        
        # Surveyor and Vendor Surveyor can only see their own daily targets
        if self.request.user.is_surveyor() or self.request.user.is_vendor_surveyor():
            return queryset.filter(assignment__assignee=self.request.user)
        
        # QC Reviewer can see daily targets from their company
        if self.request.user.is_qc_reviewer():
            return queryset.filter(assignment__assignee__company=self.request.user.company)
        
        # Default: return empty queryset for unknown roles
        return queryset.none()
    
    def get_serializer_class(self):
        if self.action == 'create':
            return DailyTargetCreateSerializer
        return DailyTargetSerializer
    
    def perform_create(self, serializer):
        """Override to log daily target creation with improved audit logging."""
        instance = serializer.save()
        
        # Get additional context data
        additional_data = self._get_additional_audit_data('create', instance, serializer.validated_data)
        
        # Create audit log
        self._create_audit_log(
            action=LogEntry.Action.CREATE,
            instance=instance,
            additional_data=additional_data,
            operation_context='daily_target_creation'
        )
        
        return instance
    
    def perform_update(self, serializer):
        """Override to log daily target updates with improved change detection."""
        # Get the instance before saving to capture old values
        instance = serializer.instance
        
        # Save the instance with new data
        updated_instance = serializer.save()
        
        # Get changes by comparing old and new values
        changes = self._get_changes(instance, serializer.validated_data)
        
        # Get additional context data
        additional_data = self._get_additional_audit_data('update', updated_instance, serializer.validated_data)
        
        # Create audit log
        self._create_audit_log(
            action=LogEntry.Action.UPDATE,
            instance=updated_instance,
            changes=changes,
            additional_data=additional_data,
            operation_context='daily_target_update'
        )
        
        return updated_instance
    
    def _get_additional_audit_data(self, action, instance, validated_data):
        """Add custom audit data for daily target operations."""
        additional_data = {
            'action_source': 'api',
            'survey_title': getattr(instance.assignment.survey, 'title', None) if hasattr(instance, 'assignment') else None,
            'assignee_name': getattr(instance.assignment.assignee, 'name', None) if hasattr(instance, 'assignment') else None,
        }
        
        if action == 'create':
            additional_data['created_via'] = 'api'
        elif action == 'update':
            additional_data['updated_fields'] = list(validated_data.keys())
        elif action == 'delete':
            additional_data['deleted_via'] = 'hard_delete'
        
        return additional_data
    
    @action(detail=True, methods=['post'], url_path='update-progress')
    def update_progress(self, request, pk=None):
        """Update completed samples for a daily target"""
        daily_target = self.get_object()
        completed_samples = request.data.get('completed_samples')
        
        if completed_samples is None:
            return Response(
                {'error': 'completed_samples is required'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        try:
            completed_samples = int(completed_samples)
            if completed_samples < 0:
                raise ValueError("Completed samples cannot be negative")
        except (ValueError, TypeError):
            return Response(
                {'error': 'completed_samples must be a positive integer'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        daily_target.completed_samples = completed_samples
        daily_target.save()
        
        serializer = self.get_serializer(daily_target)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'], url_path='today-targets')
    def today_targets(self, request):
        """Get today's targets for current user"""
        today = timezone.now().date()
        queryset = self.get_queryset().filter(
            assignment__assignee=request.user,
            date=today
        )
        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'], url_path='performance-dashboard')
    def performance_dashboard(self, request):
        """Get performance dashboard data for current user"""
        today = timezone.now().date()
        week_ago = today - timezone.timedelta(days=7)
        
        # Get today's targets
        today_targets = self.get_queryset().filter(
            assignment__assignee=request.user,
            date=today
        )
        
        # Get weekly performance
        weekly_targets = self.get_queryset().filter(
            assignment__assignee=request.user,
            date__range=[week_ago, today]
        )
        
        # Calculate statistics
        today_total_target = today_targets.aggregate(
            total=Sum('target_samples')
        )['total'] or 0
        today_total_completed = today_targets.aggregate(
            total=Sum('completed_samples')
        )['total'] or 0
        
        weekly_total_target = weekly_targets.aggregate(
            total=Sum('target_samples')
        )['total'] or 0
        weekly_total_completed = weekly_targets.aggregate(
            total=Sum('completed_samples')
        )['total'] or 0
        
        # Calculate performance metrics
        today_percentage = (today_total_completed / today_total_target * 100) if today_total_target > 0 else 0
        weekly_percentage = (weekly_total_completed / weekly_total_target * 100) if weekly_total_target > 0 else 0
        
        # Count days on track vs behind
        days_on_track = weekly_targets.filter(
            completed_samples__gte=F('target_samples')
        ).count()
        days_behind = weekly_targets.filter(
            completed_samples__lt=F('target_samples')
        ).count()
        
        dashboard_data = {
            'today': {
                'target': today_total_target,
                'completed': today_total_completed,
                'percentage': round(today_percentage, 2),
                'status': 'On Track' if today_total_completed >= today_total_target else 'Behind'
            },
            'weekly': {
                'target': weekly_total_target,
                'completed': weekly_total_completed,
                'percentage': round(weekly_percentage, 2),
                'days_on_track': days_on_track,
                'days_behind': days_behind
            },
            'targets': DailyTargetSerializer(today_targets, many=True).data
        }
        
        return Response(dashboard_data)


class PerformanceReportViewSet(PaginatedViewSetMixin, AuditLogMixin, viewsets.ModelViewSet):
    queryset = PerformanceReport.objects.all()
    serializer_class = PerformanceReportSerializer
    permission_classes = [IsAuthenticated]
    search_fields = ['assignment__survey__title', 'assignment__assignee__name']
    filterset_fields = ['assignment', 'report_date']
    date_filter_field = 'report_date'
    ordering_fields = ['report_date', 'completion_rate']
    
    def get_permissions(self):
        """Return appropriate permissions based on action"""
        if self.action in ['create', 'update', 'partial_update', 'destroy']:
            return [IsAuthenticated(), IsAdminOrCompanyAdmin()]
        return [IsAuthenticated()]
    
    def get_queryset(self):
        """Filter queryset based on user role and permissions"""
        queryset = PerformanceReport.objects.all()
        
        # Admin can see all performance reports
        if self.request.user.is_admin():
            return queryset
        
        # Company Admin can see performance reports from their company
        if self.request.user.is_company_admin():
            return queryset.filter(assignment__assignee__company=self.request.user.company)
        
        # Surveyor and Vendor Surveyor can only see their own performance reports
        if self.request.user.is_surveyor() or self.request.user.is_vendor_surveyor():
            return queryset.filter(assignment__assignee=self.request.user)
        
        # QC Reviewer can see performance reports from their company
        if self.request.user.is_qc_reviewer():
            return queryset.filter(assignment__assignee__company=self.request.user.company)
        
        # Default: return empty queryset for unknown roles
        return queryset.none()
    
    def get_serializer_class(self):
        if self.action == 'create':
            return PerformanceReportCreateSerializer
        return PerformanceReportSerializer
    
    def perform_create(self, serializer):
        """Override to log performance report creation with improved audit logging."""
        instance = serializer.save()
        
        # Get additional context data
        additional_data = self._get_additional_audit_data('create', instance, serializer.validated_data)
        
        # Create audit log
        self._create_audit_log(
            action=LogEntry.Action.CREATE,
            instance=instance,
            additional_data=additional_data,
            operation_context='performance_report_creation'
        )
        
        return instance
    
    def perform_update(self, serializer):
        """Override to log performance report updates with improved change detection."""
        # Get the instance before saving to capture old values
        instance = serializer.instance
        
        # Save the instance with new data
        updated_instance = serializer.save()
        
        # Get changes by comparing old and new values
        changes = self._get_changes(instance, serializer.validated_data)
        
        # Get additional context data
        additional_data = self._get_additional_audit_data('update', updated_instance, serializer.validated_data)
        
        # Create audit log
        self._create_audit_log(
            action=LogEntry.Action.UPDATE,
            instance=updated_instance,
            changes=changes,
            additional_data=additional_data,
            operation_context='performance_report_update'
        )
        
        return updated_instance
    
    def _get_additional_audit_data(self, action, instance, validated_data):
        """Add custom audit data for performance report operations."""
        additional_data = {
            'action_source': 'api',
            'survey_title': getattr(instance.assignment.survey, 'title', None) if hasattr(instance, 'assignment') else None,
            'assignee_name': getattr(instance.assignment.assignee, 'name', None) if hasattr(instance, 'assignment') else None,
        }
        
        if action == 'create':
            additional_data['created_via'] = 'api'
        elif action == 'update':
            additional_data['updated_fields'] = list(validated_data.keys())
        elif action == 'delete':
            additional_data['deleted_via'] = 'hard_delete'
        
        return additional_data
    
    @action(detail=False, methods=['post'], url_path='generate-report')
    def generate_report(self, request):
        """Generate performance report for an assignment"""
        assignment_id = request.data.get('assignment_id')
        report_date_str = request.data.get('report_date')
        
        if not assignment_id:
            return Response(
                {'error': 'assignment_id is required'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        try:
            assignment = SurveyAssignment.objects.get(id=assignment_id)
        except SurveyAssignment.DoesNotExist:
            return Response(
                {'error': 'Assignment not found'},
                status=status.HTTP_404_NOT_FOUND
            )
        
        if report_date_str:
            try:
                report_date = timezone.datetime.strptime(report_date_str, '%Y-%m-%d').date()
            except ValueError:
                return Response(
                    {'error': 'Invalid report_date format. Use YYYY-MM-DD'},
                    status=status.HTTP_400_BAD_REQUEST
                )
        else:
            report_date = timezone.now().date()
        
        # Calculate performance metrics
        overall_progress = assignment.get_overall_progress()
        performance_summary = assignment.get_performance_summary()
        
        # Calculate daily average
        total_days = assignment.daily_targets.count()
        daily_average = (overall_progress['total_completed'] / total_days) if total_days > 0 else 0
        
        # Count days on track vs behind
        days_on_track = sum(1 for day in performance_summary if day['status'] == 'On Track')
        days_behind = sum(1 for day in performance_summary if day['status'] == 'Behind')
        
        # Create or update performance report
        report, created = PerformanceReport.objects.update_or_create(
            assignment=assignment,
            report_date=report_date,
            defaults={
                'total_target': overall_progress['total_target'],
                'total_completed': overall_progress['total_completed'],
                'daily_average': daily_average,
                'completion_rate': overall_progress['percentage'],
                'days_on_track': days_on_track,
                'days_behind': days_behind,
                'notes': request.data.get('notes', '')
            }
        )
        
        serializer = self.get_serializer(report)
        return Response(serializer.data)


class DutyLogViewSet(PaginatedViewSetMixin, AuditLogMixin, viewsets.ModelViewSet):
    """Simple ViewSet for DutyLog model - login/logout tracking"""
    queryset = DutyLog.objects.all()
    serializer_class = DutyLogSerializer
    permission_classes = [IsAuthenticated]
    search_fields = ['user__name', 'status']
    filterset_fields = ['user', 'status', 'date']
    date_filter_field = 'timestamp'
    ordering_fields = ['timestamp', 'date']
    
    def get_permissions(self):
        """Return appropriate permissions based on action"""
        if self.action in ['create', 'update', 'partial_update', 'destroy']:
            return [IsAuthenticated(), IsAdminOrCompanyAdmin()]
        elif self.action in ['log_login', 'log_logout']:
            return [IsAuthenticated(), IsAdminOrCompanyAdmin(), IsSurveyorOrVendorSurveyor()]
        return [IsAuthenticated()]
    
    def get_queryset(self):
        """Filter queryset based on user role and permissions"""
        queryset = DutyLog.objects.all()
        
        # Admin can see all duty logs
        if self.request.user.is_admin():
            return queryset
        
        # Company Admin can see duty logs from their company
        if self.request.user.is_company_admin():
            return queryset.filter(user__company=self.request.user.company)
        
        # Surveyor and Vendor Surveyor can only see their own duty logs
        if self.request.user.is_surveyor() or self.request.user.is_vendor_surveyor():
            return queryset.filter(user=self.request.user)
        
        # QC Reviewer can see duty logs from their company
        if self.request.user.is_qc_reviewer():
            return queryset.filter(user__company=self.request.user.company)
        
        # Default: return empty queryset for unknown roles
        return queryset.none()
    
    def perform_create(self, serializer):
        """Override to log duty log creation with improved audit logging."""
        instance = serializer.save()
        
        # Get additional context data
        additional_data = self._get_additional_audit_data('create', instance, serializer.validated_data)
        
        # Create audit log
        self._create_audit_log(
            action=LogEntry.Action.CREATE,
            instance=instance,
            additional_data=additional_data,
            operation_context='duty_log_creation'
        )
        
        return instance
    
    def perform_update(self, serializer):
        """Override to log duty log updates with improved change detection."""
        # Get the instance before saving to capture old values
        instance = serializer.instance
        
        # Save the instance with new data
        updated_instance = serializer.save()
        
        # Get changes by comparing old and new values
        changes = self._get_changes(instance, serializer.validated_data)
        
        # Get additional context data
        additional_data = self._get_additional_audit_data('update', updated_instance, serializer.validated_data)
        
        # Create audit log
        self._create_audit_log(
            action=LogEntry.Action.UPDATE,
            instance=updated_instance,
            changes=changes,
            additional_data=additional_data,
            operation_context='duty_log_update'
        )
        
        return updated_instance
    
    def _get_additional_audit_data(self, action, instance, validated_data):
        """Add custom audit data for duty log operations."""
        additional_data = {
            'action_source': 'api',
            'user_name': getattr(instance.user, 'name', None) if hasattr(instance, 'user') else None,
            'status': getattr(instance, 'status', None),
        }
        
        if action == 'create':
            additional_data['created_via'] = 'api'
        elif action == 'update':
            additional_data['updated_fields'] = list(validated_data.keys())
        elif action == 'delete':
            additional_data['deleted_via'] = 'hard_delete'
        
        return additional_data
    
    @action(detail=False, methods=['post'], url_path='log-login')
    def log_login(self, request):
        """Log a surveyor login"""
        location = request.data.get('location', '')
        latitude = request.data.get('latitude', '')
        longitude = request.data.get('longitude', '')
        device_info = request.data.get('device_info', {})
        
        duty_log = AssignmentTrackingService.log_login(
            user=request.user,
            location=location,
            latitude=latitude,
            longitude=longitude,
            device_info=device_info
        )
        
        if duty_log:
            serializer = self.get_serializer(duty_log)
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        
        return Response(
            {'error': 'Failed to log login'},
            status=status.HTTP_400_BAD_REQUEST
        )
    
    @action(detail=False, methods=['post'], url_path='log-logout')
    def log_logout(self, request):
        """Log a surveyor logout"""
        location = request.data.get('location', '')
        latitude = request.data.get('latitude', '')
        longitude = request.data.get('longitude', '')
        device_info = request.data.get('device_info', {})
        
        duty_log = AssignmentTrackingService.log_logout(
            user=request.user,
            location=location,
            latitude=latitude,
            longitude=longitude,
            device_info=device_info
        )
        
        if duty_log:
            serializer = self.get_serializer(duty_log)
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        
        return Response(
            {'error': 'Failed to log logout'},
            status=status.HTTP_400_BAD_REQUEST
        )
    
    @action(detail=False, methods=['get'], url_path='my-logs')
    def my_logs(self, request):
        """Get current user's duty logs"""
        start_date_str = request.query_params.get('start_date')
        end_date_str = request.query_params.get('end_date')
        
        start_date = None
        end_date = None
        
        if start_date_str:
            try:
                start_date = timezone.datetime.strptime(start_date_str, '%Y-%m-%d').date()
            except ValueError:
                return Response(
                    {'error': 'Invalid start_date format. Use YYYY-MM-DD'},
                    status=status.HTTP_400_BAD_REQUEST
                )
        
        if end_date_str:
            try:
                end_date = timezone.datetime.strptime(end_date_str, '%Y-%m-%d').date()
            except ValueError:
                return Response(
                    {'error': 'Invalid end_date format. Use YYYY-MM-DD'},
                    status=status.HTTP_400_BAD_REQUEST
                )
        
        duty_logs = AssignmentTrackingService.get_user_duty_logs(
            user=request.user,
            start_date=start_date,
            end_date=end_date
        )
        
        if duty_logs is not None:
            serializer = self.get_serializer(duty_logs, many=True)
            return Response(serializer.data, status=status.HTTP_200_OK)
        
        return Response(
            {'error': 'Failed to get duty logs'},
            status=status.HTTP_400_BAD_REQUEST
        ) 