from django.contrib import admin
from .models import SurveyAssignment, AssignmentTarget


@admin.register(SurveyAssignment)
class SurveyAssignmentAdmin(admin.ModelAdmin):
    list_display = ('survey', 'assignee', 'role', 'date', 'status', 'is_closed', 'assigned_by')
    list_filter = ('role', 'status', 'is_closed', 'date', 'is_deleted')
    search_fields = ('survey__title', 'assignee__name', 'assignee__username')
    readonly_fields = ('assigned_at',)
    ordering = ('-assigned_at',)


@admin.register(AssignmentTarget)
class AssignmentTargetAdmin(admin.ModelAdmin):
    list_display = ('assignment', 'question', 'option', 'target_type', 'value', 'taken_count')
    list_filter = ('target_type', 'assignment__role', 'assignment__survey')
    search_fields = ('assignment__survey__title', 'question__question_text', 'option__option_text')
    readonly_fields = ('taken_at', 'last_synced_at')
    ordering = ('assignment', 'question')
