from django.utils import timezone
from django.db.models import Sum, F
from .models import SurveyAssignment, DailyTarget, PerformanceReport, DutyLog


class AssignmentTrackingService:
    """
    Service for automatically tracking assignment progress and daily targets
    """
    
    @staticmethod
    def update_daily_progress(user, survey, sample_count=1):
        """
        Automatically update daily progress when a surveyor collects samples
        
        Args:
            user: The surveyor who collected the sample
            survey: The survey for which the sample was collected
            sample_count: Number of samples collected (default 1)
        """
        try:
            # Find the assignment for this user and survey
            assignment = SurveyAssignment.objects.filter(
                assignee=user,
                survey=survey,
                is_deleted=False
            ).first()
            
            if not assignment:
                return None
            
            # Only track progress for surveyor assignments (exclude QCReviewer)
            if not assignment.is_performance_tracked():
                return None
            
            today = timezone.now().date()
            
            # Get or create daily target for today
            daily_target, created = DailyTarget.objects.get_or_create(
                assignment=assignment,
                date=today,
                defaults={
                    'target_samples': assignment.daily_target_samples,
                    'completed_samples': 0
                }
            )
            
            # Update completed samples
            daily_target.completed_samples += sample_count
            daily_target.save()
            
            # Update assignment status if needed
            if assignment.status == "Assigned":
                assignment.status = "InProgress"
                assignment.save()
            
            return daily_target
            
        except Exception as e:
            print(f"Error updating daily progress: {e}")
            return None
    
    @staticmethod
    def update_assignment_progress(user, survey):
        """
        Update overall assignment progress
        """
        try:
            assignment = SurveyAssignment.objects.filter(
                assignee=user,
                survey=survey,
                is_deleted=False
            ).first()
            
            if not assignment:
                return None
            
            # Only track progress for surveyor assignments (exclude QCReviewer)
            if not assignment.is_performance_tracked():
                return None
            
            # Calculate total completed samples
            total_completed = assignment.daily_targets.aggregate(
                total=Sum('completed_samples')
            )['total'] or 0
            
            # Check if assignment is completed
            if total_completed >= assignment.total_target_samples:
                assignment.status = "Completed"
                assignment.save()
            
            return assignment
            
        except Exception as e:
            print(f"Error updating assignment progress: {e}")
            return None
    
    @staticmethod
    def generate_performance_report(assignment, report_date=None):
        """
        Generate or update performance report for an assignment
        """
        if report_date is None:
            report_date = timezone.now().date()
        
        try:
            # Only generate reports for surveyor assignments (exclude QCReviewer)
            if not assignment.is_performance_tracked():
                return None
            
            # Calculate performance metrics
            overall_progress = assignment.get_overall_progress()
            performance_summary = assignment.get_performance_summary()
            
            # Calculate daily average
            total_days = assignment.daily_targets.count()
            daily_average = (overall_progress['total_completed'] / total_days) if total_days > 0 else 0
            
            # Count days on track vs behind
            days_on_track = sum(1 for day in performance_summary if day['status'] == 'On Track')
            days_behind = sum(1 for day in performance_summary if day['status'] == 'Behind')
            
            # Create or update performance report
            report, created = PerformanceReport.objects.update_or_create(
                assignment=assignment,
                report_date=report_date,
                defaults={
                    'total_target': overall_progress['total_target'],
                    'total_completed': overall_progress['total_completed'],
                    'daily_average': daily_average,
                    'completion_rate': overall_progress['percentage'],
                    'days_on_track': days_on_track,
                    'days_behind': days_behind
                }
            )
            
            return report
            
        except Exception as e:
            print(f"Error generating performance report: {e}")
            return None
    
    @staticmethod
    def get_user_performance_summary(user, start_date=None, end_date=None):
        """
        Get performance summary for a user
        """
        if start_date is None:
            start_date = timezone.now().date() - timezone.timedelta(days=7)
        if end_date is None:
            end_date = timezone.now().date()
        
        try:
            # Only include surveyor assignments (exclude QCReviewer)
            assignments = SurveyAssignment.objects.filter(
                assignee=user,
                is_deleted=False
            ).filter(role__in=['Surveyor', 'VendorSurveyor'])
            
            summary = {
                'user_id': user.id,
                'user_name': user.name,
                'period': {
                    'start_date': start_date,
                    'end_date': end_date
                },
                'assignments': [],
                'total_samples_collected': 0,
                'total_target': 0,
                'overall_completion_rate': 0,
                'days_on_track': 0,
                'days_behind': 0
            }
            
            for assignment in assignments:
                # Get assignment performance
                assignment_summary = assignment.get_performance_summary(start_date, end_date)
                
                if assignment_summary:
                    assignment_data = {
                        'assignment_id': assignment.id,
                        'survey_title': assignment.survey.title,
                        'performance_data': assignment_summary,
                        'overall_progress': assignment.get_overall_progress()
                    }
                    
                    summary['assignments'].append(assignment_data)
                    
                    # Aggregate totals
                    for day_data in assignment_summary:
                        summary['total_samples_collected'] += day_data['completed']
                        summary['total_target'] += day_data['target']
                        if day_data['status'] == 'On Track':
                            summary['days_on_track'] += 1
                        else:
                            summary['days_behind'] += 1
            
            # Calculate overall completion rate
            if summary['total_target'] > 0:
                summary['overall_completion_rate'] = (
                    summary['total_samples_collected'] / summary['total_target'] * 100
                )
            
            return summary
            
        except Exception as e:
            print(f"Error getting user performance summary: {e}")
            return None
    
    @staticmethod
    def check_daily_target_status(user, survey):
        """
        Check if user has met their daily target for a survey
        """
        try:
            assignment = SurveyAssignment.objects.filter(
                assignee=user,
                survey=survey,
                is_deleted=False
            ).first()
            
            if not assignment:
                return None
            
            # Only check targets for surveyor assignments (exclude QCReviewer)
            if not assignment.is_performance_tracked():
                return None
            
            today = timezone.now().date()
            daily_target = DailyTarget.objects.filter(
                assignment=assignment,
                date=today
            ).first()
            
            if not daily_target:
                return {
                    'status': 'No Target Set',
                    'target': assignment.daily_target_samples,
                    'completed': 0,
                    'percentage': 0
                }
            
            percentage = (daily_target.completed_samples / daily_target.target_samples * 100) if daily_target.target_samples > 0 else 0
            
            return {
                'status': daily_target.get_performance_status(),
                'target': daily_target.target_samples,
                'completed': daily_target.completed_samples,
                'percentage': percentage
            }
            
        except Exception as e:
            print(f"Error checking daily target status: {e}")
            return None
    
    # Simple Duty Tracking Methods
    @staticmethod
    def log_login(user, location="", latitude="", longitude="", device_info=None):
        """
        Log a surveyor login
        """
        try:
            duty_log = DutyLog.objects.create(
                user=user,
                status="Login",
                location=location,
                latitude=latitude,
                longitude=longitude,
                device_info=device_info or {}
            )
            
            print(f"Login logged for {user.name}")
            return duty_log
            
        except Exception as e:
            print(f"Error logging login: {e}")
            return None
    
    @staticmethod
    def log_logout(user, location="", latitude="", longitude="", device_info=None):
        """
        Log a surveyor logout
        """
        try:
            duty_log = DutyLog.objects.create(
                user=user,
                status="Logout",
                location=location,
                latitude=latitude,
                longitude=longitude,
                device_info=device_info or {}
            )
            
            print(f"Logout logged for {user.name}")
            return duty_log
            
        except Exception as e:
            print(f"Error logging logout: {e}")
            return None
    
    @staticmethod
    def get_user_duty_logs(user, start_date=None, end_date=None):
        """
        Get duty logs for a user
        """
        if start_date is None:
            start_date = timezone.now().date() - timezone.timedelta(days=7)
        if end_date is None:
            end_date = timezone.now().date()
        
        try:
            duty_logs = DutyLog.objects.filter(
                user=user,
                date__range=[start_date, end_date]
            ).order_by('-timestamp')
            
            return duty_logs
            
        except Exception as e:
            print(f"Error getting duty logs: {e}")
            return None 