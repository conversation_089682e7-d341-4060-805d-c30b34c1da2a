from django.db import models
from django.db.models import <PERSON>r<PERSON><PERSON>, ForeignKey, CASCADE, SET_NULL, BooleanField, DateField, DateTimeField, DecimalField, PositiveIntegerField, Sum, Q, TextField, JSONField, TimeField
from django.utils.translation import gettext_lazy as _
from django.contrib.auth import get_user_model
from django.utils import timezone
from auditlog.registry import auditlog

User = get_user_model()


class SurveyAssignment(models.Model):
    """
    SurveyAssignment model for assigning surveys to users
    """
    survey = ForeignKey('surveys.Survey', on_delete=CASCADE)
    assignee = ForeignKey(User, on_delete=CASCADE)
    role = CharField(
        _("Role"),
        max_length=20,
        choices=[
            ("Surveyor", "Surveyor"),
            ("VendorSurveyor", "Vendor Surveyor"),
            ("QCReviewer", "QC Reviewer")
        ]
    )
    date = DateField(_("Assignment Date"))
    is_one_time = BooleanField(_("Is One Time"), default=False)
    is_closed = <PERSON>oleanField(_("Is Closed"), default=False)
    status = CharField(
        _("Status"),
        max_length=20,
        choices=[
            ("Assigned", "Assigned"),
            ("InProgress", "In Progress"),
            ("Completed", "Completed")
        ],
        default="Assigned"
    )
    assigned_by = ForeignKey(
        User,
        on_delete=SET_NULL,
        null=True,
        related_name='assignments_given'
    )
    assigned_at = DateTimeField(auto_now_add=True)
    is_deleted = BooleanField(default=False)
    
    # Daily target fields
    daily_target_samples = PositiveIntegerField(_("Daily Target Samples"), default=0)
    total_target_samples = PositiveIntegerField(_("Total Target Samples"), default=0)
    
    def __str__(self):
        return f"{self.survey.title} - {self.assignee.name} ({self.date})"
    
    def get_daily_progress(self, date=None):
        """Get daily progress for a specific date"""
        if date is None:
            date = timezone.now().date()
        
        daily_target = self.daily_targets.filter(date=date).first()
        if daily_target:
            return {
                'date': date,
                'target': daily_target.target_samples,
                'completed': daily_target.completed_samples,
                'percentage': (daily_target.completed_samples / daily_target.target_samples * 100) if daily_target.target_samples > 0 else 0
            }
        return None
    
    def get_overall_progress(self):
        """Get overall progress for the assignment"""
        total_completed = self.daily_targets.aggregate(
            total=Sum('completed_samples')
        )['total'] or 0
        
        return {
            'total_target': self.total_target_samples,
            'total_completed': total_completed,
            'percentage': (total_completed / self.total_target_samples * 100) if self.total_target_samples > 0 else 0
        }
    
    def is_performance_tracked(self):
        """Check if this assignment should be included in performance tracking"""
        return self.role in ['Surveyor', 'VendorSurveyor']
    
    def get_performance_summary(self, start_date=None, end_date=None):
        """Get performance summary for a date range"""
        # Only return performance data for surveyor assignments
        if not self.is_performance_tracked():
            return []
            
        if start_date is None:
            start_date = timezone.now().date() - timezone.timedelta(days=7)
        if end_date is None:
            end_date = timezone.now().date()
        
        daily_targets = self.daily_targets.filter(
            date__range=[start_date, end_date]
        ).order_by('date')
        
        performance_data = []
        for daily_target in daily_targets:
            performance_data.append({
                'date': daily_target.date,
                'target': daily_target.target_samples,
                'completed': daily_target.completed_samples,
                'percentage': (daily_target.completed_samples / daily_target.target_samples * 100) if daily_target.target_samples > 0 else 0,
                'status': 'On Track' if daily_target.completed_samples >= daily_target.target_samples else 'Behind'
            })
        
        return performance_data
    
    class Meta:
        verbose_name = _("Survey Assignment")
        verbose_name_plural = _("Survey Assignments")


class DailyTarget(models.Model):
    """
    DailyTarget model for tracking daily performance targets
    """
    assignment = ForeignKey(SurveyAssignment, on_delete=CASCADE, related_name='daily_targets')
    date = DateField(_("Target Date"))
    target_samples = PositiveIntegerField(_("Target Samples"))
    completed_samples = PositiveIntegerField(_("Completed Samples"), default=0)
    notes = models.TextField(_("Notes"), blank=True)
    created_at = DateTimeField(auto_now_add=True)
    updated_at = DateTimeField(auto_now=True)
    
    class Meta:
        unique_together = ('assignment', 'date')
        verbose_name = _("Daily Target")
        verbose_name_plural = _("Daily Targets")
        ordering = ['-date']
    
    def __str__(self):
        return f"{self.assignment} - {self.date} ({self.completed_samples}/{self.target_samples})"
    
    def get_performance_status(self):
        """Get performance status for the day"""
        if self.completed_samples >= self.target_samples:
            return "Completed"
        elif self.completed_samples >= (self.target_samples * 0.8):
            return "On Track"
        elif self.completed_samples >= (self.target_samples * 0.5):
            return "Behind"
        else:
            return "Critical"


class AssignmentTarget(models.Model):
    """
    AssignmentTarget model for defining demographic collection quotas
    """
    assignment = ForeignKey(SurveyAssignment, on_delete=CASCADE, related_name='targets')
    question = ForeignKey('surveys.Question', on_delete=CASCADE)
    option = ForeignKey('surveys.QuestionOption', on_delete=CASCADE)
    TARGET_TYPE_CHOICES = [
        ("count", "Count"),
        ("percent", "Percentage"),
    ]
    target_type = CharField(
        _("Target Type"),
        max_length=10,
        choices=TARGET_TYPE_CHOICES,
        default="count"
    )
    value = DecimalField(_("Target Value"), max_digits=5, decimal_places=2)
    taken_count = PositiveIntegerField(_("Taken Count"), default=0)
    taken_at = DateTimeField(_("Taken At"), null=True, blank=True)
    last_synced_at = DateTimeField(_("Last Synced At"), null=True, blank=True)
    
    class Meta:
        unique_together = ('assignment', 'question', 'option')
        verbose_name = _("Assignment Target")
        verbose_name_plural = _("Assignment Targets")
    
    def __str__(self):
        return f"{self.assignment} - {self.question.question_text[:30]} - {self.option.option_text}"


class PerformanceReport(models.Model):
    """
    PerformanceReport model for storing performance analytics
    """
    assignment = ForeignKey(SurveyAssignment, on_delete=CASCADE, related_name='performance_reports')
    report_date = DateField(_("Report Date"))
    total_target = PositiveIntegerField(_("Total Target"))
    total_completed = PositiveIntegerField(_("Total Completed"))
    daily_average = DecimalField(_("Daily Average"), max_digits=5, decimal_places=2)
    completion_rate = DecimalField(_("Completion Rate %"), max_digits=5, decimal_places=2)
    days_on_track = PositiveIntegerField(_("Days On Track"), default=0)
    days_behind = PositiveIntegerField(_("Days Behind"), default=0)
    notes = models.TextField(_("Notes"), blank=True)
    created_at = DateTimeField(auto_now_add=True)
    
    class Meta:
        unique_together = ('assignment', 'report_date')
        verbose_name = _("Performance Report")
        verbose_name_plural = _("Performance Reports")
        ordering = ['-report_date']
    
    def __str__(self):
        return f"{self.assignment} - {self.report_date} ({self.completion_rate}%)"


class DutyLog(models.Model):
    """
    DutyLog model for tracking surveyor login/logout sessions
    """
    DUTY_STATUS_CHOICES = [
        ("Login", "Login"),
        ("Logout", "Logout"),
    ]
    
    user = ForeignKey(User, on_delete=CASCADE, related_name='duty_logs')
    date = DateField(_("Duty Date"), auto_now_add=True)
    status = CharField(
        _("Duty Status"),
        max_length=20,
        choices=DUTY_STATUS_CHOICES
    )
    timestamp = DateTimeField(_("Timestamp"), auto_now_add=True)
    location = CharField(_("Location"), max_length=255, blank=True)
    latitude = CharField(_("Latitude"), max_length=50, blank=True)
    longitude = CharField(_("Longitude"), max_length=50, blank=True)
    device_info = JSONField(_("Device Info"), null=True, blank=True)
    
    class Meta:
        verbose_name = _("Duty Log")
        verbose_name_plural = _("Duty Logs")
        ordering = ['-timestamp']
    
    def __str__(self):
        return f"{self.user.name} - {self.status} ({self.timestamp})"
