# Generated by Django 5.1.11 on 2025-06-29 05:53

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('surveys', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='SurveyAssignment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('role', models.CharField(choices=[('Surveyor', 'Surveyor'), ('VendorSurveyor', 'Vendor Surveyor')], max_length=20, verbose_name='Role')),
                ('date', models.DateField(verbose_name='Assignment Date')),
                ('is_one_time', models.BooleanField(default=False, verbose_name='Is One Time')),
                ('is_closed', models.BooleanField(default=False, verbose_name='Is Closed')),
                ('status', models.Char<PERSON>ield(choices=[('Assigned', 'Assigned'), ('InProgress', 'In Progress'), ('Completed', 'Completed')], default='Assigned', max_length=20, verbose_name='Status')),
                ('assigned_at', models.DateTimeField(auto_now_add=True)),
                ('is_deleted', models.BooleanField(default=False)),
                ('assigned_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='assignments_given', to=settings.AUTH_USER_MODEL)),
                ('assignee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
                ('survey', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='surveys.survey')),
            ],
            options={
                'verbose_name': 'Survey Assignment',
                'verbose_name_plural': 'Survey Assignments',
            },
        ),
        migrations.CreateModel(
            name='AssignmentTarget',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('target_type', models.CharField(choices=[('count', 'Count'), ('percent', 'Percentage')], default='count', max_length=10, verbose_name='Target Type')),
                ('value', models.DecimalField(decimal_places=2, max_digits=5, verbose_name='Target Value')),
                ('taken_count', models.PositiveIntegerField(default=0, verbose_name='Taken Count')),
                ('taken_at', models.DateTimeField(blank=True, null=True, verbose_name='Taken At')),
                ('last_synced_at', models.DateTimeField(blank=True, null=True, verbose_name='Last Synced At')),
                ('option', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='surveys.questionoption')),
                ('question', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='surveys.question')),
                ('assignment', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='targets', to='assignments.surveyassignment')),
            ],
            options={
                'verbose_name': 'Assignment Target',
                'verbose_name_plural': 'Assignment Targets',
                'unique_together': {('assignment', 'question', 'option')},
            },
        ),
    ]
