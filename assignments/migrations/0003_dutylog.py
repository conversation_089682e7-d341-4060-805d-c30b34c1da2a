# Generated by Django 5.1.11 on 2025-07-13 13:21

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('assignments', '0002_surveyassignment_daily_target_samples_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='DutyLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField(auto_now_add=True, verbose_name='Duty Date')),
                ('status', models.CharField(choices=[('Login', 'Login'), ('Logout', 'Logout')], max_length=20, verbose_name='Duty Status')),
                ('timestamp', models.DateTimeField(auto_now_add=True, verbose_name='Timestamp')),
                ('location', models.Char<PERSON>ield(blank=True, max_length=255, verbose_name='Location')),
                ('latitude', models.CharField(blank=True, max_length=50, verbose_name='Latitude')),
                ('longitude', models.CharField(blank=True, max_length=50, verbose_name='Longitude')),
                ('device_info', models.JSONField(blank=True, null=True, verbose_name='Device Info')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='duty_logs', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Duty Log',
                'verbose_name_plural': 'Duty Logs',
                'ordering': ['-timestamp'],
            },
        ),
    ]
