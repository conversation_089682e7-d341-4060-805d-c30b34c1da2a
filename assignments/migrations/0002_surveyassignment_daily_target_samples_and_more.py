# Generated by Django 5.1.11 on 2025-07-13 11:57

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('assignments', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='surveyassignment',
            name='daily_target_samples',
            field=models.PositiveIntegerField(default=0, verbose_name='Daily Target Samples'),
        ),
        migrations.AddField(
            model_name='surveyassignment',
            name='total_target_samples',
            field=models.PositiveIntegerField(default=0, verbose_name='Total Target Samples'),
        ),
        migrations.CreateModel(
            name='DailyTarget',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField(verbose_name='Target Date')),
                ('target_samples', models.PositiveIntegerField(verbose_name='Target Samples')),
                ('completed_samples', models.PositiveIntegerField(default=0, verbose_name='Completed Samples')),
                ('notes', models.TextField(blank=True, verbose_name='Notes')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('assignment', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='daily_targets', to='assignments.surveyassignment')),
            ],
            options={
                'verbose_name': 'Daily Target',
                'verbose_name_plural': 'Daily Targets',
                'ordering': ['-date'],
                'unique_together': {('assignment', 'date')},
            },
        ),
        migrations.CreateModel(
            name='PerformanceReport',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('report_date', models.DateField(verbose_name='Report Date')),
                ('total_target', models.PositiveIntegerField(verbose_name='Total Target')),
                ('total_completed', models.PositiveIntegerField(verbose_name='Total Completed')),
                ('daily_average', models.DecimalField(decimal_places=2, max_digits=5, verbose_name='Daily Average')),
                ('completion_rate', models.DecimalField(decimal_places=2, max_digits=5, verbose_name='Completion Rate %')),
                ('days_on_track', models.PositiveIntegerField(default=0, verbose_name='Days On Track')),
                ('days_behind', models.PositiveIntegerField(default=0, verbose_name='Days Behind')),
                ('notes', models.TextField(blank=True, verbose_name='Notes')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('assignment', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='performance_reports', to='assignments.surveyassignment')),
            ],
            options={
                'verbose_name': 'Performance Report',
                'verbose_name_plural': 'Performance Reports',
                'ordering': ['-report_date'],
                'unique_together': {('assignment', 'report_date')},
            },
        ),
    ]
