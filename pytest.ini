[tool:pytest]
DJANGO_SETTINGS_MODULE = config.settings.test
python_files = tests.py test_*.py *_tests.py
python_classes = Test*
python_functions = test_*
addopts = 
    --strict-markers
    --strict-config
    --verbose
    --tb=short
    --reuse-db
    --disable-warnings
    --nomigrations
    --django-db-use-migrations=false
testpaths = 
    voter/users/tests
    companies/tests
markers =
    slow: marks tests as slow (deselect with '-m "not slow"')
    integration: marks tests as integration tests
    unit: marks tests as unit tests
    api: marks tests as API tests
    auth: marks tests as authentication tests
    models: marks tests as model tests
    serializers: marks tests as serializer tests
    views: marks tests as view tests
    permissions: marks tests as permission tests
    audit: marks tests as audit log tests 