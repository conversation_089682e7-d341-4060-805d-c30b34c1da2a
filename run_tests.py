#!/usr/bin/env python
"""
Test runner script for Voter-Backend API tests.

This script provides convenient commands to run different types of tests.
"""

import os
import sys
import subprocess
import argparse
from pathlib import Path


def run_command(cmd, description=""):
    """Run a command and handle errors."""
    print(f"\n{'='*60}")
    print(f"Running: {description}")
    print(f"Command: {' '.join(cmd)}")
    print(f"{'='*60}\n")
    
    result = subprocess.run(cmd, capture_output=False)
    
    if result.returncode != 0:
        print(f"\n❌ Command failed with exit code {result.returncode}")
        sys.exit(result.returncode)
    else:
        print(f"\n✅ {description} completed successfully")


def main():
    parser = argparse.ArgumentParser(description="Run Voter-Backend API tests")
    parser.add_argument(
        "--type",
        choices=["all", "unit", "integration", "api", "auth", "models", "serializers", "views"],
        default="all",
        help="Type of tests to run"
    )
    parser.add_argument(
        "--app",
        choices=["users", "companies", "all"],
        default="all",
        help="App to test"
    )
    parser.add_argument(
        "--coverage",
        action="store_true",
        help="Run tests with coverage report"
    )
    parser.add_argument(
        "--verbose",
        action="store_true",
        help="Run tests in verbose mode"
    )
    parser.add_argument(
        "--parallel",
        action="store_true",
        help="Run tests in parallel"
    )
    parser.add_argument(
        "--failed",
        action="store_true",
        help="Run only failed tests"
    )
    
    args = parser.parse_args()
    
    # Set environment variables
    os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings.test")
    print("💾 Using SQLite for testing (fast and reliable)")
    
    # Build pytest command
    cmd = ["python", "-m", "pytest"]
    
    # Add coverage if requested
    if args.coverage:
        cmd.extend(["--cov=voter", "--cov=companies", "--cov-report=html", "--cov-report=term-missing"])
    
    # Add verbose flag
    if args.verbose:
        cmd.append("-v")
    
    # Add parallel flag
    if args.parallel:
        cmd.extend(["-n", "auto"])
    
    # Add failed flag
    if args.failed:
        cmd.append("--lf")
    
    # Determine test paths based on app
    if args.app == "users":
        test_paths = ["voter/users/tests"]
    elif args.app == "companies":
        test_paths = ["companies/tests"]
    else:
        test_paths = ["voter/users/tests", "companies/tests"]
    
    # Add test type markers
    if args.type != "all":
        cmd.extend(["-m", args.type])
    
    # Add test paths
    cmd.extend(test_paths)
    
    # Run the command
    description = f"Running {args.type} tests for {args.app} app"
    if args.coverage:
        description += " with coverage"
    if args.parallel:
        description += " in parallel"
    if args.failed:
        description += " (failed only)"
    
    run_command(cmd, description)


if __name__ == "__main__":
    main() 