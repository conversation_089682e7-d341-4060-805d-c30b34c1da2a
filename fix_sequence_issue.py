#!/usr/bin/env python
"""
<PERSON><PERSON><PERSON> to fix the PostgreSQL sequence issue for QuestionResponse model.

This script addresses the "duplicate key value violates unique constraint" error
that occurs when the PostgreSQL sequence gets out of sync with the actual data.

Usage:
    python fix_sequence_issue.py
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.local')
django.setup()

from django.db import connection
from samples.models import QuestionResponse


def check_sequence_status():
    """Check the current status of the QuestionResponse sequence."""
    print("Checking QuestionResponse sequence status...")
    
    table_name = QuestionResponse._meta.db_table
    sequence_name = f'{table_name}_id_seq'
    
    with connection.cursor() as cursor:
        try:
            # Get the maximum ID from the table
            cursor.execute(f'SELECT MAX(id) FROM {table_name}')
            max_id_result = cursor.fetchone()
            max_id = max_id_result[0] if max_id_result[0] is not None else 0
            
            # Get the current sequence value
            cursor.execute(f"SELECT last_value FROM {sequence_name}")
            current_seq_result = cursor.fetchone()
            current_seq = current_seq_result[0] if current_seq_result else 0
            
            print(f"Table: {table_name}")
            print(f"Sequence: {sequence_name}")
            print(f"Maximum ID in table: {max_id}")
            print(f"Current sequence value: {current_seq}")
            
            if current_seq <= max_id:
                print("❌ ISSUE DETECTED: Sequence is out of sync!")
                print(f"   Sequence should be at least {max_id + 1}")
                return False
            else:
                print("✅ Sequence is correct")
                return True
                
        except Exception as e:
            print(f"❌ Error checking sequence: {str(e)}")
            return False


def fix_sequence():
    """Fix the QuestionResponse sequence."""
    print("\nAttempting to fix the sequence...")
    
    try:
        if QuestionResponse.fix_sequence():
            print("✅ Sequence fixed successfully!")
            return True
        else:
            print("ℹ️  Sequence was already correct")
            return True
    except Exception as e:
        print(f"❌ Error fixing sequence: {str(e)}")
        return False


def test_creation():
    """Test creating a QuestionResponse to verify the fix."""
    print("\nTesting QuestionResponse creation...")
    
    try:
        # We need a sample and question to create a response
        from samples.models import Sample
        from surveys.models import Question
        
        # Get the first available sample and question for testing
        sample = Sample.objects.first()
        question = Question.objects.first()
        
        if not sample or not question:
            print("⚠️  Cannot test: No sample or question available in database")
            return True
        
        # Check if a response already exists for this combination
        existing_response = QuestionResponse.objects.filter(
            sample=sample, 
            question=question
        ).first()
        
        if existing_response:
            print(f"ℹ️  Response already exists (ID: {existing_response.id})")
            return True
        
        # Try to create a new response
        response = QuestionResponse.objects.create(
            sample=sample,
            question=question,
            response="Test response to verify sequence fix"
        )
        
        print(f"✅ Successfully created QuestionResponse with ID: {response.id}")
        
        # Clean up the test response
        response.delete()
        print("🧹 Test response cleaned up")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing creation: {str(e)}")
        return False


def main():
    """Main function to run the sequence fix."""
    print("PostgreSQL Sequence Fix for QuestionResponse")
    print("=" * 50)
    
    # Check if we're using PostgreSQL
    from django.conf import settings
    if 'postgresql' not in settings.DATABASES['default']['ENGINE']:
        print("❌ This fix is only for PostgreSQL databases")
        sys.exit(1)
    
    # Step 1: Check current status
    is_correct = check_sequence_status()
    
    # Step 2: Fix if needed
    if not is_correct:
        if not fix_sequence():
            print("\n❌ Failed to fix the sequence")
            sys.exit(1)
        
        # Verify the fix
        print("\nVerifying the fix...")
        if not check_sequence_status():
            print("❌ Sequence is still incorrect after fix attempt")
            sys.exit(1)
    
    # Step 3: Test creation
    if not test_creation():
        print("\n❌ Failed to test QuestionResponse creation")
        sys.exit(1)
    
    print("\n🎉 All checks passed! The sequence issue has been resolved.")
    print("\nNext steps:")
    print("1. Run the migration: python manage.py migrate")
    print("2. Use the management command for future issues: python manage.py fix_sequence")


if __name__ == '__main__':
    main()
