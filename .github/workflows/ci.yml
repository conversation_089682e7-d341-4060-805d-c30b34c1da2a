name: <PERSON><PERSON>

# Enable Buildkit and let compose use it to speed up image building
env:
  DOCKER_BUILDKIT: 1
  COMPOSE_DOCKER_CLI_BUILD: 1

on:
  pull_request:
    branches: ['master', 'main']
    paths-ignore: ['docs/**']

  # Manual trigger
  workflow_dispatch:

  # Comment out push trigger to disable CI on direct pushes
  # push:
  #   branches: ['master', 'main']
  #   paths-ignore: ['docs/**']

concurrency:
  group: ${{ github.head_ref || github.run_id }}
  cancel-in-progress: true

jobs:
  linter:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout Code Repository
        uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version-file: '.python-version'
      # Consider using pre-commit.ci for open source project
      - name: Run pre-commit
        uses: pre-commit/action@v3.0.1

  # With no caching at all the entire ci process takes 3m to complete!
  pytest:
    runs-on: ubuntu-latest

    services:
      postgres:
        image: postgres:17
        ports:
          - 5432:5432
        env:
          POSTGRES_PASSWORD: postgres

    env:
      # postgres://user:password@host:port/database
      DATABASE_URL: 'postgres://postgres:postgres@localhost:5432/postgres'

    steps:
      - name: Checkout Code Repository
        uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version-file: '.python-version'
          cache: pip
          cache-dependency-path: |
            requirements/base.txt
            requirements/local.txt

      - name: Install Dependencies
        run: |
          python -m pip install --upgrade pip
          pip install -r requirements/all_packages.txt

      - name: Check DB Migrations
        run: python manage.py makemigrations --check

      - name: Run DB Migrations
        run: python manage.py migrate

      - name: Test with run_tests.py
        run: python run_tests.py
