# Config for Dependabot updates. See Documentation here:
# https://docs.github.com/en/code-security/dependabot/dependabot-version-updates/configuration-options-for-the-dependabot.yml-file

version: 2
updates:
  # Update GitHub actions in workflows
  - package-ecosystem: 'github-actions'
    directory: '/'
    # Every weekday
    schedule:
      interval: 'daily'
    groups:
      github-actions:
        patterns:
          - '*'

  # Enable version updates for Python/Pip - Production
  - package-ecosystem: 'pip'
    # Look for a `requirements.txt` in the `root` directory
    # also 'setup.cfg', '.python-version' and 'requirements/*.txt'
    directory: '/'
    # Every weekday
    schedule:
      interval: 'daily'
    groups:
      python:
        update-types:
          - 'minor'
          - 'patch'
