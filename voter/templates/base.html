{% load static i18n %} {% get_current_language as LANGUAGE_CODE %}
<!DOCTYPE html>
<html lang="{{ LANGUAGE_CODE }}">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="x-ua-compatible" content="ie=edge" />
    <title>{% block title %}Voter-Backend{% endblock title %}</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="Surveys" />
    <meta name="author" content="Uday" />
    <link rel="icon" href="{% static 'images/favicons/favicon.ico' %}" />

    {% block css %}
    <!-- Bootstrap CSS -->
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.2.3/css/bootstrap.min.css"
      integrity="sha512-SbiR/eusphKoMVVXysTKG/7VseWii+Y3FdHrt0EpKgpToZeemhqHeZeLWLhJutz/2ut2Vw1uQEj2MbRF+TVBUA=="
      crossorigin="anonymous"
      referrerpolicy="no-referrer"
    />
    <!-- Project-specific CSS -->
    <link href="{% static 'css/project.css' %}" rel="stylesheet" />
    {% endblock css %} {% block javascript %}
    <!-- Bootstrap JS -->
    <script
      defer
      src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.2.3/js/bootstrap.min.js"
      integrity="sha512-1/RvZTcCDEUjY/CypiMz+iqqtaoQfAITmNSJY17Myp4Ms5mdxPS5UV7iOfdZoxcGhzFbOm6sntTKJppjvuhg4g=="
      crossorigin="anonymous"
      referrerpolicy="no-referrer"
    ></script>
    <!-- Project-specific JS -->
    <script defer src="{% static 'js/project.js' %}"></script>
    {% endblock javascript %}
  </head>

  <body class="{% block bodyclass %}{% endblock bodyclass %}">
    {% block body %}
    <nav class="navbar navbar-expand-md navbar-light bg-light mb-3">
      <div class="container-fluid">
        <a class="navbar-brand" href="{% url 'home' %}">Voter-Backend</a>
        <button
          class="navbar-toggler"
          type="button"
          data-bs-toggle="collapse"
          data-bs-target="#navbarNav"
          aria-controls="navbarNav"
          aria-expanded="false"
          aria-label="Toggle navigation"
        >
          <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbarNav">
          <ul class="navbar-nav me-auto mb-2 mb-md-0">
            <li class="nav-item">
              <a
                class="nav-link{% if request.resolver_match.url_name == 'home' %} active{% endif %}"
                href="{% url 'home' %}"
              >
                Home
              </a>
            </li>
            <li class="nav-item">
              <a
                class="nav-link{% if request.resolver_match.url_name == 'about' %} active{% endif %}"
                href="{% url 'about' %}"
              >
                About
              </a>
            </li>
            {% if request.user.is_authenticated %}
            <li class="nav-item">
              <a
                class="nav-link"
                href="{% url 'users:detail' request.user.username %}"
              >
                {% translate "My Profile" %}
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="{% url 'account_logout' %}">
                {% translate "Sign Out" %}
              </a>
            </li>
            {% else %} {% if ACCOUNT_ALLOW_REGISTRATION %}
            <li class="nav-item">
              <a class="nav-link" href="{% url 'account_signup' %}">
                {% translate "Sign Up" %}
              </a>
            </li>
            {% endif %}
            <li class="nav-item">
              <a class="nav-link" href="{% url 'account_login' %}">
                {% translate "Sign In" %}
              </a>
            </li>
            {% endif %}
          </ul>
        </div>
      </div>
    </nav>

    <div class="container">
      {% if messages %} {% for message in messages %}
      <div
        class="alert alert-dismissible {% if message.tags %}alert-{{ message.tags }}{% endif %}"
      >
        {{ message }}
        <button
          type="button"
          class="btn-close"
          data-bs-dismiss="alert"
          aria-label="Close"
        ></button>
      </div>
      {% endfor %} {% endif %} {% block content %}
      <p>Use this document as a quick-start for any new project.</p>
      {% endblock content %}
    </div>  {# end of your main container #}
  {% endblock body %}

  {% block modal %}
  {% endblock modal %}

  {% block inline_javascript %}
    {% comment %}
      Place inline scripts with only code (no src) here.
      They will run after your deferred external scripts.
    {% endcomment %}
  {% endblock inline_javascript %}
</body>
</html>
