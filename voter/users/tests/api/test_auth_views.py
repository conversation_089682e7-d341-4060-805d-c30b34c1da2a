import pytest
from django.urls import reverse
from rest_framework import status
from rest_framework.test import APIClient
from rest_framework.authtoken.models import Token
from django.utils import timezone
from datetime import timedelta

from voter.users.models import User, OTP
from voter.users.tests.factories import UserFactory
from companies.models import Company
from companies.tests.factories import CompanyFactory


@pytest.fixture
def api_client():
    return APIClient()


@pytest.fixture
def company(db):
    return CompanyFactory()


@pytest.fixture
def admin_user(db, company):
    user = UserFactory(
        role="Admin",
        mobile_number="+1234567890",
        company=company
    )
    return user


@pytest.fixture
def company_admin_user(db, company):
    user = UserFactory(
        role="CompanyAdmin",
        mobile_number="+1234567891",
        company=company
    )
    return user


@pytest.fixture
def surveyor_user(db, company):
    user = UserFactory(
        role="Surveyor",
        mobile_number="+1234567892",
        company=company
    )
    return user


class TestSendOTP:
    """Test cases for send OTP endpoint"""
    
    def test_send_otp_success(self, api_client, db):
        """Test successful OTP sending"""
        # Create user with the test mobile number
        User.objects.create_user(username='testuser', mobile_number='+1234567890', password='testpass123')
        url = reverse('api:send-otp')
        data = {'mobile_number': '+1234567890'}
        
        response = api_client.post(url, data)
        
        # Debug output
        print(f"Response status: {response.status_code}")
        print(f"Response data: {response.data}")
        
        assert response.status_code == status.HTTP_200_OK
        assert response.data['success'] is True
        assert response.data['message'] == 'OTP sent successfully'
        assert 'otp_code' in response.data
        
        # Check if OTP was created in database
        otp = OTP.objects.filter(mobile_number='+1234567890').first()
        assert otp is not None
        assert otp.is_valid() is True
    
    def test_send_otp_invalid_mobile(self, api_client, db):
        """Test OTP sending with invalid mobile number"""
        url = reverse('api:send-otp')
        data = {'mobile_number': 'invalid'}
        
        response = api_client.post(url, data)
        
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert 'mobile_number' in response.data
    
    def test_send_otp_missing_mobile(self, api_client, db):
        """Test OTP sending without mobile number"""
        url = reverse('api:send-otp')
        data = {}
        
        response = api_client.post(url, data)
        
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert 'mobile_number' in response.data
    
    def test_send_otp_existing_user(self, api_client, db, surveyor_user):
        """Test OTP sending for existing user"""
        url = reverse('api:send-otp')
        data = {'mobile_number': surveyor_user.mobile_number}
        
        response = api_client.post(url, data)
        
        assert response.status_code == status.HTTP_200_OK
        assert response.data['success'] is True


class TestVerifyOTP:
    """Test cases for verify OTP endpoint"""
    
    def test_verify_otp_success(self, api_client, db, surveyor_user):
        """Test successful OTP verification"""
        # Create OTP first
        otp = OTP.objects.create(
            mobile_number=surveyor_user.mobile_number,
            otp_code=123456
        )
        
        url = reverse('api:verify-otp')
        data = {
            'mobile_number': surveyor_user.mobile_number,
            'otp_code': 123456
        }
        
        response = api_client.post(url, data)
        
        assert response.status_code == status.HTTP_200_OK
        assert response.data['success'] is True
        assert response.data['message'] == 'OTP verified successfully'
        assert 'token' in response.data
        assert 'user' in response.data
        
        # Check if OTP was marked as used
        otp.refresh_from_db()
        assert otp.is_used is True
    
    def test_verify_otp_invalid_code(self, api_client, db, surveyor_user):
        """Test OTP verification with invalid code"""
        # Create OTP first
        OTP.objects.create(
            mobile_number=surveyor_user.mobile_number,
            otp_code=123456
        )
        
        url = reverse('api:verify-otp')
        data = {
            'mobile_number': surveyor_user.mobile_number,
            'otp_code': 999999
        }
        
        response = api_client.post(url, data)
        
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert response.data['success'] is False
    
    def test_verify_otp_expired(self, api_client, db, surveyor_user):
        """Test OTP verification with expired OTP"""
        # Create expired OTP
        otp = OTP.objects.create(
            mobile_number=surveyor_user.mobile_number,
            otp_code=123456,
            expires_at=timezone.now() - timedelta(minutes=1)
        )
        
        url = reverse('api:verify-otp')
        data = {
            'mobile_number': surveyor_user.mobile_number,
            'otp_code': 123456
        }
        
        response = api_client.post(url, data)
        
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert response.data['success'] is False
    
    def test_verify_otp_already_used(self, api_client, db, surveyor_user):
        """Test OTP verification with already used OTP"""
        # Create used OTP
        otp = OTP.objects.create(
            mobile_number=surveyor_user.mobile_number,
            otp_code=123456,
            is_used=True
        )
        
        url = reverse('api:verify-otp')
        data = {
            'mobile_number': surveyor_user.mobile_number,
            'otp_code': 123456
        }
        
        response = api_client.post(url, data)
        
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert response.data['success'] is False


class TestWebLogin:
    """Test cases for web login endpoint"""
    
    def test_web_login_success(self, api_client, db, surveyor_user):
        """Test successful web login"""
        # Set a known password for the user
        surveyor_user.set_password('testpass123')
        surveyor_user.save()
        
        url = reverse('api:web-login')
        data = {
            'username_or_email': surveyor_user.username,
            'password': 'testpass123'
        }
        
        response = api_client.post(url, data)
        
        assert response.status_code == status.HTTP_200_OK
        assert response.data['success'] is True
        assert response.data['message'] == 'Login successful'
        assert 'token' in response.data
        assert 'user' in response.data
    
    def test_web_login_invalid_credentials(self, api_client, db, surveyor_user):
        """Test web login with invalid credentials"""
        # Set a known password for the user
        surveyor_user.set_password('testpass123')
        surveyor_user.save()
        
        url = reverse('api:web-login')
        data = {
            'username_or_email': surveyor_user.username,
            'password': 'wrongpassword'
        }
        
        response = api_client.post(url, data)
        
        assert response.status_code == status.HTTP_401_UNAUTHORIZED
        assert response.data['success'] is False
    
    def test_web_login_nonexistent_user(self, api_client, db):
        """Test web login with nonexistent user"""
        url = reverse('api:web-login')
        data = {
            'username_or_email': 'nonexistent',
            'password': 'testpass123'
        }
        
        response = api_client.post(url, data)
        
        assert response.status_code == status.HTTP_401_UNAUTHORIZED


class TestLogout:
    """Test cases for logout endpoint"""
    
    def test_logout_success(self, api_client, db, surveyor_user):
        """Test successful logout"""
        # Create token for user
        token, _ = Token.objects.get_or_create(user=surveyor_user)
        api_client.credentials(HTTP_AUTHORIZATION=f'Token {token.key}')
        
        url = reverse('api:logout')
        response = api_client.post(url)
        
        assert response.status_code == status.HTTP_200_OK
        assert response.data['success'] is True
        assert response.data['message'] == 'Logged out successfully'
        
        # Check if token was deleted
        assert not Token.objects.filter(user=surveyor_user).exists()
    
    def test_logout_unauthorized(self, api_client, db):
        """Test logout without authentication"""
        url = reverse('api:logout')
        response = api_client.post(url)
        
        assert response.status_code == status.HTTP_401_UNAUTHORIZED


class TestForceLogoutAllDevices:
    """Test cases for force logout all devices endpoint"""
    
    def test_force_logout_all_devices_success(self, api_client, db, surveyor_user):
        """Test successful force logout from all devices"""
        # Create token for user
        token, _ = Token.objects.get_or_create(user=surveyor_user)
        api_client.credentials(HTTP_AUTHORIZATION=f'Token {token.key}')
        
        url = reverse('api:force-logout-all')
        response = api_client.post(url)
        
        assert response.status_code == status.HTTP_200_OK
        assert response.data['success'] is True
        assert response.data['message'] == 'Force logout successful'
        assert 'timestamp' in response.data
        
        # Check if user's last_logout_timestamp was updated
        surveyor_user.refresh_from_db()
        assert surveyor_user.last_logout_timestamp is not None
    
    def test_force_logout_all_devices_unauthorized(self, api_client, db):
        """Test force logout without authentication"""
        url = reverse('api:force-logout-all')
        response = api_client.post(url)
        
        assert response.status_code == status.HTTP_401_UNAUTHORIZED


class TestCheckTokenValidity:
    """Test cases for check token validity endpoint"""
    
    def test_check_token_validity_success(self, api_client, db, surveyor_user):
        """Test successful token validity check"""
        # Create token for user
        token, _ = Token.objects.get_or_create(user=surveyor_user)
        api_client.credentials(HTTP_AUTHORIZATION=f'Token {token.key}')
        
        url = reverse('api:check-token')
        response = api_client.get(url)
        
        assert response.status_code == status.HTTP_200_OK
        assert response.data['success'] is True
        assert response.data['message'] == 'Token is valid'
        assert 'user' in response.data
    
    def test_check_token_validity_unauthorized(self, api_client, db):
        """Test token validity check without authentication"""
        url = reverse('api:check-token')
        response = api_client.get(url)
        
        assert response.status_code == status.HTTP_401_UNAUTHORIZED
    
    def test_check_token_validity_invalid_token(self, api_client, db):
        """Test token validity check with invalid token"""
        api_client.credentials(HTTP_AUTHORIZATION='Token invalid_token')
        
        url = reverse('api:check-token')
        response = api_client.get(url)
        
        assert response.status_code == status.HTTP_401_UNAUTHORIZED


class TestGetUserPermissions:
    """Test cases for get user permissions endpoint"""
    
    def test_get_user_permissions_admin(self, api_client, db, admin_user):
        """Test getting permissions for admin user"""
        # Create token for user
        token, _ = Token.objects.get_or_create(user=admin_user)
        api_client.credentials(HTTP_AUTHORIZATION=f'Token {token.key}')
        
        url = reverse('api:user-permissions')
        response = api_client.get(url)
        
        assert response.status_code == status.HTTP_200_OK
        assert response.data['success'] is True
        assert 'permissions' in response.data
        
        permissions = response.data['permissions']
        assert permissions['can_manage_users'] is True
        assert permissions['can_manage_teams'] is True
        assert permissions['can_manage_surveys'] is True
    
    def test_get_user_permissions_surveyor(self, api_client, db, surveyor_user):
        """Test getting permissions for surveyor user"""
        # Create token for user
        token, _ = Token.objects.get_or_create(user=surveyor_user)
        api_client.credentials(HTTP_AUTHORIZATION=f'Token {token.key}')
        
        url = reverse('api:user-permissions')
        response = api_client.get(url)
        
        assert response.status_code == status.HTTP_200_OK
        assert response.data['success'] is True
        assert 'permissions' in response.data
        
        permissions = response.data['permissions']
        assert permissions['can_manage_users'] is False
        assert permissions['can_manage_teams'] is False
        assert permissions['can_collect_samples'] is True
    
    def test_get_user_permissions_unauthorized(self, api_client, db):
        """Test getting permissions without authentication"""
        url = reverse('api:user-permissions')
        response = api_client.get(url)
        
        assert response.status_code == status.HTTP_401_UNAUTHORIZED 