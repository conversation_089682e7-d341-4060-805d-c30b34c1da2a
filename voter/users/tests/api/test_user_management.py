import pytest
from django.urls import reverse
from rest_framework import status
from rest_framework.test import APIClient
from rest_framework.authtoken.models import Token

from voter.users.models import User
from voter.users.tests.factories import UserFactory
from companies.models import Company
from companies.tests.factories import CompanyFactory


@pytest.fixture
def api_client():
    return APIClient()


@pytest.fixture
def company(db):
    return CompanyFactory()


@pytest.fixture
def admin_user(db, company):
    user = UserFactory(
        role="Admin",
        mobile_number="+1234567890",
        company=company
    )
    return user


@pytest.fixture
def company_admin_user(db, company):
    user = UserFactory(
        role="CompanyAdmin",
        mobile_number="+1234567891",
        company=company
    )
    return user


@pytest.fixture
def surveyor_user(db, company):
    user = UserFactory(
        role="Surveyor",
        mobile_number="+1234567892",
        company=company
    )
    return user


@pytest.fixture
def qc_reviewer_user(db, company):
    user = UserFactory(
        role="QCReviewer",
        mobile_number="+1234567893",
        company=company
    )
    return user


@pytest.fixture
def vendor_surveyor_user(db, company):
    user = UserFactory(
        role="VendorSurveyor",
        mobile_number="+1234567894",
        company=company
    )
    return user


class TestUserViewSet:
    """Test cases for UserViewSet"""
    
    def test_list_users_authenticated(self, api_client, db, admin_user):
        """Test listing users with authentication"""
        token, _ = Token.objects.get_or_create(user=admin_user)
        api_client.credentials(HTTP_AUTHORIZATION=f'Token {token.key}')
        
        # Create some test users
        UserFactory(company=admin_user.company)
        UserFactory(company=admin_user.company)
        
        url = reverse('api:user-list')
        response = api_client.get(url)
        
        assert response.status_code == status.HTTP_200_OK
        assert len(response.data) >= 3  # admin_user + 2 created users
    
    def test_list_users_unauthorized(self, api_client, db):
        """Test listing users without authentication"""
        url = reverse('api:user-list')
        response = api_client.get(url)
        
        assert response.status_code == status.HTTP_401_UNAUTHORIZED
    
    def test_retrieve_user_authenticated(self, api_client, db, admin_user, surveyor_user):
        """Test retrieving a specific user"""
        token, _ = Token.objects.get_or_create(user=admin_user)
        api_client.credentials(HTTP_AUTHORIZATION=f'Token {token.key}')
        
        url = reverse('api:user-detail', kwargs={'username': surveyor_user.username})
        response = api_client.get(url)
        
        assert response.status_code == status.HTTP_200_OK
        assert response.data['username'] == surveyor_user.username
    
    def test_create_user_authenticated(self, api_client, db, admin_user):
        """Test creating a new user"""
        token, _ = Token.objects.get_or_create(user=admin_user)
        api_client.credentials(HTTP_AUTHORIZATION=f'Token {token.key}')
        
        url = reverse('api:user-list')
        data = {
            'username': 'newuser',
            'email': '<EMAIL>',
            'name': 'New User',
            'mobile_number': '+1234567895',
            'role': 'Surveyor',
            'company': admin_user.company.id
        }
        
        response = api_client.post(url, data)
        
        assert response.status_code == status.HTTP_201_CREATED
        assert response.data['username'] == 'newuser'
        assert response.data['role'] == 'Surveyor'
    
    def test_update_user_authenticated(self, api_client, db, admin_user, surveyor_user):
        """Test updating a user"""
        token, _ = Token.objects.get_or_create(user=admin_user)
        api_client.credentials(HTTP_AUTHORIZATION=f'Token {token.key}')
        
        url = reverse('api:user-detail', kwargs={'username': surveyor_user.username})
        data = {'name': 'Updated Name'}
        response = api_client.patch(url, data)
        
        assert response.status_code == status.HTTP_200_OK
        assert response.data['name'] == 'Updated Name'
    
    def test_delete_user_authenticated(self, api_client, db, admin_user, surveyor_user):
        """Test deleting a user"""
        token, _ = Token.objects.get_or_create(user=admin_user)
        api_client.credentials(HTTP_AUTHORIZATION=f'Token {token.key}')
        
        url = reverse('api:user-detail', kwargs={'username': surveyor_user.username})
        response = api_client.delete(url)
        
        assert response.status_code == status.HTTP_204_NO_CONTENT
        
        # Check if user is soft deleted
        surveyor_user.refresh_from_db()
        assert surveyor_user.is_deleted is True
    
    def test_soft_delete_user_action(self, api_client, db, admin_user, surveyor_user):
        """Test soft delete action"""
        token, _ = Token.objects.get_or_create(user=admin_user)
        api_client.credentials(HTTP_AUTHORIZATION=f'Token {token.key}')
        
        url = reverse('api:user-soft-delete', kwargs={'username': surveyor_user.username})
        response = api_client.delete(url)
        
        assert response.status_code == status.HTTP_200_OK
        assert response.data['success'] is True
        
        # Check if user is soft deleted
        surveyor_user.refresh_from_db()
        assert surveyor_user.is_deleted is True


class TestGetUsersByRole:
    """Test cases for get users by role endpoint"""
    
    def test_get_users_by_role_success(self, api_client, db, admin_user, surveyor_user, qc_reviewer_user):
        """Test getting users by role successfully"""
        token, _ = Token.objects.get_or_create(user=admin_user)
        api_client.credentials(HTTP_AUTHORIZATION=f'Token {token.key}')
        
        url = reverse('api:users-by-role')
        response = api_client.get(url, {'role': 'Surveyor'})
        
        assert response.status_code == status.HTTP_200_OK
        assert response.data['success'] is True
        assert 'users' in response.data
        assert 'count' in response.data
        assert response.data['count'] >= 1
        
        # Check if surveyor user is in the response
        user_ids = [user['id'] for user in response.data['users']]
        assert surveyor_user.id in user_ids
    
    def test_get_users_by_role_invalid_role(self, api_client, db, admin_user):
        """Test getting users by invalid role"""
        token, _ = Token.objects.get_or_create(user=admin_user)
        api_client.credentials(HTTP_AUTHORIZATION=f'Token {token.key}')
        
        url = reverse('api:users-by-role')
        response = api_client.get(url, {'role': 'InvalidRole'})
        
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert response.data['success'] is False
    
    def test_get_users_by_role_missing_role(self, api_client, db, admin_user):
        """Test getting users without specifying role"""
        token, _ = Token.objects.get_or_create(user=admin_user)
        api_client.credentials(HTTP_AUTHORIZATION=f'Token {token.key}')
        
        url = reverse('api:users-by-role')
        response = api_client.get(url)
        
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert response.data['success'] is False
    
    def test_get_users_by_role_unauthorized(self, api_client, db):
        """Test getting users by role without authentication"""
        url = reverse('api:users-by-role')
        response = api_client.get(url, {'role': 'Surveyor'})
        
        assert response.status_code == status.HTTP_401_UNAUTHORIZED
    
    def test_get_users_by_role_insufficient_permissions(self, api_client, db, surveyor_user):
        """Test getting users by role with insufficient permissions"""
        token, _ = Token.objects.get_or_create(user=surveyor_user)
        api_client.credentials(HTTP_AUTHORIZATION=f'Token {token.key}')
        
        url = reverse('api:users-by-role')
        response = api_client.get(url, {'role': 'Surveyor'})
        
        assert response.status_code == status.HTTP_403_FORBIDDEN


class TestCreateUser:
    """Test cases for create user endpoint"""
    
    def test_create_user_success(self, api_client, db, admin_user):
        """Test creating a user successfully"""
        token, _ = Token.objects.get_or_create(user=admin_user)
        api_client.credentials(HTTP_AUTHORIZATION=f'Token {token.key}')
        
        url = reverse('api:create-user')
        data = {
            'username': 'newuser',
            'email': '<EMAIL>',
            'name': 'New User',
            'mobile_number': '+1234567895',
            'role': 'Surveyor',
            'company': admin_user.company.id,
            'password': 'testpass123'
        }
        
        response = api_client.post(url, data)
        
        assert response.status_code == status.HTTP_201_CREATED
        assert response.data['success'] is True
        assert response.data['message'] == 'User created successfully'
        assert 'user' in response.data
        
        # Check if user was created in database
        user = User.objects.get(username='newuser')
        assert user.role == 'Surveyor'
        assert user.company == admin_user.company
        assert user.created_by == admin_user
    
    def test_create_user_invalid_data(self, api_client, db, admin_user):
        """Test creating a user with invalid data"""
        token, _ = Token.objects.get_or_create(user=admin_user)
        api_client.credentials(HTTP_AUTHORIZATION=f'Token {token.key}')
        
        url = reverse('api:create-user')
        data = {
            'username': 'newuser',
            'email': 'invalid-email',
            'role': 'InvalidRole'
        }
        
        response = api_client.post(url, data)
        
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert response.data['success'] is False
        assert 'errors' in response.data
    
    def test_create_user_duplicate_username(self, api_client, db, admin_user, surveyor_user):
        """Test creating a user with duplicate username"""
        token, _ = Token.objects.get_or_create(user=admin_user)
        api_client.credentials(HTTP_AUTHORIZATION=f'Token {token.key}')
        
        url = reverse('api:create-user')
        data = {
            'username': surveyor_user.username,  # Duplicate username
            'email': '<EMAIL>',
            'name': 'New User',
            'mobile_number': '+1234567895',
            'role': 'Surveyor',
            'company': admin_user.company.id,
            'password': 'testpass123'
        }
        
        response = api_client.post(url, data)
        
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert response.data['success'] is False
    
    def test_create_user_unauthorized(self, api_client, db):
        """Test creating a user without authentication"""
        url = reverse('api:create-user')
        data = {
            'username': 'newuser',
            'email': '<EMAIL>',
            'role': 'Surveyor'
        }
        
        response = api_client.post(url, data)
        
        assert response.status_code == status.HTTP_401_UNAUTHORIZED
    
    def test_create_user_insufficient_permissions(self, api_client, db, surveyor_user):
        """Test creating a user with insufficient permissions"""
        token, _ = Token.objects.get_or_create(user=surveyor_user)
        api_client.credentials(HTTP_AUTHORIZATION=f'Token {token.key}')
        
        url = reverse('api:create-user')
        data = {
            'username': 'newuser',
            'email': '<EMAIL>',
            'role': 'Surveyor'
        }
        
        response = api_client.post(url, data)
        
        assert response.status_code == status.HTTP_403_FORBIDDEN


class TestUpdateUser:
    """Test cases for update user endpoint"""
    
    def test_update_user_success(self, api_client, db, admin_user, surveyor_user):
        """Test updating a user successfully"""
        token, _ = Token.objects.get_or_create(user=admin_user)
        api_client.credentials(HTTP_AUTHORIZATION=f'Token {token.key}')
        
        url = reverse('api:update-user', kwargs={'user_id': surveyor_user.id})
        data = {
            'name': 'Updated Name',
            'role': 'QCReviewer',
            'mobile_number': '+1234567896'
        }
        
        response = api_client.put(url, data)
        
        assert response.status_code == status.HTTP_200_OK
        assert response.data['success'] is True
        assert response.data['message'] == 'User updated successfully'
        assert 'user' in response.data
        
        # Check if user was updated in database
        surveyor_user.refresh_from_db()
        assert surveyor_user.name == 'Updated Name'
        assert surveyor_user.role == 'QCReviewer'
        assert surveyor_user.mobile_number == '+1234567896'
    
    def test_update_user_partial(self, api_client, db, admin_user, surveyor_user):
        """Test partial update of a user"""
        token, _ = Token.objects.get_or_create(user=admin_user)
        api_client.credentials(HTTP_AUTHORIZATION=f'Token {token.key}')
        
        url = reverse('api:update-user', kwargs={'user_id': surveyor_user.id})
        data = {
            'name': 'Updated Name'
        }
        
        response = api_client.patch(url, data)
        
        assert response.status_code == status.HTTP_200_OK
        assert response.data['success'] is True
        
        # Check if only name was updated
        surveyor_user.refresh_from_db()
        assert surveyor_user.name == 'Updated Name'
        # Other fields should remain unchanged
        assert surveyor_user.role == 'Surveyor'
    
    def test_update_user_invalid_data(self, api_client, db, admin_user, surveyor_user):
        """Test updating a user with invalid data"""
        token, _ = Token.objects.get_or_create(user=admin_user)
        api_client.credentials(HTTP_AUTHORIZATION=f'Token {token.key}')
        
        url = reverse('api:update-user', kwargs={'user_id': surveyor_user.id})
        data = {
            'email': 'invalid-email',
            'role': 'InvalidRole'
        }
        
        response = api_client.put(url, data)
        
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert response.data['success'] is False
        assert 'errors' in response.data
    
    def test_update_user_not_found(self, api_client, db, admin_user):
        """Test updating a non-existent user"""
        token, _ = Token.objects.get_or_create(user=admin_user)
        api_client.credentials(HTTP_AUTHORIZATION=f'Token {token.key}')
        
        url = reverse('api:update-user', kwargs={'user_id': 99999})
        data = {
            'name': 'Updated Name'
        }
        
        response = api_client.put(url, data)
        
        assert response.status_code == status.HTTP_404_NOT_FOUND
        assert response.data['success'] is False
    
    def test_update_user_unauthorized(self, api_client, db, surveyor_user):
        """Test updating a user without authentication"""
        url = reverse('api:update-user', kwargs={'user_id': surveyor_user.id})
        data = {
            'name': 'Updated Name'
        }
        
        response = api_client.put(url, data)
        
        assert response.status_code == status.HTTP_401_UNAUTHORIZED
    
    def test_update_user_insufficient_permissions(self, api_client, db, surveyor_user, qc_reviewer_user):
        """Test updating a user with insufficient permissions"""
        token, _ = Token.objects.get_or_create(user=surveyor_user)
        api_client.credentials(HTTP_AUTHORIZATION=f'Token {token.key}')
        
        url = reverse('api:update-user', kwargs={'user_id': qc_reviewer_user.id})
        data = {
            'name': 'Updated Name'
        }
        
        response = api_client.put(url, data)
        
        assert response.status_code == status.HTTP_403_FORBIDDEN 