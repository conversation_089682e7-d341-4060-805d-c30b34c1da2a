import pytest
from rest_framework.test import APIRequestFactory

from voter.users.api.views import UserViewSet
from voter.users.models import User


class TestUserViewSet:
    @pytest.fixture
    def api_rf(self) -> APIRequestFactory:
        return APIRequestFactory()

    def test_get_queryset(self, user: User, api_rf: APIRequestFactory):
        view = UserViewSet()
        request = api_rf.get("/fake-url/")
        request.user = user

        view.request = request

        assert user in view.get_queryset()

    def test_me(self, user: User, api_rf: APIRequestFactory):
        view = UserViewSet()
        request = api_rf.get("/fake-url/")
        request.user = user

        view.request = request

        response = view.me(request)  # type: ignore[call-arg, arg-type, misc]

        # Check that response contains expected user data
        assert response.data['username'] == user.username
        assert response.data['name'] == user.name
        assert 'id' in response.data
        assert 'permissions' in response.data
