import pytest
from django.test import TestCase
from rest_framework.test import APITestCase

from voter.users.models import User, OTP
from voter.users.tests.factories import UserFactory
from voter.users.api.serializers import (
    UserSerializer, SendOTPSerializer, VerifyOTPSerializer,
    WebLoginSerializer, UserDetailSerializer, UserCreateSerializer,
    UserUpdateSerializer
)
from companies.tests.factories import CompanyFactory


class TestUserSerializer(TestCase):
    """Test cases for UserSerializer"""
    
    def setUp(self):
        self.company = CompanyFactory()
        self.user = UserFactory(company=self.company)
    
    def test_user_serializer_valid_data(self):
        """Test UserSerializer with valid data"""
        serializer = UserSerializer(self.user)
        data = serializer.data
        
        assert data['id'] == self.user.id
        assert data['username'] == self.user.username
        assert data['email'] == self.user.email
        assert data['name'] == self.user.name
        assert data['mobile_number'] == self.user.mobile_number
        assert data['role'] == self.user.role
        assert data['is_deleted'] == self.user.is_deleted
        assert data['created_at'] is not None
    
    def test_user_serializer_includes_company_info(self):
        """Test UserSerializer includes company information"""
        serializer = UserSerializer(self.user)
        data = serializer.data
        
        assert 'company' in data
        assert data['company']['id'] == self.company.id
        assert data['company']['name'] == self.company.name
    
    def test_user_serializer_excludes_sensitive_fields(self):
        """Test UserSerializer excludes sensitive fields"""
        serializer = UserSerializer(self.user)
        data = serializer.data
        
        # Should not include password
        assert 'password' not in data
        # Should not include last_login
        assert 'last_login' not in data
        # Should not include is_superuser
        assert 'is_superuser' not in data


class TestUserDetailSerializer(TestCase):
    """Test cases for UserDetailSerializer"""
    
    def setUp(self):
        self.company = CompanyFactory()
        self.user = UserFactory(company=self.company)
    
    def test_user_detail_serializer_valid_data(self):
        """Test UserDetailSerializer with valid data"""
        serializer = UserDetailSerializer(self.user)
        data = serializer.data
        
        assert data['id'] == self.user.id
        assert data['username'] == self.user.username
        assert data['email'] == self.user.email
        assert data['name'] == self.user.name
        assert data['mobile_number'] == self.user.mobile_number
        assert data['role'] == self.user.role
        assert data['dob'] == self.user.dob
        assert data['is_deleted'] == self.user.is_deleted
        assert data['created_at'] is not None
    
    def test_user_detail_serializer_includes_company_info(self):
        """Test UserDetailSerializer includes company information"""
        serializer = UserDetailSerializer(self.user)
        data = serializer.data
        
        assert 'company' in data
        assert data['company']['id'] == self.company.id
        assert data['company']['name'] == self.company.name
    
    def test_user_detail_serializer_includes_created_by(self):
        """Test UserDetailSerializer includes created_by information"""
        # Create a user with created_by
        creator = UserFactory(company=self.company)
        user = UserFactory(company=self.company, created_by=creator)
        
        serializer = UserDetailSerializer(user)
        data = serializer.data
        
        assert 'created_by' in data
        assert data['created_by']['id'] == creator.id
        assert data['created_by']['username'] == creator.username


class TestUserCreateSerializer(TestCase):
    """Test cases for UserCreateSerializer"""
    
    def setUp(self):
        self.company = CompanyFactory()
        self.admin_user = UserFactory(role="Admin", company=self.company)
    
    def test_user_create_serializer_valid_data(self):
        """Test UserCreateSerializer with valid data"""
        data = {
            'username': 'newuser',
            'email': '<EMAIL>',
            'name': 'New User',
            'mobile_number': '+1234567890',
            'role': 'Surveyor',
            'company': self.company.id,
            'password': 'testpass123'
        }
        
        serializer = UserCreateSerializer(data=data)
        assert serializer.is_valid()
        
        user = serializer.save(created_by=self.admin_user)
        assert user.username == 'newuser'
        assert user.email == '<EMAIL>'
        assert user.name == 'New User'
        assert user.mobile_number == '+1234567890'
        assert user.role == 'Surveyor'
        assert user.company == self.company
        assert user.created_by == self.admin_user
        assert user.check_password('testpass123')
    
    def test_user_create_serializer_invalid_data(self):
        """Test UserCreateSerializer with invalid data"""
        data = {
            'username': '',  # Empty username
            'email': 'invalid-email',
            'role': 'InvalidRole',
            'password': 'short'  # Too short password
        }
        
        serializer = UserCreateSerializer(data=data)
        assert not serializer.is_valid()
        assert 'username' in serializer.errors
        assert 'email' in serializer.errors
        assert 'role' in serializer.errors
        assert 'password' in serializer.errors
    
    def test_user_create_serializer_duplicate_username(self):
        """Test UserCreateSerializer with duplicate username"""
        # Create a user first
        existing_user = UserFactory(username='existinguser')
        
        data = {
            'username': 'existinguser',  # Duplicate username
            'email': '<EMAIL>',
            'name': 'New User',
            'role': 'Surveyor',
            'company': self.company.id,
            'password': 'testpass123'
        }
        
        serializer = UserCreateSerializer(data=data)
        assert not serializer.is_valid()
        assert 'username' in serializer.errors
    
    def test_user_create_serializer_duplicate_email(self):
        """Test UserCreateSerializer with duplicate email"""
        # Create a user first
        existing_user = UserFactory(email='<EMAIL>')
        
        data = {
            'username': 'newuser',
            'email': '<EMAIL>',  # Duplicate email
            'name': 'New User',
            'role': 'Surveyor',
            'company': self.company.id,
            'password': 'testpass123'
        }
        
        serializer = UserCreateSerializer(data=data)
        assert not serializer.is_valid()
        assert 'email' in serializer.errors
    
    def test_user_create_serializer_duplicate_mobile(self):
        """Test UserCreateSerializer with duplicate mobile number"""
        # Create a user first
        existing_user = UserFactory(mobile_number='+1234567890')
        
        data = {
            'username': 'newuser',
            'email': '<EMAIL>',
            'name': 'New User',
            'mobile_number': '+1234567890',  # Duplicate mobile
            'role': 'Surveyor',
            'company': self.company.id,
            'password': 'testpass123'
        }
        
        serializer = UserCreateSerializer(data=data)
        assert not serializer.is_valid()
        assert 'mobile_number' in serializer.errors


class TestUserUpdateSerializer(TestCase):
    """Test cases for UserUpdateSerializer"""
    
    def setUp(self):
        self.company = CompanyFactory()
        self.user = UserFactory(company=self.company)
    
    def test_user_update_serializer_valid_data(self):
        """Test UserUpdateSerializer with valid data"""
        data = {
            'name': 'Updated Name',
            'email': '<EMAIL>',
            'mobile_number': '+1234567891',
            'role': 'QCReviewer',
            'dob': '1990-01-01'
        }
        
        serializer = UserUpdateSerializer(self.user, data=data, partial=True)
        assert serializer.is_valid()
        
        user = serializer.save()
        assert user.name == 'Updated Name'
        assert user.email == '<EMAIL>'
        assert user.mobile_number == '+1234567891'
        assert user.role == 'QCReviewer'
        assert user.dob.strftime('%Y-%m-%d') == '1990-01-01'
    
    def test_user_update_serializer_invalid_data(self):
        """Test UserUpdateSerializer with invalid data"""
        data = {
            'email': 'invalid-email',
            'role': 'InvalidRole',
            'mobile_number': 'invalid-mobile'
        }
        
        serializer = UserUpdateSerializer(self.user, data=data, partial=True)
        assert not serializer.is_valid()
        assert 'email' in serializer.errors
        assert 'role' in serializer.errors
        assert 'mobile_number' in serializer.errors
    
    def test_user_update_serializer_duplicate_email(self):
        """Test UserUpdateSerializer with duplicate email"""
        # Create another user
        other_user = UserFactory(email='<EMAIL>', company=self.company)
        
        data = {
            'email': '<EMAIL>'  # Duplicate email
        }
        
        serializer = UserUpdateSerializer(self.user, data=data, partial=True)
        assert not serializer.is_valid()
        assert 'email' in serializer.errors
    
    def test_user_update_serializer_duplicate_mobile(self):
        """Test UserUpdateSerializer with duplicate mobile number"""
        # Create another user
        other_user = UserFactory(mobile_number='+1234567891', company=self.company)
        
        data = {
            'mobile_number': '+1234567891'  # Duplicate mobile
        }
        
        serializer = UserUpdateSerializer(self.user, data=data, partial=True)
        assert not serializer.is_valid()
        assert 'mobile_number' in serializer.errors


class TestSendOTPSerializer(TestCase):
    """Test cases for SendOTPSerializer"""
    
    def test_send_otp_serializer_valid_data(self):
        """Test SendOTPSerializer with valid data"""
        data = {
            'mobile_number': '+1234567890'
        }
        
        serializer = SendOTPSerializer(data=data)
        assert serializer.is_valid()
        assert serializer.validated_data['mobile_number'] == '+1234567890'
    
    def test_send_otp_serializer_invalid_mobile(self):
        """Test SendOTPSerializer with invalid mobile number"""
        data = {
            'mobile_number': 'invalid-mobile'
        }
        
        serializer = SendOTPSerializer(data=data)
        assert not serializer.is_valid()
        assert 'mobile_number' in serializer.errors
    
    def test_send_otp_serializer_missing_mobile(self):
        """Test SendOTPSerializer without mobile number"""
        data = {}
        
        serializer = SendOTPSerializer(data=data)
        assert not serializer.is_valid()
        assert 'mobile_number' in serializer.errors
    
    def test_send_otp_serializer_empty_mobile(self):
        """Test SendOTPSerializer with empty mobile number"""
        data = {
            'mobile_number': ''
        }
        
        serializer = SendOTPSerializer(data=data)
        assert not serializer.is_valid()
        assert 'mobile_number' in serializer.errors


class TestVerifyOTPSerializer(TestCase):
    """Test cases for VerifyOTPSerializer"""
    
    def test_verify_otp_serializer_valid_data(self):
        """Test VerifyOTPSerializer with valid data"""
        data = {
            'mobile_number': '+1234567890',
            'otp_code': 123456
        }
        
        serializer = VerifyOTPSerializer(data=data)
        assert serializer.is_valid()
        assert serializer.validated_data['mobile_number'] == '+1234567890'
        assert serializer.validated_data['otp_code'] == 123456
    
    def test_verify_otp_serializer_invalid_mobile(self):
        """Test VerifyOTPSerializer with invalid mobile number"""
        data = {
            'mobile_number': 'invalid-mobile',
            'otp_code': 123456
        }
        
        serializer = VerifyOTPSerializer(data=data)
        assert not serializer.is_valid()
        assert 'mobile_number' in serializer.errors
    
    def test_verify_otp_serializer_invalid_otp(self):
        """Test VerifyOTPSerializer with invalid OTP code"""
        data = {
            'mobile_number': '+1234567890',
            'otp_code': 123  # Too short
        }
        
        serializer = VerifyOTPSerializer(data=data)
        assert serializer.is_valid()  # IntegerField accepts any integer
    
    def test_verify_otp_serializer_missing_fields(self):
        """Test VerifyOTPSerializer with missing fields"""
        data = {
            'mobile_number': '+1234567890'
            # Missing otp_code
        }
        
        serializer = VerifyOTPSerializer(data=data)
        assert not serializer.is_valid()
        assert 'otp_code' in serializer.errors
    
    def test_verify_otp_serializer_empty_fields(self):
        """Test VerifyOTPSerializer with empty fields"""
        data = {
            'mobile_number': '',
            'otp_code': ''
        }
        
        serializer = VerifyOTPSerializer(data=data)
        assert not serializer.is_valid()
        assert 'mobile_number' in serializer.errors
        assert 'otp_code' in serializer.errors


class TestWebLoginSerializer(TestCase):
    """Test cases for WebLoginSerializer"""
    
    def test_web_login_serializer_valid_data(self):
        """Test WebLoginSerializer with valid data"""
        data = {
            'username_or_email': 'testuser',
            'password': 'testpass123'
        }
        
        serializer = WebLoginSerializer(data=data)
        assert serializer.is_valid()
        assert serializer.validated_data['username_or_email'] == 'testuser'
        assert serializer.validated_data['password'] == 'testpass123'
    
    def test_web_login_serializer_invalid_username(self):
        """Test WebLoginSerializer with invalid username"""
        data = {
            'username_or_email': '',  # Empty username
            'password': 'testpass123'
        }
        
        serializer = WebLoginSerializer(data=data)
        assert not serializer.is_valid()
        assert 'username_or_email' in serializer.errors
    
    def test_web_login_serializer_invalid_password(self):
        """Test WebLoginSerializer with invalid password"""
        data = {
            'username_or_email': 'testuser',
            'password': ''  # Empty password
        }
        
        serializer = WebLoginSerializer(data=data)
        assert not serializer.is_valid()
        assert 'password' in serializer.errors
    
    def test_web_login_serializer_missing_fields(self):
        """Test WebLoginSerializer with missing fields"""
        data = {}
        
        serializer = WebLoginSerializer(data=data)
        assert not serializer.is_valid()
        assert 'username_or_email' in serializer.errors
        assert 'password' in serializer.errors
    
    def test_web_login_serializer_extra_fields(self):
        """Test WebLoginSerializer with extra fields (should be ignored)"""
        data = {
            'username_or_email': 'testuser',
            'password': 'testpass123',
            'extra_field': 'extra_value'
        }
        
        serializer = WebLoginSerializer(data=data)
        assert serializer.is_valid()
        # Extra fields should be ignored
        assert 'extra_field' not in serializer.validated_data 