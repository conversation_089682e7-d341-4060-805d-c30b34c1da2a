from rest_framework import permissions


class Is<PERSON>uperUser(permissions.BasePermission):
    """
    Custom permission to only allow superusers.
    """
    
    def has_permission(self, request, view):
        return bool(request.user and request.user.is_authenticated and request.user.is_superuser)


class IsAdmin(permissions.BasePermission):
    """
    Permission to only allow Admin users.
    """
    
    def has_permission(self, request, view):
        return bool(request.user and request.user.is_authenticated and request.user.is_admin())


class IsCompanyAdmin(permissions.BasePermission):
    """
    Permission to only allow Company Admin users.
    """
    
    def has_permission(self, request, view):
        return bool(request.user and request.user.is_authenticated and request.user.is_company_admin())


class IsSurveyor(permissions.BasePermission):
    """
    Permission to only allow Surveyor users.
    """
    
    def has_permission(self, request, view):
        return bool(request.user and request.user.is_authenticated and request.user.is_surveyor())


class IsVendorSurveyor(permissions.BasePermission):
    """
    Permission to only allow Vendor Surveyor users.
    """
    
    def has_permission(self, request, view):
        return bool(request.user and request.user.is_authenticated and request.user.is_vendor_surveyor())


class IsQCReviewer(permissions.BasePermission):
    """
    Permission to only allow QC Reviewer users.
    """
    
    def has_permission(self, request, view):
        return bool(request.user and request.user.is_authenticated and request.user.is_qc_reviewer())


class CanManageSurveys(permissions.BasePermission):
    """
    Permission to manage surveys (Admin and Company Admin).
    """
    
    def has_permission(self, request, view):
        return bool(request.user and request.user.is_authenticated and request.user.can_manage_surveys())


class CanCollectSamples(permissions.BasePermission):
    """
    Permission to collect survey samples (Surveyor and Vendor Surveyor).
    """
    
    def has_permission(self, request, view):
        return bool(request.user and request.user.is_authenticated and request.user.can_collect_samples())


class CanReviewSamples(permissions.BasePermission):
    """
    Permission to review samples (QC Reviewer).
    """
    
    def has_permission(self, request, view):
        return bool(request.user and request.user.is_authenticated and request.user.can_review_samples())


class CanManageUsers(permissions.BasePermission):
    """
    Permission to manage users (Admin and Company Admin).
    """
    
    def has_permission(self, request, view):
        return bool(request.user and request.user.is_authenticated and request.user.can_manage_users())


class CanCreateUsers(permissions.BasePermission):
    """
    Permission to create users with company-based restrictions.
    - Admin/Superuser can create users for any company
    - Company Admin can only create users for their own company
    """
    
    def has_permission(self, request, view):
        if not (request.user and request.user.is_authenticated):
            return False
        
        # Admin and Superuser can create users for any company
        if request.user.is_admin() or request.user.is_superuser:
            return True
        
        # Company Admin can only create users for their own company
        if request.user.is_company_admin():
            return True
        
        return False
    
    def has_object_permission(self, request, view, obj):
        # For object-level permissions (if needed)
        return self.has_permission(request, view)


class CanManageTeams(permissions.BasePermission):
    """
    Permission to manage teams (Admin and Company Admin).
    """
    
    def has_permission(self, request, view):
        return bool(request.user and request.user.is_authenticated and request.user.can_manage_teams())


class IsAdminOrCompanyAdmin(permissions.BasePermission):
    """
    Permission to allow Admin or Company Admin users.
    """
    
    def has_permission(self, request, view):
        return bool(
            request.user and 
            request.user.is_authenticated and 
            (request.user.is_admin() or request.user.is_company_admin())
        )


class IsSurveyorOrVendorSurveyor(permissions.BasePermission):
    """
    Permission to allow Surveyor or Vendor Surveyor users.
    """
    
    def has_permission(self, request, view):
        return bool(
            request.user and 
            request.user.is_authenticated and 
            (request.user.is_surveyor() or request.user.is_vendor_surveyor())
        ) 