from django.conf import settings
from django.contrib import admin
from django.contrib.auth.admin import UserAdmin as BaseUserAdmin
from django.utils.translation import gettext_lazy as _
from allauth.account.admin import EmailAddressAdmin
from allauth.account.models import EmailAddress
from allauth.socialaccount.admin import SocialAccount, SocialApp, SocialToken
from allauth.socialaccount.models import SocialAccount as SocialAccountModel
from allauth.socialaccount.models import SocialApp as SocialAppModel
from allauth.socialaccount.models import SocialToken as SocialTokenModel

from voter.users.models import User, OTP

if settings.DJANGO_ADMIN_FORCE_ALLAUTH:
    # Force the `admin` sign in process to go through the `django-allauth` workflow:
    # https://docs.allauth.org/en/latest/common/admin.html#admin
    admin.autodiscover()


@admin.register(User)
class UserAdmin(BaseUserAdmin):
    """User admin configuration"""

    list_display = [
        "username", "name", "mobile_number", "email", "role", 
        "company", "is_superuser", "last_logout_timestamp", "is_deleted"
    ]
    list_filter = [
        "role", "is_staff", "is_superuser", "is_active", "is_deleted", 
        "groups", "company", "last_logout_timestamp", "created_at"
    ]
    search_fields = ["username", "name", "email", "mobile_number"]
    ordering = ["username"]
    
    fieldsets = (
        (None, {"fields": ("username", "password")}),
        (_("Personal info"), {
            "fields": ("name", "email", "mobile_number", "dob", "role")
        }),
        (_("Company & Teams"), {
            "fields": ("company",)
        }),
        (
            _("Permissions"),
            {
                "fields": (
                    "is_active",
                    "is_staff",
                    "is_superuser",
                    "groups",
                    "user_permissions",
                ),
            },
        ),
        (_("Important dates"), {
            "fields": ("last_login", "date_joined", "last_logout_timestamp", "created_at")
        }),
        (_("System"), {
            "fields": ("is_deleted", "created_by")
        }),
    )
    
    add_fieldsets = (
        (
            None,
            {
                "classes": ("wide",),
                "fields": (
                    "username", "password1", "password2", "email", 
                    "mobile_number", "name", "role", "dob", "company"
                ),
            },
        ),
    )
    
    readonly_fields = ["last_logout_timestamp", "created_at"]


@admin.register(OTP)
class OTPAdmin(admin.ModelAdmin):
    """OTP admin configuration"""
    
    list_display = ["mobile_number", "otp_code", "is_used", "created_at", "expires_at", "is_expired"]
    list_filter = ["is_used", "created_at"]
    search_fields = ["mobile_number"]
    readonly_fields = ["created_at", "expires_at"]
    ordering = ["-created_at"]
    
    def is_expired(self, obj):
        return obj.is_expired()
    is_expired.boolean = True
    is_expired.short_description = "Expired"
