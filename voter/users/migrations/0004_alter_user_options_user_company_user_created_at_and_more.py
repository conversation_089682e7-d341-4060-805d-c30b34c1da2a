# Generated by Django 5.1.11 on 2025-06-28 13:37

import django.db.models.deletion
import django.utils.timezone
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('companies', '0001_initial'),
        ('users', '0003_user_last_logout_timestamp'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='user',
            options={'verbose_name': 'User', 'verbose_name_plural': 'Users'},
        ),
        migrations.AddField(
            model_name='user',
            name='company',
            field=models.ForeignKey(blank=True, help_text='Company the user belongs to', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='users', to='companies.company'),
        ),
        migrations.AddField(
            model_name='user',
            name='created_at',
            field=models.DateTimeField(default=django.utils.timezone.now, help_text='When this user was created', verbose_name='Created At'),
        ),
        migrations.AddField(
            model_name='user',
            name='created_by',
            field=models.ForeignKey(blank=True, help_text='User who created this user', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_users', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='user',
            name='dob',
            field=models.DateField(blank=True, help_text="User's date of birth", null=True, verbose_name='Date of Birth'),
        ),
        migrations.AddField(
            model_name='user',
            name='is_deleted',
            field=models.BooleanField(default=False, help_text='Soft delete flag', verbose_name='Is Deleted'),
        ),
        migrations.AddField(
            model_name='user',
            name='role',
            field=models.CharField(choices=[('Admin', 'Admin'), ('CompanyAdmin', 'Company Admin'), ('Surveyor', 'Surveyor'), ('VendorSurveyor', 'Vendor Surveyor'), ('QCReviewer', 'QC Reviewer')], default='Surveyor', help_text='User role in the system', max_length=20, verbose_name='Role'),
        ),
    ]
