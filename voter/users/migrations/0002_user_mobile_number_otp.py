# Generated by Django 5.1.11 on 2025-06-28 11:31

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('users', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='user',
            name='mobile_number',
            field=models.CharField(blank=True, help_text='Mobile number for OTP authentication', max_length=15, null=True, unique=True, verbose_name='Mobile Number'),
        ),
        migrations.CreateModel(
            name='OTP',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('mobile_number', models.CharField(max_length=15)),
                ('otp_code', models.IntegerField()),
                ('is_used', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('expires_at', models.DateTimeField()),
            ],
            options={
                'db_table': 'user_otp',
                'indexes': [models.Index(fields=['mobile_number', 'created_at'], name='user_otp_mobile__f3463b_idx')],
            },
        ),
    ]
