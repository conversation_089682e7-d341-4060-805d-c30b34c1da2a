from rest_framework.routers import Default<PERSON>outer
from .views import (
    UserViewSet, send_otp, verify_otp, web_login, logout, force_logout_all_devices,
    check_token_validity, get_user_permissions, get_users_by_role, create_user, update_user
)
from django.urls import path

router = DefaultRouter()
router.register(r'users', UserViewSet)

urlpatterns = router.urls + [
    path("auth/send-otp/", send_otp, name="send-otp"),
    path("auth/verify-otp/", verify_otp, name="verify-otp"),
    path("auth/web-login/", web_login, name="web-login"),
    path("auth/logout/", logout, name="logout"),
    path("auth/force-logout-all/", force_logout_all_devices, name="force-logout-all"),
    path("auth/check-token/", check_token_validity, name="check-token"),
    path("auth/permissions/", get_user_permissions, name="user-permissions"),
    path("users/by-role/", get_users_by_role, name="users-by-role"),
    path("users/create/", create_user, name="create-user"),
    path("users/<int:user_id>/update/", update_user, name="update-user"),
] 