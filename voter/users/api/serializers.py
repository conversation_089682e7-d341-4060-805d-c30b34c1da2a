import re
from rest_framework import serializers
from rest_framework.validators import UniqueValidator
from voter.users.models import User, OTP
from companies.models import Company


class CompanyNestedSerializer(serializers.ModelSerializer):
    class Meta:
        model = Company
        fields = ["id", "name", "code"]


class UserNestedSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = ["id", "username", "name"]


class UserSerializer(serializers.ModelSerializer):
    company = CompanyNestedSerializer(read_only=True)
    created_by = UserNestedSerializer(read_only=True)
    is_deleted = serializers.BooleanField(read_only=True)
    created_at = serializers.DateTimeField(read_only=True)
    
    class Meta:
        model = User
        fields = [
            "id", "username", "name", "mobile_number", "email", "role", "company",
            "is_deleted", "created_at", "created_by"
        ]


class SendOTPSerializer(serializers.Serializer):
    mobile_number = serializers.Char<PERSON>ield(max_length=15)
    
    def validate_mobile_number(self, value):
        # Basic mobile number validation (allows + and digits)
        if not re.match(r'^\+?[\d\s\-\(\)]+$', value):
            raise serializers.ValidationError("Invalid mobile number format")
        return value


class VerifyOTPSerializer(serializers.Serializer):
    mobile_number = serializers.CharField(max_length=15)
    otp_code = serializers.IntegerField()
    
    def validate_mobile_number(self, value):
        # Basic mobile number validation (allows + and digits)
        if not re.match(r'^\+?[\d\s\-\(\)]+$', value):
            raise serializers.ValidationError("Invalid mobile number format")
        return value


class WebLoginSerializer(serializers.Serializer):
    username_or_email = serializers.CharField()
    password = serializers.CharField(write_only=True)


class UserDetailSerializer(serializers.ModelSerializer):
    company = CompanyNestedSerializer(read_only=True)
    created_by = UserNestedSerializer(read_only=True)
    is_deleted = serializers.BooleanField(read_only=True)
    created_at = serializers.DateTimeField(read_only=True)
    permissions = serializers.SerializerMethodField()
    
    class Meta:
        model = User
        fields = [
            "id", "username", "name", "mobile_number", "email", "role", "dob", "company",
            "is_deleted", "created_at", "created_by", "permissions"
        ]
        read_only_fields = ["id", "created_at", "created_by", "is_deleted"]
    
    def get_permissions(self, obj):
        """Get user permissions based on role"""
        return {
            "can_manage_surveys": obj.can_manage_surveys(),
            "can_collect_samples": obj.can_collect_samples(),
            "can_review_samples": obj.can_review_samples(),
            "can_manage_users": obj.can_manage_users(),
            "can_manage_teams": obj.can_manage_teams(),
            "is_admin": obj.is_admin(),
            "is_company_admin": obj.is_company_admin(),
            "is_surveyor": obj.is_surveyor(),
            "is_vendor_surveyor": obj.is_vendor_surveyor(),
            "is_qc_reviewer": obj.is_qc_reviewer(),
        }


class UserCreateSerializer(serializers.ModelSerializer):
    password = serializers.CharField(write_only=True, min_length=8)
    username = serializers.CharField(validators=[UniqueValidator(queryset=User.objects.all())])
    email = serializers.EmailField(allow_blank=True, required=False, validators=[UniqueValidator(queryset=User.objects.all())])
    mobile_number = serializers.CharField(allow_blank=True, required=False, validators=[UniqueValidator(queryset=User.objects.all())])
    
    class Meta:
        model = User
        fields = [
            "username", "name", "mobile_number", "email", "password",
            "role", "dob", "company"
        ]
    
    def validate_password(self, value):
        if len(value) < 8:
            raise serializers.ValidationError("Password must be at least 8 characters long.")
        return value
    
    def validate_company(self, value):
        """Validate company assignment based on user permissions"""
        request = self.context.get('request')
        if not request or not request.user:
            return value
        
        # Admin and Superuser can assign any company
        if request.user.is_admin() or request.user.is_superuser:
            return value
        
        # Company Admin can only assign their own company
        if request.user.is_company_admin():
            if value and value != request.user.company:
                raise serializers.ValidationError(
                    "Company Admin can only create users for their own company."
                )
            # If no company specified, use the Company Admin's company
            if not value:
                return request.user.company
        
        return value
    
    def validate(self, data):
        """Validate that non-admin roles must have a company assigned"""
        role = data.get('role')
        company = data.get('company')
        
        # Admin role can exist without a company
        if role == "Admin":
            return data
        
        # All other roles (CompanyAdmin, Surveyor, VendorSurveyor, QCReviewer) must have a company
        if not company:
            raise serializers.ValidationError({
                'company': f"Company is required for role '{role}'. Only Admin role can exist without a company."
            })
        
        return data
    
    def create(self, validated_data):
        user = User.objects.create_user(**validated_data)
        return user


class UserUpdateSerializer(serializers.ModelSerializer):
    email = serializers.EmailField(allow_blank=True, required=False, validators=[UniqueValidator(queryset=User.objects.all())])
    mobile_number = serializers.CharField(allow_blank=True, required=False, validators=[UniqueValidator(queryset=User.objects.all())])
    
    class Meta:
        model = User
        fields = [
            "name", "mobile_number", "email", "role", "dob", "company"
        ]
    
    def validate_mobile_number(self, value):
        if value and not re.match(r'^\+?[\d\s\-\(\)]+$', value):
            raise serializers.ValidationError("Invalid mobile number format")
        return value
    
    def validate_company(self, value):
        """Validate company assignment based on user permissions"""
        request = self.context.get('request')
        if not request or not request.user:
            return value
        
        # Admin and Superuser can assign any company or remove company assignment
        if request.user.is_admin() or request.user.is_superuser:
            return value
        
        # Company Admin can only assign their own company
        if request.user.is_company_admin():
            if value and value != request.user.company:
                raise serializers.ValidationError(
                    "Company Admin can only assign users to their own company."
                )
            # Company Admin cannot remove company assignment
            if not value:
                raise serializers.ValidationError(
                    "Company Admin cannot remove company assignment. Only Admin users can remove company assignments."
                )
        
        return value
    
    def validate(self, data):
        """Validate that non-admin roles must have a company assigned"""
        # Get current role and company from data or instance
        role = data.get('role')
        company = data.get('company')
        
        # If role is not being updated, get it from the instance
        if not role and hasattr(self, 'instance') and self.instance:
            role = self.instance.role
        
        # If company is not being updated, get it from the instance
        if not company and hasattr(self, 'instance') and self.instance:
            company = self.instance.company
        
        # Admin role can exist without a company
        if role == "Admin":
            return data
        
        # All other roles (CompanyAdmin, Surveyor, VendorSurveyor, QCReviewer) must have a company
        if not company:
            raise serializers.ValidationError({
                'company': f"Company is required for role '{role}'. Only Admin role can exist without a company. To remove company assignment, first change the role to 'Admin'."
            })
        
        return data
