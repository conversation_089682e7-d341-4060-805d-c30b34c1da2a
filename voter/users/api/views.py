from rest_framework import status
from rest_framework.decorators import api_view, permission_classes, action
from rest_framework.permissions import AllowAny, IsAuthenticated
from rest_framework.response import Response
from rest_framework.viewsets import ReadOnlyModelViewSet, ModelViewSet
from rest_framework.authtoken.models import Token
from django.contrib.auth import authenticate, get_user_model
from django.db.models import Q
from drf_spectacular.utils import extend_schema, OpenApiParameter
from auditlog.context import set_actor
from auditlog.models import LogEntry
from django.contrib.contenttypes.models import ContentType
from voter.contrib.audit import AuditLogMixin, SoftDeleteAuditMixin
from rest_framework.generics import GenericAPIView
from voter.contrib.pagination import PaginatedViewSetMixin

from voter.users.models import User
from voter.users.api.serializers import (
    UserSerializer,
    SendOTPSerializer,
    VerifyOTPSerializer,
    WebLoginSerializer,
    UserDetailSerializer,
    UserCreateSerializer,
    UserUpdateSerializer
)
from voter.users.services import OTPService
from voter.users.permissions import (
    CanManageUsers, CanManageTeams, IsAdminOrCompanyAdmin,
    IsSurveyorOrVendorSurveyor, IsQCReviewer, IsAdmin, CanCreateUsers
)

User = get_user_model()


class UserViewSet(PaginatedViewSetMixin, SoftDeleteAuditMixin, ModelViewSet):
    """User viewset for API access with soft delete support and automatic audit logging"""
    serializer_class = UserSerializer
    permission_classes = [IsAuthenticated]
    lookup_field = "username"
    lookup_url_kwarg = "username"
    search_fields = ['username', 'email', 'name', 'role']
    filterset_fields = ['role', 'is_active', 'company']
    date_filter_field = 'created_at'
    ordering_fields = ['username', 'name', 'created_at', 'last_login']
    
    def get_permissions(self):
        """Return appropriate permissions based on action"""
        if self.action in ['create', 'update', 'partial_update']:
            return [CanCreateUsers()]
        return super().get_permissions()
    
    def get_queryset(self):
        """Filter queryset based on user permissions"""
        queryset = User.objects.filter(is_deleted=False)
        
        # Company Admin can only see users from their own company
        if self.request.user.is_company_admin():
            queryset = queryset.filter(company=self.request.user.company)
        
        return queryset
    
    def get_serializer_class(self):
        """Return appropriate serializer based on action"""
        if self.action in ['create', 'update', 'partial_update']:
            return UserCreateSerializer
        return UserSerializer
    
    def get_serializer_context(self):
        """Add request to serializer context"""
        context = super().get_serializer_context()
        context['request'] = self.request
        return context
    
    def perform_create(self, serializer):
        """Handle company assignment during user creation with improved audit logging"""
        # Ensure non-admin roles have a company assigned
        role = serializer.validated_data.get('role')
        company = serializer.validated_data.get('company')
        
        if role != "Admin" and not company:
            # For Company Admin, assign their company
            if self.request.user.is_company_admin():
                serializer.validated_data['company'] = self.request.user.company
        
        user = serializer.save(created_by=self.request.user)
        
        # Get additional context data
        additional_data = self._get_additional_audit_data('create', user, serializer.validated_data)
        
        # Create audit log
        self._create_audit_log(
            action=LogEntry.Action.CREATE,
            instance=user,
            additional_data=additional_data,
            operation_context='user_creation'
        )
        
        return user
    
    def perform_update(self, serializer):
        """Override to log user updates with improved change detection."""
        # Get the instance before saving to capture old values
        user = serializer.instance
        
        # Save the instance with new data
        updated_user = serializer.save()
        
        # Get changes by comparing old and new values
        changes = self._get_changes(user, serializer.validated_data)
        
        # Get additional context data
        additional_data = self._get_additional_audit_data('update', updated_user, serializer.validated_data)
        
        # Create audit log
        self._create_audit_log(
            action=LogEntry.Action.UPDATE,
            instance=updated_user,
            changes=changes,
            additional_data=additional_data,
            operation_context='user_update'
        )
        
        return updated_user

    @action(detail=True, methods=['delete'], permission_classes=[CanManageUsers])
    def soft_delete(self, request, username=None):
        """Soft delete a user with company-based restrictions"""
        user = self.get_object()
        
        # Check if user can delete this specific user based on company
        if request.user.is_company_admin():
            if user.company != request.user.company:
                return Response({
                    'success': False,
                    'message': 'Company Admin can only delete users from their own company'
                }, status=status.HTTP_403_FORBIDDEN)
        
        self.perform_soft_delete(user)
        return Response({
            'success': True,
            'message': 'User soft deleted successfully',
            'user': UserDetailSerializer(user).data
        }, status=status.HTTP_200_OK)

    @action(detail=False, methods=['get'])
    def me(self, request):
        """Get current user information"""
        serializer = UserDetailSerializer(request.user)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def admin_users(self, request):
        """Get all admin users (Admin and CompanyAdmin roles)"""
        queryset = self.get_queryset().filter(role__in=['Admin', 'CompanyAdmin'])

        # Apply search and filtering
        queryset = self.filter_queryset(queryset)

        # Check if pagination is requested
        if request.query_params.get('pagination', '').lower() == 'true':
            page = self.paginate_queryset(queryset)
            if page is not None:
                serializer = self.get_serializer(page, many=True)
                return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def ground_users(self, request):
        """Get all ground users (Surveyor role)"""

        # if user sends all params then return both Surveyor and VendorSurveyor roles
        if request.query_params.get('all', '').lower() == 'true':
            queryset = self.get_queryset().filter(role__in=['Surveyor', 'VendorSurveyor'])

        else:
            queryset = self.get_queryset().filter(role='Surveyor')

        # Apply search and filtering
        queryset = self.filter_queryset(queryset)

        # Check if pagination is requested
        if request.query_params.get('pagination', '').lower() == 'true':
            page = self.paginate_queryset(queryset)
            if page is not None:
                serializer = self.get_serializer(page, many=True)
                return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def vendor_users(self, request):
        """Get all vendor users (VendorSurveyor role)"""
        queryset = self.get_queryset().filter(role='VendorSurveyor')

        # Apply search and filtering
        queryset = self.filter_queryset(queryset)

        # Check if pagination is requested
        if request.query_params.get('pagination', '').lower() == 'true':
            page = self.paginate_queryset(queryset)
            if page is not None:
                serializer = self.get_serializer(page, many=True)
                return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def qc_users(self, request):
        """Get all QC users (QCReviewer role)"""
        queryset = self.get_queryset().filter(role='QCReviewer')

        # Apply search and filtering
        queryset = self.filter_queryset(queryset)

        # Check if pagination is requested
        if request.query_params.get('pagination', '').lower() == 'true':
            page = self.paginate_queryset(queryset)
            if page is not None:
                serializer = self.get_serializer(page, many=True)
                return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

    def _get_additional_audit_data(self, action, instance, validated_data):
        """Add custom audit data for user operations."""
        additional_data = {
            'user_type': getattr(instance, 'user_type', None),
            'company_id': getattr(instance, 'company_id', None),
            'action_source': 'api',
        }
        
        # Add specific data based on action
        if action == 'create':
            additional_data['created_via'] = 'api'
        elif action == 'update':
            additional_data['updated_fields'] = list(validated_data.keys())
        elif action == 'soft_delete':
            additional_data['deleted_via'] = 'soft_delete'
        
        return additional_data


@extend_schema(
    request=SendOTPSerializer, 
    responses={
        200: {
            "type": "object",
            "properties": {
                "success": {"type": "boolean"},
                "message": {"type": "string"},
                "otp_code": {"type": "string"}
            }
        },
        400: {
            "type": "object",
            "properties": {
                "success": {"type": "boolean"},
                "message": {"type": "string"}
            }
        }
    }
)
@api_view(['POST'])
@permission_classes([AllowAny])
def send_otp(request):
    """Send OTP to mobile number"""
    serializer = SendOTPSerializer(data=request.data)
    if serializer.is_valid():
        mobile_number = serializer.validated_data['mobile_number']
        
        # Send OTP
        result = OTPService.send_otp(mobile_number)
        
        if result['success']:
            return Response({
                'success': True,
                'message': 'OTP sent successfully',
                'otp_code': result['otp_code']  # Remove in production
            }, status=status.HTTP_200_OK)
        else:
            return Response({
                'success': False,
                'message': result['message']
            }, status=status.HTTP_400_BAD_REQUEST)
    
    return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


@extend_schema(
    request=VerifyOTPSerializer, 
    responses={
        200: {
            "type": "object",
            "properties": {
                "success": {"type": "boolean"},
                "message": {"type": "string"},
                "token": {"type": "string"},
                "user": {"type": "object"}
            }
        },
        400: {
            "type": "object",
            "properties": {
                "success": {"type": "boolean"},
                "message": {"type": "string"}
            }
        }
    }
)
@api_view(['POST'])
@permission_classes([AllowAny])
def verify_otp(request):
    """Verify OTP and authenticate user"""
    serializer = VerifyOTPSerializer(data=request.data)
    if serializer.is_valid():
        mobile_number = serializer.validated_data['mobile_number']
        otp_code = serializer.validated_data['otp_code']
        
        # Verify OTP
        result = OTPService.verify_otp(mobile_number, otp_code)
        
        if result['success']:
            # Get the existing user
            user = result['user']
            
            # Generate token
            token, created = Token.objects.get_or_create(user=user)
            
            return Response({
                'success': True,
                'message': 'OTP verified successfully',
                'token': token.key,
                'user': UserDetailSerializer(user).data
            }, status=status.HTTP_200_OK)
        else:
            return Response({
                'success': False,
                'message': result['message']
            }, status=status.HTTP_400_BAD_REQUEST)
    
    return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


@extend_schema(
    request=WebLoginSerializer, 
    responses={
        200: {
            "type": "object",
            "properties": {
                "success": {"type": "boolean"},
                "message": {"type": "string"},
                "token": {"type": "string"},
                "user": {"type": "object"}
            }
        },
        400: {
            "type": "object",
            "properties": {
                "success": {"type": "boolean"},
                "message": {"type": "string"}
            }
        },
        401: {
            "type": "object",
            "properties": {
                "success": {"type": "boolean"},
                "message": {"type": "string"}
            }
        }
    }
)
@api_view(['POST'])
@permission_classes([AllowAny])
def web_login(request):
    """Web login with username/email and password"""
    serializer = WebLoginSerializer(data=request.data)
    if serializer.is_valid():
        username_or_email = serializer.validated_data['username_or_email']
        password = serializer.validated_data['password']
        
        # Try to authenticate with username or email
        user = None
        
        # First try with username
        user = authenticate(username=username_or_email, password=password)
        
        # If not found, try with email
        if user is None:
            try:
                user_obj = User.objects.get(email=username_or_email)
                user = authenticate(username=user_obj.username, password=password)
            except User.DoesNotExist:
                pass
        
        if user:
            # Generate token
            token, created = Token.objects.get_or_create(user=user)
            
            return Response({
                'success': True,
                'message': 'Login successful',
                'token': token.key,
                'user': UserDetailSerializer(user).data
            }, status=status.HTTP_200_OK)
        else:
            return Response({
                'success': False,
                'message': 'Invalid credentials'
            }, status=status.HTTP_401_UNAUTHORIZED)
    
    return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class LogoutView(GenericAPIView):
    """Logout user by deleting token"""
    permission_classes = [IsAuthenticated]
    
    def get_serializer_class(self):
        return None  # No serializer needed for logout
    
    @extend_schema(
        responses={
            200: {
                "type": "object",
                "properties": {
                    "success": {"type": "boolean"},
                    "message": {"type": "string"}
                }
            },
            400: {
                "type": "object", 
                "properties": {
                    "success": {"type": "boolean"},
                    "message": {"type": "string"}
                }
            }
        }
    )
    def post(self, request):
        try:
            request.user.auth_token.delete()
            return Response({
                'success': True,
                'message': 'Logged out successfully'
            }, status=status.HTTP_200_OK)
        except:
            return Response({
                'success': False,
                'message': 'Error during logout'
            }, status=status.HTTP_400_BAD_REQUEST)


class ForceLogoutAllDevicesView(GenericAPIView):
    """Force logout from all devices"""
    permission_classes = [IsAuthenticated]
    
    def get_serializer_class(self):
        return None  # No serializer needed for force logout
    
    @extend_schema(
        responses={
            200: {
                "type": "object",
                "properties": {
                    "success": {"type": "boolean"},
                    "message": {"type": "string"},
                    "timestamp": {"type": "string", "format": "date-time"}
                }
            },
            400: {
                "type": "object",
                "properties": {
                    "success": {"type": "boolean"},
                    "message": {"type": "string"}
                }
            }
        }
    )
    def post(self, request):
        try:
            timestamp = request.user.force_logout_all_devices()
            return Response({
                'success': True,
                'message': 'Force logout successful',
                'timestamp': timestamp
            }, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({
                'success': False,
                'message': f'Error during force logout: {str(e)}'
            }, status=status.HTTP_400_BAD_REQUEST)


@extend_schema(
    responses={
        200: {
            "type": "object",
            "properties": {
                "success": {"type": "boolean"},
                "message": {"type": "string"},
                "user": {"type": "object"}
            }
        }
    }
)
@api_view(['GET'])
@permission_classes([IsAuthenticated])
def check_token_validity(request):
    """Check if current token is valid"""
    return Response({
        'success': True,
        'message': 'Token is valid',
        'user': UserDetailSerializer(request.user).data
    }, status=status.HTTP_200_OK)


@extend_schema(
    responses={
        200: {
            "type": "object",
            "properties": {
                "success": {"type": "boolean"},
                "permissions": {"type": "object"}
            }
        }
    }
)
@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_user_permissions(request):
    """Get current user's permissions"""
    user = request.user
    permissions = {
        "can_manage_surveys": user.can_manage_surveys(),
        "can_collect_samples": user.can_collect_samples(),
        "can_review_samples": user.can_review_samples(),
        "can_manage_users": user.can_manage_users(),
        "can_manage_teams": user.can_manage_teams(),
        "is_admin": user.is_admin(),
        "is_company_admin": user.is_company_admin(),
        "is_surveyor": user.is_surveyor(),
        "is_vendor_surveyor": user.is_vendor_surveyor(),
        "is_qc_reviewer": user.is_qc_reviewer(),
    }
    
    return Response({
        'success': True,
        'permissions': permissions
    }, status=status.HTTP_200_OK)


@extend_schema(
    parameters=[
        OpenApiParameter(
            name="role",
            location=OpenApiParameter.QUERY,
            required=True,
            type=str,
            description="Role to filter users by"
        )
    ],
    responses={
        200: {
            "type": "object",
            "properties": {
                "success": {"type": "boolean"},
                "users": {"type": "array", "items": {"type": "object"}},
                "count": {"type": "integer"}
            }
        },
        400: {
            "type": "object",
            "properties": {
                "success": {"type": "boolean"},
                "message": {"type": "string"}
            }
        }
    }
)
@api_view(['GET'])
@permission_classes([IsAuthenticated, IsAdminOrCompanyAdmin])
def get_users_by_role(request):
    """Get users filtered by role with company-based restrictions"""
    role = request.query_params.get('role')
    
    if not role:
        return Response(
            {'success': False, 'error': 'role parameter is required'}, 
            status=status.HTTP_400_BAD_REQUEST
        )
    
    # Validate role
    valid_roles = [choice[0] for choice in User.ROLE_CHOICES]
    if role not in valid_roles:
        return Response(
            {'success': False, 'error': f'Invalid role. Valid roles are: {", ".join(valid_roles)}'}, 
            status=status.HTTP_400_BAD_REQUEST
        )
    
    users = User.objects.filter(role=role, is_deleted=False)
    
    # Company Admin can only see users from their own company
    if request.user.is_company_admin():
        users = users.filter(company=request.user.company)
    
    serializer = UserSerializer(users, many=True)
    return Response({'success': True, 'users': serializer.data, 'count': users.count()})


@extend_schema(
    request=UserCreateSerializer, 
    responses={
        201: {
            "type": "object",
            "properties": {
                "success": {"type": "boolean"},
                "message": {"type": "string"},
                "user": {"type": "object"}
            }
        },
        400: {
            "type": "object",
            "properties": {
                "success": {"type": "boolean"},
                "message": {"type": "string"},
                "errors": {"type": "object"}
            }
        }
    }
)
@api_view(['POST'])
@permission_classes([CanCreateUsers])
def create_user(request):
    """Create a new user with company-based restrictions"""
    serializer = UserCreateSerializer(data=request.data, context={'request': request})
    if serializer.is_valid():
        # Handle company assignment for Company Admin
        if request.user.is_company_admin() and not serializer.validated_data.get('company'):
            serializer.validated_data['company'] = request.user.company
        
        # Ensure non-admin roles have a company assigned
        role = serializer.validated_data.get('role')
        company = serializer.validated_data.get('company')
        
        if role != "Admin" and not company:
            # For Company Admin, assign their company
            if request.user.is_company_admin():
                serializer.validated_data['company'] = request.user.company
            else:
                return Response({
                    'success': False,
                    'message': 'Company is required for non-admin roles',
                    'errors': {'company': 'Company is required for non-admin roles'}
                }, status=status.HTTP_400_BAD_REQUEST)
        
        user = serializer.save(created_by=request.user)
        return Response({
            'success': True,
            'message': 'User created successfully',
            'user': UserDetailSerializer(user).data
        }, status=status.HTTP_201_CREATED)
    
    return Response({
        'success': False,
        'message': 'Error creating user',
        'errors': serializer.errors
    }, status=status.HTTP_400_BAD_REQUEST)


@extend_schema(
    request=UserUpdateSerializer, 
    responses={
        200: {
            "type": "object",
            "properties": {
                "success": {"type": "boolean"},
                "message": {"type": "string"},
                "user": {"type": "object"}
            }
        },
        400: {
            "type": "object",
            "properties": {
                "success": {"type": "boolean"},
                "message": {"type": "string"},
                "errors": {"type": "object"}
            }
        },
        404: {
            "type": "object",
            "properties": {
                "success": {"type": "boolean"},
                "message": {"type": "string"}
            }
        }
    }
)
@api_view(['PUT', 'PATCH'])
@permission_classes([CanManageUsers])
def update_user(request, user_id):
    """Update an existing user with company-based restrictions"""
    try:
        user = User.objects.get(id=user_id, is_deleted=False)
    except User.DoesNotExist:
        return Response({
            'success': False,
            'message': 'User not found'
        }, status=status.HTTP_404_NOT_FOUND)
    
    # Check if user can update this specific user based on company
    if request.user.is_company_admin():
        if user.company != request.user.company:
            return Response({
                'success': False,
                'message': 'Company Admin can only update users from their own company'
            }, status=status.HTTP_403_FORBIDDEN)
    
    serializer = UserUpdateSerializer(user, data=request.data, partial=True, context={'request': request})
    if serializer.is_valid():
        serializer.save()
        return Response({
            'success': True,
            'message': 'User updated successfully',
            'user': UserDetailSerializer(user).data
        }, status=status.HTTP_200_OK)
    
    return Response({
        'success': False,
        'message': 'Error updating user',
        'errors': serializer.errors
    }, status=status.HTTP_400_BAD_REQUEST)
