import random
from django.utils import timezone
from rest_framework.authtoken.models import Token
from .models import OTP, User


class OTPService:
    """
    Service class for OTP operations
    """
    
    @staticmethod
    def generate_otp():
        """Generate a 6-digit OTP"""
        return random.randint(100000, 999999)
    
    @staticmethod
    def send_otp(mobile_number):
        """
        Send OTP to mobile number (hardcoded for now)
        Returns the OTP code for testing purposes
        """
        # Check if user exists with this mobile number
        try:
            user = User.objects.get(mobile_number=mobile_number)
        except User.DoesNotExist:
            return {
                'success': False,
                'message': 'User with this mobile number does not exist'
            }
        
        # Generate OTP
        otp_code = OTPService.generate_otp()
        
        # Invalidate any existing unused OTPs for this mobile number
        OTP.objects.filter(
            mobile_number=mobile_number,
            is_used=False
        ).update(is_used=True)
        
        # Create new OTP
        otp = OTP.objects.create(
            mobile_number=mobile_number,
            otp_code=otp_code
        )
        
        # For now, we'll return the OTP code since we don't have SMS gateway
        # In production, this would send SMS and return success/failure
        # print(f"OTP for {mobile_number}: {otp_code}")
        
        return {
            'success': True,
            'message': 'OTP sent successfully',
            'otp_code': otp_code  # Remove this in production
        }
    
    @staticmethod
    def verify_otp(mobile_number, otp_code):
        """
        Verify OTP for mobile number
        """
        try:
            otp = OTP.objects.get(
                mobile_number=mobile_number,
                otp_code=otp_code,
                is_used=False
            )
            
            if otp.is_expired():
                return {
                    'success': False,
                    'message': 'OTP has expired'
                }
            
            # Mark OTP as used
            otp.is_used = True
            otp.save()
            
            # Get the existing user
            try:
                user = User.objects.get(mobile_number=mobile_number)
                return {
                    'success': True,
                    'message': 'OTP verified successfully',
                    'user': user
                }
            except User.DoesNotExist:
                return {
                    'success': False,
                    'message': 'User not found'
                }
            
        except OTP.DoesNotExist:
            return {
                'success': False,
                'message': 'Invalid OTP'
            }
    
    @staticmethod
    def is_token_valid(user):
        """
        Check if user's token is still valid based on last logout timestamp
        """
        if not user.last_logout_timestamp:
            return True
        
        # Get the user's most recent token creation time
        try:
            token = Token.objects.get(user=user)
            # If token was created before the last logout, it's invalid
            return token.created > user.last_logout_timestamp
        except Token.DoesNotExist:
            return False 