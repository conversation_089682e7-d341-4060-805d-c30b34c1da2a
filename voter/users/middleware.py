from django.utils.deprecation import MiddlewareMixin
from rest_framework.authtoken.models import Token
from rest_framework.exceptions import AuthenticationFailed
from auditlog.context import set_actor
from .services import OTPService


class TokenValidityMiddleware(MiddlewareMixin):
    """
    Middleware to check if user's token is still valid based on force logout
    and set the actor for audit logging
    """
    
    def process_request(self, request):
        # Only check for API requests
        if not request.path.startswith('/api/'):
            return None
        
        # Check if user is authenticated via token
        if hasattr(request, 'user') and request.user.is_authenticated:
            # Set the actor for audit logging
            set_actor(request.user)
            
            # Check if token is still valid
            if not OTPService.is_token_valid(request.user):
                # Token is invalid, raise authentication error
                raise AuthenticationFailed('Token has been invalidated by force logout from another device')
        
        return None 