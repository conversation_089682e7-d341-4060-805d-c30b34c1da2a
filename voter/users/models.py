from django.contrib.auth.models import AbstractUser
from django.db import models
from django.db.models import <PERSON><PERSON><PERSON><PERSON>, Model, DateTimeField, IntegerField, BooleanField, DateField, ForeignKey, CASCADE, SET_NULL
from django.urls import reverse
from django.utils.translation import gettext_lazy as _
from django.utils import timezone
from datetime import timed<PERSON><PERSON>
from auditlog.registry import auditlog


class User(AbstractUser):
    """
    Default custom user model for Voter-Backend.
    If adding fields that need to be filled at user signup,
    check forms.SignupForm and forms.SocialSignupForms accordingly.
    """

    # First and last name do not cover name patterns around the globe
    name = Char<PERSON><PERSON>(_("Name of User"), blank=True, max_length=255)
    first_name = None  # type: ignore[assignment]
    last_name = None  # type: ignore[assignment]
    
    # Mobile number for OTP authentication
    mobile_number = CharField(
        _("Mobile Number"), 
        max_length=15, 
        blank=True, 
        null=True, 
        unique=True,
        help_text=_("Mobile number for OTP authentication")
    )
    
    # Track force logout timestamp
    last_logout_timestamp = DateTimeField(
        _("Last Logout Timestamp"),
        null=True,
        blank=True,
        help_text=_("Timestamp when user last performed force logout")
    )
    
    # User role system based on Final Models
    ROLE_CHOICES = [
        ("Admin", "Admin"),
        ("CompanyAdmin", "Company Admin"),
        ("Surveyor", "Surveyor"),
        ("VendorSurveyor", "Vendor Surveyor"),
        ("QCReviewer", "QC Reviewer"),
    ]
    
    role = CharField(
        _("Role"),
        max_length=20,
        choices=ROLE_CHOICES,
        default="Surveyor",
        help_text=_("User role in the system")
    )
    
    # Date of birth
    dob = DateField(
        _("Date of Birth"),
        null=True,
        blank=True,
        help_text=_("User's date of birth")
    )
    
    # Soft delete functionality
    is_deleted = BooleanField(
        _("Is Deleted"),
        default=False,
        help_text=_("Soft delete flag")
    )
    
    # Company relationship (for multi-tenancy)
    company = ForeignKey(
        'companies.Company',
        on_delete=SET_NULL,
        null=True,
        blank=True,
        related_name='users',
        help_text=_("Company the user belongs to")
    )
    
    # User creation tracking
    created_by = ForeignKey(
        'self',
        on_delete=SET_NULL,
        null=True,
        blank=True,
        related_name='created_users',
        help_text=_("User who created this user")
    )
    
    created_at = DateTimeField(
        _("Created At"),
        auto_now_add=True,
        help_text=_("When this user was created")
    )

    def get_absolute_url(self) -> str:
        """Get URL for user's detail view.

        Returns:
            str: URL for user detail.

        """
        return reverse("users:detail", kwargs={"username": self.username})
    
    def force_logout_all_devices(self):
        """Force logout from all devices by updating timestamp"""
        self.last_logout_timestamp = timezone.now()
        self.save()
        return self.last_logout_timestamp
    
    def is_admin(self):
        """Check if user is Admin"""
        return self.role == "Admin"
    
    def is_company_admin(self):
        """Check if user is Company Admin"""
        return self.role == "CompanyAdmin"
    
    def is_surveyor(self):
        """Check if user is Surveyor"""
        return self.role == "Surveyor"
    
    def is_vendor_surveyor(self):
        """Check if user is Vendor Surveyor"""
        return self.role == "VendorSurveyor"
    
    def is_qc_reviewer(self):
        """Check if user is QC Reviewer"""
        return self.role == "QCReviewer"
    
    def can_manage_surveys(self):
        """Check if user can manage surveys"""
        return self.role in ["Admin", "CompanyAdmin"]
    
    def can_collect_samples(self):
        """Check if user can collect survey samples"""
        return self.role in ["Surveyor", "VendorSurveyor"]
    
    def can_review_samples(self):
        """Check if user can review samples"""
        return self.role == "QCReviewer"
    
    def can_manage_users(self):
        """Check if user can manage other users"""
        return self.role in ["Admin", "CompanyAdmin"]
    
    def can_manage_teams(self):
        """Check if user can manage teams"""
        return self.role in ["Admin", "CompanyAdmin"]
    
    class Meta:
        verbose_name = _("User")
        verbose_name_plural = _("Users")


class OTP(Model):
    """
    OTP model for mobile authentication
    """
    mobile_number = CharField(max_length=15)
    otp_code = IntegerField()
    is_used = BooleanField(default=False)
    created_at = DateTimeField(auto_now_add=True)
    expires_at = DateTimeField()
    
    class Meta:
        db_table = 'user_otp'
        indexes = [
            models.Index(fields=['mobile_number', 'created_at']),
        ]
    
    def save(self, *args, **kwargs):
        if not self.expires_at:
            # OTP expires in 10 minutes
            self.expires_at = timezone.now() + timedelta(minutes=10)
        super().save(*args, **kwargs)
    
    def is_expired(self):
        return timezone.now() > self.expires_at
    
    def is_valid(self):
        return not self.is_used and not self.is_expired()
