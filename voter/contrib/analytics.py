"""
Analytics module for dashboard data
Provides comprehensive analytics for the survey project
"""

from django.db.models import (
    Count, Sum, Avg, Min, Max, Q, F, Case, When, Value, IntegerField,
    DecimalField, DateTimeField, DateField, CharField, FloatField
)
from django.db.models.functions import (
    TruncDate, TruncHour, TruncDay, TruncWeek, TruncMonth,
    Coalesce, ExtractHour, ExtractDay, ExtractMonth
)
from django.utils import timezone
from datetime import datetime, timedelta
from decimal import Decimal
import json
import pytz

from surveys.models import Survey, Section, Question, QuestionOption
from samples.models import Sample, QuestionResponse
from companies.models import Company, Team, TeamUser
from assignments.models import SurveyAssignment, AssignmentTarget
from qc.models import QCScore
from voter.users.models import User


class DashboardAnalytics:
    """
    Comprehensive analytics class for dashboard data
    """
    
    def __init__(self, user=None, company=None, date_range=None):
        self.user = user
        self.company = company or (user.company if user else None)
        self.date_range = date_range or {
            'start': timezone.now() - timedelta(days=30),
            'end': timezone.now()
        }
    
    def get_overview_stats(self):
        """
        Get overview statistics for the dashboard with date range filtering
        """
        # Base querysets with company filtering
        surveys_qs = Survey.objects.filter(is_deleted=False)
        samples_qs = Sample.objects.all()
        users_qs = User.objects.filter(is_deleted=False)
        assignments_qs = SurveyAssignment.objects.filter(is_deleted=False)
        
        if self.company:
            surveys_qs = surveys_qs.filter(created_by__company=self.company)
            samples_qs = samples_qs.filter(user__company=self.company)
            users_qs = users_qs.filter(company=self.company)
            assignments_qs = assignments_qs.filter(assignee__company=self.company)
        
        # Apply date range filtering
        start_date = self.date_range['start']
        end_date = self.date_range['end']
        
        # Filter samples by date range
        samples_qs = samples_qs.filter(timestamp__range=(start_date, end_date))
        
        # Get IST timezone for today's calculations
        ist_tz = pytz.timezone('Asia/Kolkata')
        now_utc = timezone.now()
        now_ist = now_utc.astimezone(ist_tz)
        today_ist = now_ist.date()
        
        # Calculate today's start and end in UTC
        today_start_ist = datetime.combine(today_ist, datetime.min.time())
        today_end_ist = datetime.combine(today_ist, datetime.max.time())
        today_start_utc = ist_tz.localize(today_start_ist).astimezone(pytz.UTC)
        today_end_utc = ist_tz.localize(today_end_ist).astimezone(pytz.UTC)
        
        # Calculate this week's start and end in UTC
        week_start_ist = today_ist - timedelta(days=today_ist.weekday())
        week_start_ist = datetime.combine(week_start_ist, datetime.min.time())
        week_start_utc = ist_tz.localize(week_start_ist).astimezone(pytz.UTC)
        
        # Calculate this month's start in UTC
        month_start_ist = today_ist.replace(day=1)
        month_start_ist = datetime.combine(month_start_ist, datetime.min.time())
        month_start_utc = ist_tz.localize(month_start_ist).astimezone(pytz.UTC)
        
        return {
            'total_surveys': surveys_qs.count(),
            'active_surveys': surveys_qs.filter(status='Active').count(),
            'draft_surveys': surveys_qs.filter(status='Draft').count(),
            'closed_surveys': surveys_qs.filter(status='Closed').count(),
            
            'total_samples': samples_qs.count(),
            'samples_today': Sample.objects.filter(
                timestamp__range=(today_start_utc, today_end_utc)
            ).filter(user__company=self.company).count() if self.company else Sample.objects.filter(
                timestamp__range=(today_start_utc, today_end_utc)
            ).count(),
            'samples_this_week': Sample.objects.filter(
                timestamp__gte=week_start_utc
            ).filter(user__company=self.company).count() if self.company else Sample.objects.filter(
                timestamp__gte=week_start_utc
            ).count(),
            'samples_this_month': Sample.objects.filter(
                timestamp__gte=month_start_utc
            ).filter(user__company=self.company).count() if self.company else Sample.objects.filter(
                timestamp__gte=month_start_utc
            ).count(),
            
            'total_users': users_qs.count(),
            'active_users': users_qs.filter(is_active=True).count(),
            'surveyors': users_qs.filter(role__in=['Surveyor', 'VendorSurveyor']).count(),
            'qc_reviewers': users_qs.filter(role='QCReviewer').count(),
            
            'total_assignments': assignments_qs.count(),
            'active_assignments': assignments_qs.filter(status='InProgress').count(),
            'completed_assignments': assignments_qs.filter(status='Completed').count(),
            'pending_assignments': assignments_qs.filter(status='Assigned').count(),
            
            # Performance tracking assignments (exclude QCReviewer)
            'performance_assignments': assignments_qs.filter(role__in=['Surveyor', 'VendorSurveyor']).count(),
            'performance_active_assignments': assignments_qs.filter(role__in=['Surveyor', 'VendorSurveyor'], status='InProgress').count(),
            'performance_completed_assignments': assignments_qs.filter(role__in=['Surveyor', 'VendorSurveyor'], status='Completed').count(),
            'performance_pending_assignments': assignments_qs.filter(role__in=['Surveyor', 'VendorSurveyor'], status='Assigned').count(),
            
            'qc_scores': QCScore.objects.filter(
                sample__user__company=self.company
            ).count() if self.company else QCScore.objects.count(),
            'avg_qc_score': QCScore.objects.filter(
                sample__user__company=self.company
            ).aggregate(
                avg_score=Coalesce(Avg('score'), Value(0.0, output_field=DecimalField()))
            )['avg_score'] if self.company else QCScore.objects.aggregate(
                avg_score=Coalesce(Avg('score'), Value(0.0, output_field=DecimalField()))
            )['avg_score'],
            
            'companies': Company.objects.filter(id=self.company.id, is_deleted=False).count() if self.company else Company.objects.filter(is_deleted=False).count(),
            'teams': Team.objects.filter(company=self.company, is_deleted=False).count() if self.company else Team.objects.filter(is_deleted=False).count(),
        }
    
    def get_survey_analytics(self):
        """
        Get detailed survey analytics with date range filtering
        """
        surveys_qs = Survey.objects.filter(is_deleted=False)
        if self.company:
            surveys_qs = surveys_qs.filter(created_by__company=self.company)
        
        # Apply date range filtering
        start_date = self.date_range['start']
        end_date = self.date_range['end']
        surveys_qs = surveys_qs.filter(created_at__range=(start_date, end_date))
        
        # Survey status distribution
        status_distribution = surveys_qs.values('status').annotate(
            count=Count('id')
        ).order_by('status')
        
        # Survey creation trend - group by IST date
        creation_trend = surveys_qs.annotate(
            # Convert UTC to IST and then truncate to date
            trend_date=TruncDate(
                F('created_at') + timedelta(hours=5, minutes=30)
            )
        ).values('trend_date').annotate(
            count=Count('id')
        ).order_by('trend_date')
        
        # Top surveys by sample count (filtered by date range)
        top_surveys = surveys_qs.annotate(
            sample_count=Count('sample', filter=Q(sample__timestamp__range=(start_date, end_date)))
        ).order_by('-sample_count')[:10]
        
        # Survey completion rates
        survey_completion = []
        for survey in surveys_qs[:10]:  # Top 10 surveys
            # Count total questions by iterating through sections
            total_questions = 0
            for section in survey.sections.all():
                total_questions += section.questions.count()
            
            if total_questions > 0:
                # Filter samples by date range
                survey_samples = survey.sample_set.filter(timestamp__range=(start_date, end_date))
                completed_samples = survey_samples.annotate(
                    response_count=Count('responses')
                ).filter(response_count__gte=total_questions).count()
                
                completion_rate = (completed_samples / survey_samples.count() * 100) if survey_samples.count() > 0 else 0
                
                survey_completion.append({
                    'survey_id': survey.id,
                    'survey_title': survey.title,
                    'total_samples': survey_samples.count(),
                    'completed_samples': completed_samples,
                    'completion_rate': round(completion_rate, 2)
                })
        
        return {
            'status_distribution': list(status_distribution),
            'creation_trend': list(creation_trend),
            'top_surveys': [
                {
                    'id': survey.id,
                    'title': survey.title,
                    'status': survey.status,
                    'sample_count': survey.sample_count,
                    'created_at': survey.created_at
                }
                for survey in top_surveys
            ],
            'survey_completion': survey_completion
        }
    
    def get_sample_analytics(self):
        """
        Get detailed sample analytics with date range filtering
        """
        samples_qs = Sample.objects.all()
        if self.company:
            samples_qs = samples_qs.filter(user__company=self.company)
        
        # Apply date range filtering
        start_date = self.date_range['start']
        end_date = self.date_range['end']
        samples_qs = samples_qs.filter(timestamp__range=(start_date, end_date))
        
        # Sample collection trend - group by IST date
        collection_trend = samples_qs.annotate(
            # Convert UTC to IST and then truncate to date
            trend_date=TruncDate(
                F('timestamp') + timedelta(hours=5, minutes=30)
            )
        ).values('trend_date').annotate(
            count=Count('id')
        ).order_by('trend_date')
        
        # Sample status distribution
        status_distribution = {
            'total': samples_qs.count(),
            'uploaded': samples_qs.filter(is_uploaded=True).count(),
            'offline': samples_qs.filter(is_offline=True).count(),
            'qc_done': samples_qs.filter(is_qc_done=True).count(),
        }
        
        # Average completion time
        completion_time = samples_qs.filter(
            start_time__isnull=False,
            end_time__isnull=False
        ).annotate(
            duration=F('end_time') - F('start_time')
        ).aggregate(
            avg_duration=Avg('duration')
        )['avg_duration']
        
        # Samples by hour of day
        hourly_distribution = samples_qs.annotate(
            hour=ExtractHour('timestamp')
        ).values('hour').annotate(
            count=Count('id')
        ).order_by('hour')
        
        # Top performing surveyors
        top_surveyors = samples_qs.values(
            'user__name', 'user__username'
        ).annotate(
            sample_count=Count('id')
        ).filter(
            user__isnull=False
        ).order_by('-sample_count')[:10]
        
        return {
            'collection_trend': list(collection_trend),
            'status_distribution': status_distribution,
            'avg_completion_time_minutes': completion_time.total_seconds() / 60 if completion_time else 0,
            'hourly_distribution': list(hourly_distribution),
            'top_surveyors': list(top_surveyors)
        }
    
    def get_user_analytics(self):
        """
        Get detailed user analytics with date range filtering
        """
        users_qs = User.objects.filter(is_deleted=False)
        if self.company:
            users_qs = users_qs.filter(company=self.company)
        
        # Apply date range filtering for user creation
        start_date = self.date_range['start']
        end_date = self.date_range['end']
        users_qs_filtered = users_qs.filter(date_joined__range=(start_date, end_date))
        
        # User role distribution
        role_distribution = users_qs_filtered.values('role').annotate(
            count=Count('id')
        ).order_by('role')
        
        # User activity (last login) - for the date range period
        active_users = users_qs.filter(
            last_login__range=(start_date, end_date)
        ).count()
        
        # User creation trend - group by IST date
        creation_trend = users_qs_filtered.annotate(
            # Convert UTC to IST and then truncate to date
            trend_date=TruncDate(
                F('date_joined') + timedelta(hours=5, minutes=30)
            )
        ).values('trend_date').annotate(
            count=Count('id')
        ).order_by('trend_date')
        
        # Top performing users by samples collected in date range
        top_performers = users_qs.annotate(
            sample_count=Count('sample', filter=Q(sample__timestamp__range=(start_date, end_date)))
        ).order_by('-sample_count')[:10]
        
        return {
            'role_distribution': list(role_distribution),
            'active_users': active_users,
            'creation_trend': list(creation_trend),
            'top_performers': [
                {
                    'id': user.id,
                    'name': user.name,
                    'username': user.username,
                    'role': user.role,
                    'sample_count': user.sample_count,
                    'last_login': user.last_login
                }
                for user in top_performers
            ]
        }
    
    def get_assignment_analytics(self):
        """
        Get detailed assignment analytics with date range filtering
        """
        assignments_qs = SurveyAssignment.objects.filter(is_deleted=False)
        if self.company:
            assignments_qs = assignments_qs.filter(assignee__company=self.company)
        
        # Apply date range filtering
        start_date = self.date_range['start']
        end_date = self.date_range['end']
        assignments_qs = assignments_qs.filter(assigned_at__range=(start_date, end_date))
        
        # Performance tracking assignments (exclude QCReviewer)
        performance_assignments_qs = assignments_qs.filter(role__in=['Surveyor', 'VendorSurveyor'])
        
        # Assignment status distribution (all assignments)
        status_distribution = assignments_qs.values('status').annotate(
            count=Count('id')
        ).order_by('status')
        
        # Performance assignment status distribution (exclude QCReviewer)
        performance_status_distribution = performance_assignments_qs.values('status').annotate(
            count=Count('id')
        ).order_by('status')
        
        # Assignment trend - group by IST date (all assignments)
        assignment_trend = assignments_qs.annotate(
            # Convert UTC to IST and then truncate to date
            trend_date=TruncDate(
                F('assigned_at') + timedelta(hours=5, minutes=30)
            )
        ).values('trend_date').annotate(
            count=Count('id')
        ).order_by('trend_date')
        
        # Performance assignment trend (exclude QCReviewer)
        performance_assignment_trend = performance_assignments_qs.annotate(
            # Convert UTC to IST and then truncate to date
            trend_date=TruncDate(
                F('assigned_at') + timedelta(hours=5, minutes=30)
            )
        ).values('trend_date').annotate(
            count=Count('id')
        ).order_by('trend_date')
        
        # Assignment completion rates (all assignments)
        completion_rates = assignments_qs.values('survey__title').annotate(
            total=Count('id'),
            completed=Count('id', filter=Q(status='Completed')),
            in_progress=Count('id', filter=Q(status='InProgress')),
            assigned=Count('id', filter=Q(status='Assigned'))
        ).order_by('-total')[:10]
        
        # Performance assignment completion rates (exclude QCReviewer)
        performance_completion_rates = performance_assignments_qs.values('survey__title').annotate(
            total=Count('id'),
            completed=Count('id', filter=Q(status='Completed')),
            in_progress=Count('id', filter=Q(status='InProgress')),
            assigned=Count('id', filter=Q(status='Assigned'))
        ).order_by('-total')[:10]
        
        # Top assignees (all assignments)
        top_assignees = assignments_qs.values(
            'assignee__name', 'assignee__username'
        ).annotate(
            assignment_count=Count('id'),
            completed_count=Count('id', filter=Q(status='Completed'))
        ).filter(
            assignee__isnull=False
        ).order_by('-assignment_count')[:10]
        
        # Top performance assignees (exclude QCReviewer)
        top_performance_assignees = performance_assignments_qs.values(
            'assignee__name', 'assignee__username'
        ).annotate(
            assignment_count=Count('id'),
            completed_count=Count('id', filter=Q(status='Completed'))
        ).filter(
            assignee__isnull=False
        ).order_by('-assignment_count')[:10]
        
        return {
            'status_distribution': list(status_distribution),
            'performance_status_distribution': list(performance_status_distribution),
            'assignment_trend': list(assignment_trend),
            'performance_assignment_trend': list(performance_assignment_trend),
            'completion_rates': list(completion_rates),
            'performance_completion_rates': list(performance_completion_rates),
            'top_assignees': list(top_assignees),
            'top_performance_assignees': list(top_performance_assignees)
        }
    
    def get_qc_analytics(self):
        """
        Get QC (Quality Control) analytics with date range filtering and company filtering
        """
        qc_scores_qs = QCScore.objects.all()
        
        # Apply company filtering
        if self.company:
            qc_scores_qs = qc_scores_qs.filter(sample__user__company=self.company)
        
        # Apply date range filtering
        start_date = self.date_range['start']
        end_date = self.date_range['end']
        qc_scores_qs = qc_scores_qs.filter(reviewed_at__range=(start_date, end_date))
        
        # QC score distribution
        score_distribution = qc_scores_qs.aggregate(
            avg_score=Coalesce(Avg('score'), Value(0.0, output_field=DecimalField())),
            min_score=Coalesce(Min('score'), Value(0.0, output_field=DecimalField())),
            max_score=Coalesce(Max('score'), Value(0.0, output_field=DecimalField())),
            total_reviews=Count('id')
        )
        
        # QC score trend - group by IST date
        score_trend = qc_scores_qs.annotate(
            # Convert UTC to IST and then truncate to date
            trend_date=TruncDate(
                F('reviewed_at') + timedelta(hours=5, minutes=30)
            )
        ).values('trend_date').annotate(
            avg_score=Coalesce(Avg('score'), Value(0.0, output_field=DecimalField())),
            count=Count('id')
        ).order_by('trend_date')
        
        # Top reviewers
        top_reviewers = qc_scores_qs.values(
            'reviewer__name', 'reviewer__username'
        ).annotate(
            review_count=Count('id'),
            avg_score=Coalesce(Avg('score'), Value(0.0, output_field=DecimalField()))
        ).filter(
            reviewer__isnull=False
        ).order_by('-review_count')[:10]
        
        # QC score ranges
        score_ranges = qc_scores_qs.aggregate(
            excellent=Count('id', filter=Q(score__gte=4.5)),
            good=Count('id', filter=Q(score__gte=3.5, score__lt=4.5)),
            average=Count('id', filter=Q(score__gte=2.5, score__lt=3.5)),
            poor=Count('id', filter=Q(score__lt=2.5))
        )
        
        return {
            'score_distribution': score_distribution,
            'score_trend': list(score_trend),
            'top_reviewers': list(top_reviewers),
            'score_ranges': score_ranges
        }
    
    def get_company_analytics(self):
        """
        Get company-specific analytics with proper filtering and date range
        """
        # Get companies based on user permissions
        if self.user and self.user.is_admin():
            # Admin can see all companies
            companies_qs = Company.objects.filter(is_deleted=False)
        elif self.company:
            # Company admin or regular user sees only their company
            companies_qs = Company.objects.filter(id=self.company.id, is_deleted=False)
        else:
            # Fallback to user's company if available
            companies_qs = Company.objects.filter(id=self.user.company.id, is_deleted=False) if self.user and self.user.company else Company.objects.none()
        
        # Apply date range filtering
        start_date = self.date_range['start']
        end_date = self.date_range['end']
        
        # Company performance with date range filtering
        company_performance = []
        for company in companies_qs:
            # Get company data with date range filtering
            company_users = User.objects.filter(company=company, is_deleted=False)
            company_samples = Sample.objects.filter(
                user__company=company,
                timestamp__range=(start_date, end_date)
            )
            company_surveys = Survey.objects.filter(
                created_by__company=company, 
                is_deleted=False,
                created_at__range=(start_date, end_date)
            )
            company_assignments = SurveyAssignment.objects.filter(
                assignee__company=company,
                is_deleted=False,
                assigned_at__range=(start_date, end_date)
            )
            
            # Get QC scores for samples in date range
            company_qc_scores = QCScore.objects.filter(
                sample__user__company=company,
                reviewed_at__range=(start_date, end_date)
            )
            
            # Calculate average QC score
            avg_qc_score = company_qc_scores.aggregate(
                avg_score=Coalesce(Avg('score'), Value(0.0, output_field=DecimalField()))
            )['avg_score']
            
            # Get assignment completion rates
            completed_assignments = company_assignments.filter(status='Completed').count()
            total_assignments = company_assignments.count()
            completion_rate = (completed_assignments / total_assignments * 100) if total_assignments > 0 else 0
            
            company_performance.append({
                'company_id': company.id,
                'company_name': company.name,
                'company_code': company.code,
                'company_type': company.type,
                'user_count': company_users.count(),
                'sample_count': company_samples.count(),
                'survey_count': company_surveys.count(),
                'assignment_count': company_assignments.count(),
                'completed_assignments': completed_assignments,
                'completion_rate': round(completion_rate, 2),
                'avg_qc_score': round(float(avg_qc_score), 2),
                'qc_reviews_count': company_qc_scores.count()
            })
        
        # Company type distribution
        type_distribution = companies_qs.values('type').annotate(
            count=Count('id')
        ).order_by('type')
        
        return {
            'company_performance': company_performance,
            'type_distribution': list(type_distribution)
        }
    
    def get_question_analytics(self, survey_id=None):
        """
        Get question-level analytics with company filtering
        """
        questions_qs = Question.objects.all()
        if survey_id:
            questions_qs = questions_qs.filter(section__survey_id=survey_id)
        
        # Apply company filtering if user is not admin
        if self.company:
            # Filter questions to only those in surveys created by the user's company
            questions_qs = questions_qs.filter(section__survey__created_by__company=self.company)
        
        # Question type distribution
        type_distribution = questions_qs.values('type').annotate(
            count=Count('id')
        ).order_by('type')
        
        # Most answered questions
        most_answered = questions_qs.annotate(
            response_count=Count('questionresponse', filter=Q(
                questionresponse__sample__user__company=self.company,
                questionresponse__sample__timestamp__range=(self.date_range['start'], self.date_range['end'])
            ) if self.company else Q(
                questionresponse__sample__timestamp__range=(self.date_range['start'], self.date_range['end'])
            ))
        ).order_by('-response_count')[:10]
        
        # Question response rates
        response_rates = []
        for question in questions_qs[:20]:  # Top 20 questions
            total_responses = question.questionresponse_set.filter(
                sample__timestamp__range=(self.date_range['start'], self.date_range['end'])
            )
            if self.company:
                total_responses = total_responses.filter(sample__user__company=self.company)
            total_responses = total_responses.count()
            if total_responses > 0:
                # Calculate response rate based on samples that have this question
                # Get surveys that contain this question
                surveys_with_question = Survey.objects.filter(
                    sections__questions=question
                )
                if self.company:
                    surveys_with_question = surveys_with_question.filter(created_by__company=self.company)
                surveys_with_question = surveys_with_question.distinct()
                
                # Count samples from those surveys
                total_possible = Sample.objects.filter(
                    survey__in=surveys_with_question,
                    timestamp__range=(self.date_range['start'], self.date_range['end'])
                )
                if self.company:
                    total_possible = total_possible.filter(user__company=self.company)
                total_possible = total_possible.count()
                
                response_rate = (total_responses / total_possible * 100) if total_possible > 0 else 0
                
                response_rates.append({
                    'question_id': question.id,
                    'question_text': question.question_text[:50],
                    'question_type': question.type,
                    'total_responses': total_responses,
                    'response_rate': round(response_rate, 2)
                })
        
        return {
            'type_distribution': list(type_distribution),
            'most_answered': [
                {
                    'id': question.id,
                    'text': question.question_text[:50],
                    'type': question.type,
                    'response_count': question.response_count
                }
                for question in most_answered
            ],
            'response_rates': response_rates
        }
    
    def get_realtime_stats(self):
        """
        Get real-time statistics with IST timezone
        """
        # Get current time in IST
        ist_tz = pytz.timezone('Asia/Kolkata')
        now_utc = timezone.now()
        now_ist = now_utc.astimezone(ist_tz)
        today_ist = now_ist.date()
        this_hour_ist = now_ist.replace(minute=0, second=0, microsecond=0)
        
        # Convert IST times to UTC for database queries
        today_start_utc = ist_tz.localize(
            datetime.combine(today_ist, datetime.min.time())
        ).astimezone(pytz.UTC)
        today_end_utc = ist_tz.localize(
            datetime.combine(today_ist, datetime.max.time())
        ).astimezone(pytz.UTC)
        
        this_hour_start_utc = ist_tz.localize(
            datetime.combine(this_hour_ist.date(), this_hour_ist.time())
        ).astimezone(pytz.UTC)
        
        # Today's stats
        today_samples = Sample.objects.filter(
            timestamp__range=(today_start_utc, today_end_utc)
        )
        if self.company:
            today_samples = today_samples.filter(user__company=self.company)
        
        # This hour's stats
        this_hour_samples = Sample.objects.filter(
            timestamp__gte=this_hour_start_utc
        )
        if self.company:
            this_hour_samples = this_hour_samples.filter(user__company=self.company)
        
        # Active users (logged in within last hour)
        active_users = User.objects.filter(
            last_login__gte=now_utc - timedelta(hours=1)
        )
        if self.company:
            active_users = active_users.filter(company=self.company)
        
        return {
            'today_samples': today_samples.count(),
            'this_hour_samples': this_hour_samples.count(),
            'active_users': active_users.count(),
            'current_time': now_ist.isoformat(),
            'current_time_utc': now_utc.isoformat()
        }
    
    def get_comprehensive_dashboard(self):
        """
        Get comprehensive dashboard data with IST timezone information
        """
        return {
            'overview': self.get_overview_stats(),
            'surveys': self.get_survey_analytics(),
            'samples': self.get_sample_analytics(),
            'users': self.get_user_analytics(),
            'assignments': self.get_assignment_analytics(),
            'qc': self.get_qc_analytics(),
            'companies': self.get_company_analytics(),
            'questions': self.get_question_analytics(),
            'realtime': self.get_realtime_stats(),
            'date_range': {
                'start': self.date_range['start'].isoformat(),
                'end': self.date_range['end'].isoformat(),
                'start_ist': self.date_range.get('start_ist').isoformat() if self.date_range.get('start_ist') else None,
                'end_ist': self.date_range.get('end_ist').isoformat() if self.date_range.get('end_ist') else None,
                'timezone': 'Asia/Kolkata'
            }
        }


class AnalyticsFilters:
    """
    Helper class for analytics filtering
    """
    
    @staticmethod
    def get_date_range_filter(range_type='30d'):
        """
        Get date range filter based on type with IST timezone conversion
        """
        # Get current time in IST
        ist_tz = pytz.timezone('Asia/Kolkata')
        now_utc = timezone.now()
        now_ist = now_utc.astimezone(ist_tz)
        
        # Calculate start date based on IST
        if range_type == '7d':
            start_date_ist = now_ist - timedelta(days=7)
        elif range_type == '30d':
            start_date_ist = now_ist - timedelta(days=30)
        elif range_type == '90d':
            start_date_ist = now_ist - timedelta(days=90)
        elif range_type == '1y':
            start_date_ist = now_ist - timedelta(days=365)
        else:
            start_date_ist = now_ist - timedelta(days=30)
        
        # Set start and end times in IST
        # Start of day in IST (00:00:00)
        start_date_ist = start_date_ist.replace(hour=0, minute=0, second=0, microsecond=0)
        # End of day in IST (23:59:59)
        end_date_ist = now_ist.replace(hour=23, minute=59, second=59, microsecond=999999)
        
        # Convert IST times to UTC for database queries
        # Create naive datetime and localize to IST, then convert to UTC
        start_date_utc = ist_tz.localize(
            datetime.combine(start_date_ist.date(), datetime.min.time())
        ).astimezone(pytz.UTC)
        
        end_date_utc = ist_tz.localize(
            datetime.combine(end_date_ist.date(), datetime.max.time())
        ).astimezone(pytz.UTC)
        
        return {
            'start': start_date_utc,
            'end': end_date_utc,
            'start_ist': start_date_ist,
            'end_ist': end_date_ist
        }
    
    @staticmethod
    def get_company_filter(user):
        """
        Get company filter based on user permissions
        """
        if user.is_admin():
            return None  # Admin can see all data
        elif user.is_company_admin():
            return user.company
        else:
            return user.company  # Regular users see their company data 