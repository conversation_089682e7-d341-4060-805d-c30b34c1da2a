"""
API views for dashboard analytics
"""

from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.views import APIView
from drf_spectacular.utils import extend_schema, OpenApiParameter
from django.utils import timezone
from datetime import timedel<PERSON>

from .analytics import DashboardAnalytics, AnalyticsFilters


class DashboardOverviewView(APIView):
    """
    Dashboard overview statistics
    """
    permission_classes = [IsAuthenticated]
    
    @extend_schema(
        parameters=[
            OpenApiParameter(
                name="date_range",
                location=OpenApiParameter.QUERY,
                required=False,
                type=str,
                description="Date range: 7d, 30d, 90d, 1y (default: 30d)"
            ),
        ],
        responses={
            200: {
                "type": "object",
                "properties": {
                    "overview": {"type": "object"},
                    "date_range": {"type": "object"}
                }
            }
        }
    )
    def get(self, request):
        """Get dashboard overview statistics"""
        date_range_type = request.query_params.get('date_range', '30d')
        date_range = AnalyticsFilters.get_date_range_filter(date_range_type)
        
        analytics = DashboardAnalytics(
            user=request.user,
            date_range=date_range
        )
        
        return Response({
            'overview': analytics.get_overview_stats(),
            'date_range': {
                'start': date_range['start'].isoformat(),
                'end': date_range['end'].isoformat(),
                'start_ist': date_range.get('start_ist').isoformat() if date_range.get('start_ist') else None,
                'end_ist': date_range.get('end_ist').isoformat() if date_range.get('end_ist') else None,
                'type': date_range_type,
                'timezone': 'Asia/Kolkata'
            }
        })


class SurveyAnalyticsView(APIView):
    """
    Survey-specific analytics
    """
    permission_classes = [IsAuthenticated]
    
    @extend_schema(
        parameters=[
            OpenApiParameter(
                name="date_range",
                location=OpenApiParameter.QUERY,
                required=False,
                type=str,
                description="Date range: 7d, 30d, 90d, 1y (default: 30d)"
            ),
        ],
        responses={
            200: {
                "type": "object",
                "properties": {
                    "surveys": {"type": "object"},
                    "date_range": {"type": "object"}
                }
            }
        }
    )
    def get(self, request):
        """Get survey analytics"""
        date_range_type = request.query_params.get('date_range', '30d')
        date_range = AnalyticsFilters.get_date_range_filter(date_range_type)
        
        analytics = DashboardAnalytics(
            user=request.user,
            date_range=date_range
        )
        
        return Response({
            'surveys': analytics.get_survey_analytics(),
            'date_range': {
                'start': date_range['start'].isoformat(),
                'end': date_range['end'].isoformat(),
                'start_ist': date_range.get('start_ist').isoformat() if date_range.get('start_ist') else None,
                'end_ist': date_range.get('end_ist').isoformat() if date_range.get('end_ist') else None,
                'type': date_range_type,
                'timezone': 'Asia/Kolkata'
            }
        })


class SampleAnalyticsView(APIView):
    """
    Sample collection analytics
    """
    permission_classes = [IsAuthenticated]
    
    @extend_schema(
        parameters=[
            OpenApiParameter(
                name="date_range",
                location=OpenApiParameter.QUERY,
                required=False,
                type=str,
                description="Date range: 7d, 30d, 90d, 1y (default: 30d)"
            ),
        ],
        responses={
            200: {
                "type": "object",
                "properties": {
                    "samples": {"type": "object"},
                    "date_range": {"type": "object"}
                }
            }
        }
    )
    def get(self, request):
        """Get sample analytics"""
        date_range_type = request.query_params.get('date_range', '30d')
        date_range = AnalyticsFilters.get_date_range_filter(date_range_type)
        
        analytics = DashboardAnalytics(
            user=request.user,
            date_range=date_range
        )
        
        return Response({
            'samples': analytics.get_sample_analytics(),
            'date_range': {
                'start': date_range['start'].isoformat(),
                'end': date_range['end'].isoformat(),
                'start_ist': date_range.get('start_ist').isoformat() if date_range.get('start_ist') else None,
                'end_ist': date_range.get('end_ist').isoformat() if date_range.get('end_ist') else None,
                'type': date_range_type,
                'timezone': 'Asia/Kolkata'
            }
        })


class UserAnalyticsView(APIView):
    """
    User performance analytics
    """
    permission_classes = [IsAuthenticated]
    
    @extend_schema(
        parameters=[
            OpenApiParameter(
                name="date_range",
                location=OpenApiParameter.QUERY,
                required=False,
                type=str,
                description="Date range: 7d, 30d, 90d, 1y (default: 30d)"
            ),
        ],
        responses={
            200: {
                "type": "object",
                "properties": {
                    "users": {"type": "object"},
                    "date_range": {"type": "object"}
                }
            }
        }
    )
    def get(self, request):
        """Get user analytics"""
        date_range_type = request.query_params.get('date_range', '30d')
        date_range = AnalyticsFilters.get_date_range_filter(date_range_type)
        
        analytics = DashboardAnalytics(
            user=request.user,
            date_range=date_range
        )
        
        return Response({
            'users': analytics.get_user_analytics(),
            'date_range': {
                'start': date_range['start'].isoformat(),
                'end': date_range['end'].isoformat(),
                'start_ist': date_range.get('start_ist').isoformat() if date_range.get('start_ist') else None,
                'end_ist': date_range.get('end_ist').isoformat() if date_range.get('end_ist') else None,
                'type': date_range_type,
                'timezone': 'Asia/Kolkata'
            }
        })


class AssignmentAnalyticsView(APIView):
    """
    Assignment analytics
    """
    permission_classes = [IsAuthenticated]
    
    @extend_schema(
        parameters=[
            OpenApiParameter(
                name="date_range",
                location=OpenApiParameter.QUERY,
                required=False,
                type=str,
                description="Date range: 7d, 30d, 90d, 1y (default: 30d)"
            ),
        ],
        responses={
            200: {
                "type": "object",
                "properties": {
                    "assignments": {"type": "object"},
                    "date_range": {"type": "object"}
                }
            }
        }
    )
    def get(self, request):
        """Get assignment analytics"""
        date_range_type = request.query_params.get('date_range', '30d')
        date_range = AnalyticsFilters.get_date_range_filter(date_range_type)
        
        analytics = DashboardAnalytics(
            user=request.user,
            date_range=date_range
        )
        
        return Response({
            'assignments': analytics.get_assignment_analytics(),
            'date_range': {
                'start': date_range['start'].isoformat(),
                'end': date_range['end'].isoformat(),
                'start_ist': date_range.get('start_ist').isoformat() if date_range.get('start_ist') else None,
                'end_ist': date_range.get('end_ist').isoformat() if date_range.get('end_ist') else None,
                'type': date_range_type,
                'timezone': 'Asia/Kolkata'
            }
        })


class QCAnalyticsView(APIView):
    """
    Quality Control analytics
    """
    permission_classes = [IsAuthenticated]
    
    @extend_schema(
        parameters=[
            OpenApiParameter(
                name="date_range",
                location=OpenApiParameter.QUERY,
                required=False,
                type=str,
                description="Date range: 7d, 30d, 90d, 1y (default: 30d)"
            ),
        ],
        responses={
            200: {
                "type": "object",
                "properties": {
                    "qc": {"type": "object"},
                    "date_range": {"type": "object"}
                }
            }
        }
    )
    def get(self, request):
        """Get QC analytics"""
        date_range_type = request.query_params.get('date_range', '30d')
        date_range = AnalyticsFilters.get_date_range_filter(date_range_type)
        
        analytics = DashboardAnalytics(
            user=request.user,
            date_range=date_range
        )
        
        return Response({
            'qc': analytics.get_qc_analytics(),
            'date_range': {
                'start': date_range['start'].isoformat(),
                'end': date_range['end'].isoformat(),
                'start_ist': date_range.get('start_ist').isoformat() if date_range.get('start_ist') else None,
                'end_ist': date_range.get('end_ist').isoformat() if date_range.get('end_ist') else None,
                'type': date_range_type,
                'timezone': 'Asia/Kolkata'
            }
        })


class CompanyAnalyticsView(APIView):
    """
    Company performance analytics
    """
    permission_classes = [IsAuthenticated]
    
    @extend_schema(
        parameters=[
            OpenApiParameter(
                name="date_range",
                location=OpenApiParameter.QUERY,
                required=False,
                type=str,
                description="Date range: 7d, 30d, 90d, 1y (default: 30d)"
            ),
        ],
        responses={
            200: {
                "type": "object",
                "properties": {
                    "companies": {"type": "object"},
                    "date_range": {"type": "object"}
                }
            }
        }
    )
    def get(self, request):
        """Get company analytics"""
        date_range_type = request.query_params.get('date_range', '30d')
        date_range = AnalyticsFilters.get_date_range_filter(date_range_type)
        
        analytics = DashboardAnalytics(
            user=request.user,
            date_range=date_range
        )
        
        return Response({
            'companies': analytics.get_company_analytics(),
            'date_range': {
                'start': date_range['start'].isoformat(),
                'end': date_range['end'].isoformat(),
                'start_ist': date_range.get('start_ist').isoformat() if date_range.get('start_ist') else None,
                'end_ist': date_range.get('end_ist').isoformat() if date_range.get('end_ist') else None,
                'type': date_range_type,
                'timezone': 'Asia/Kolkata'
            }
        })


class QuestionAnalyticsView(APIView):
    """
    Question-level analytics
    """
    permission_classes = [IsAuthenticated]
    
    @extend_schema(
        parameters=[
            OpenApiParameter(
                name="survey_id",
                location=OpenApiParameter.QUERY,
                required=False,
                type=int,
                description="Survey ID to filter questions (optional)"
            ),
            OpenApiParameter(
                name="date_range",
                location=OpenApiParameter.QUERY,
                required=False,
                type=str,
                description="Date range: 7d, 30d, 90d, 1y (default: 30d)"
            ),
        ],
        responses={
            200: {
                "type": "object",
                "properties": {
                    "questions": {"type": "object"},
                    "date_range": {"type": "object"}
                }
            }
        }
    )
    def get(self, request):
        """Get question analytics"""
        survey_id = request.query_params.get('survey_id')
        date_range_type = request.query_params.get('date_range', '30d')
        date_range = AnalyticsFilters.get_date_range_filter(date_range_type)
        
        analytics = DashboardAnalytics(
            user=request.user,
            date_range=date_range
        )
        
        return Response({
            'questions': analytics.get_question_analytics(survey_id),
            'date_range': {
                'start': date_range['start'].isoformat(),
                'end': date_range['end'].isoformat(),
                'start_ist': date_range.get('start_ist').isoformat() if date_range.get('start_ist') else None,
                'end_ist': date_range.get('end_ist').isoformat() if date_range.get('end_ist') else None,
                'type': date_range_type,
                'timezone': 'Asia/Kolkata'
            }
        })


class RealtimeStatsView(APIView):
    """
    Real-time statistics
    """
    permission_classes = [IsAuthenticated]
    
    @extend_schema(
        responses={
            200: {
                "type": "object",
                "properties": {
                    "realtime": {"type": "object"}
                }
            }
        }
    )
    def get(self, request):
        """Get real-time statistics"""
        analytics = DashboardAnalytics(user=request.user)
        
        return Response({
            'realtime': analytics.get_realtime_stats()
        })


class ComprehensiveDashboardView(APIView):
    """
    Comprehensive dashboard with all analytics
    """
    permission_classes = [IsAuthenticated]
    
    @extend_schema(
        parameters=[
            OpenApiParameter(
                name="date_range",
                location=OpenApiParameter.QUERY,
                required=False,
                type=str,
                description="Date range: 7d, 30d, 90d, 1y (default: 30d)"
            ),
        ],
        responses={
            200: {
                "type": "object",
                "properties": {
                    "overview": {"type": "object"},
                    "surveys": {"type": "object"},
                    "samples": {"type": "object"},
                    "users": {"type": "object"},
                    "assignments": {"type": "object"},
                    "qc": {"type": "object"},
                    "companies": {"type": "object"},
                    "questions": {"type": "object"},
                    "realtime": {"type": "object"},
                    "date_range": {"type": "object"}
                }
            }
        }
    )
    def get(self, request):
        """Get comprehensive dashboard data"""
        date_range_type = request.query_params.get('date_range', '30d')
        date_range = AnalyticsFilters.get_date_range_filter(date_range_type)
        
        analytics = DashboardAnalytics(
            user=request.user,
            date_range=date_range
        )
        
        dashboard_data = analytics.get_comprehensive_dashboard()
        return Response(dashboard_data)


@extend_schema(
    parameters=[
        OpenApiParameter(
            name="date_range",
            location=OpenApiParameter.QUERY,
            required=False,
            type=str,
            description="Date range: 7d, 30d, 90d, 1y (default: 30d)"
        ),
    ],
    responses={
        200: {
            "type": "object",
            "properties": {
                "overview": {"type": "object"},
                "surveys": {"type": "object"},
                "samples": {"type": "object"},
                "users": {"type": "object"},
                "assignments": {"type": "object"},
                "qc": {"type": "object"},
                "companies": {"type": "object"},
                "questions": {"type": "object"},
                "realtime": {"type": "object"},
                "date_range": {"type": "object"}
            }
        }
    }
)
@api_view(['GET'])
@permission_classes([IsAuthenticated])
def dashboard_data(request):
    """
    Get comprehensive dashboard data (function-based view alternative)
    """
    date_range_type = request.query_params.get('date_range', '30d')
    date_range = AnalyticsFilters.get_date_range_filter(date_range_type)
    
    analytics = DashboardAnalytics(
        user=request.user,
        date_range=date_range
    )
    
    dashboard_data = analytics.get_comprehensive_dashboard()
    return Response(dashboard_data) 