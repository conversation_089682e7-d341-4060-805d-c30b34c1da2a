"""
Audit logging system for automatic CRUD operation tracking.
Provides mixins and utilities for easy integration with any ViewSet.
Enhanced for clear user/actor tracking in multi-model operations.
"""
import uuid
import json
from typing import Dict, Any, Optional, Union, List
from django.contrib.contenttypes.models import ContentType
from django.db import models
from django.utils import timezone
from auditlog.models import LogEntry
from auditlog.context import set_actor
from auditlog.registry import auditlog


class AuditLogMixin:
    """
    Mixin to automatically log CRUD operations for any ViewSet.
    Enhanced with explicit user/actor tracking for multi-model operations.
    
    Usage:
        class MyViewSet(AuditLogMixin, ModelViewSet):
            # Your ViewSet implementation
            pass
    """
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self._audit_logged = False  # Flag to prevent duplicate logging
    
    def _get_user_context(self) -> Dict[str, Any]:
        """
        Get comprehensive user context for audit logging.
        Ensures we have clear user identification in all audit logs.
        """
        user = self.request.user
        context = {
            'actor_id': user.id,
            'actor_username': user.username,
            'actor_email': getattr(user, 'email', None),
            'actor_name': getattr(user, 'name', None),
            'actor_role': getattr(user, 'role', None),
            'actor_company': getattr(user.company, 'name', None) if hasattr(user, 'company') and user.company else None,
            'session_id': getattr(self.request.session, 'session_key', None) if hasattr(self.request, 'session') else None,
            'request_id': getattr(self.request, 'id', None),
            'timestamp': timezone.now().isoformat(),
        }
        return context
    
    def _get_changes(self, instance: models.Model, validated_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        Compare old instance values with new validated data to track changes.
        Returns a dictionary of field changes or None if no changes.
        """
        changes = {}
        
        # Get the current instance values from database
        if instance.pk:
            # Refresh from database to get current values
            instance.refresh_from_db()
        
        # Compare each field in validated_data with current instance values
        for field_name, new_value in validated_data.items():
            if hasattr(instance, field_name):
                old_value = getattr(instance, field_name)
                
                # Handle different data types for comparison
                if isinstance(old_value, (str, int, float, bool)) and isinstance(new_value, (str, int, float, bool)):
                    if old_value != new_value:
                        changes[field_name] = {
                            'old': str(old_value),
                            'new': str(new_value)
                        }
                elif old_value != new_value:
                    # For other types, convert to string for comparison
                    changes[field_name] = {
                        'old': str(old_value),
                        'new': str(new_value)
                    }
        
        return changes if changes else None
    
    def _create_audit_log(
        self, 
        action: str, 
        instance: models.Model, 
        changes: Optional[Dict[str, Any]] = None,
        additional_data: Optional[Dict[str, Any]] = None,
        operation_context: Optional[str] = None
    ) -> LogEntry:
        """
        Create an audit log entry with enhanced user tracking.
        
        Args:
            action: The action performed (CREATE, UPDATE, DELETE, etc.)
            instance: The model instance being logged
            changes: Dictionary of field changes (for updates)
            additional_data: Any additional data to log
            operation_context: Context about the operation (e.g., 'survey_creation', 'sample_submission')
        """
        # Prevent duplicate logging
        if self._audit_logged:
            return None
            
        user = self.request.user
        set_actor(user)
        
        # Get comprehensive user context
        user_context = self._get_user_context()
        
        # Prepare enhanced additional data
        enhanced_data = {
            'user_context': user_context,
            'operation_context': operation_context,
            'viewset_name': self.__class__.__name__,
            'action_name': self.action if hasattr(self, 'action') else None,
            'request_method': self.request.method,
            'request_path': self.request.path,
        }
        
        if additional_data:
            enhanced_data.update(additional_data)
        
        log_entry = LogEntry.objects.create(
            actor=user,
            actor_email=user_context['actor_email'],
            action=action,
            content_type=ContentType.objects.get_for_model(instance),
            object_pk=str(instance.pk),
            object_repr=str(instance),
            changes=changes,
            additional_data=enhanced_data,
            remote_addr=self.request.META.get('REMOTE_ADDR'),
            remote_port=self.request.META.get('REMOTE_PORT'),
        )
        
        # Mark as logged to prevent duplicates
        self._audit_logged = True
        
        return log_entry
    
    def perform_create(self, serializer):
        """Override to log CREATE operations with enhanced user tracking."""
        instance = serializer.save()
        
        # Get additional context data if available
        additional_data = self._get_additional_audit_data('create', instance, serializer.validated_data)
        
        # Determine operation context based on model and action
        operation_context = self._get_operation_context('create', instance)
        
        self._create_audit_log(
            action=LogEntry.Action.CREATE,
            instance=instance,
            additional_data=additional_data,
            operation_context=operation_context
        )
        
        return instance
    
    def perform_update(self, serializer):
        """Override to log UPDATE operations with enhanced user tracking."""
        # Get the instance before saving to capture old values
        instance = serializer.instance
        
        # Save the instance with new data
        updated_instance = serializer.save()
        
        # Get changes by comparing old and new values
        changes = self._get_changes(instance, serializer.validated_data)
        
        # Get additional context data if available
        additional_data = self._get_additional_audit_data('update', updated_instance, serializer.validated_data)
        
        # Determine operation context based on model and action
        operation_context = self._get_operation_context('update', updated_instance)
        
        self._create_audit_log(
            action=LogEntry.Action.UPDATE,
            instance=updated_instance,
            changes=changes,
            additional_data=additional_data,
            operation_context=operation_context
        )
        
        return updated_instance
    
    def perform_destroy(self, instance):
        """Override to log DELETE operations with enhanced user tracking."""
        # Get additional context data if available
        additional_data = self._get_additional_audit_data('delete', instance, {})
        
        # Determine operation context based on model and action
        operation_context = self._get_operation_context('delete', instance)
        
        # For soft deletes, we might want to log before setting is_deleted
        if hasattr(instance, 'is_deleted'):
            # This is a soft delete - log it as a single operation
            self._create_audit_log(
                action=LogEntry.Action.DELETE,
                instance=instance,
                additional_data=additional_data,
                operation_context=operation_context
            )
            
            # Perform soft delete
            instance.is_deleted = True
            instance.save()
        else:
            # This is a hard delete
            self._create_audit_log(
                action=LogEntry.Action.DELETE,
                instance=instance,
                additional_data=additional_data,
                operation_context=operation_context
            )
            
            # Call the original perform_destroy
            super().perform_destroy(instance)
    
    def _get_operation_context(self, action: str, instance: models.Model) -> str:
        """
        Determine the operation context based on the model and action.
        This helps identify what type of operation is being performed.
        """
        model_name = instance.__class__.__name__.lower()
        
        # Map common operations to contexts
        context_mapping = {
            'survey': {
                'create': 'survey_creation',
                'update': 'survey_update',
                'delete': 'survey_deletion'
            },
            'sample': {
                'create': 'sample_submission',
                'update': 'sample_update',
                'delete': 'sample_deletion'
            },
            'questionresponse': {
                'create': 'response_submission',
                'update': 'response_update',
                'delete': 'response_deletion'
            },
            'section': {
                'create': 'section_creation',
                'update': 'section_update',
                'delete': 'section_deletion'
            },
            'question': {
                'create': 'question_creation',
                'update': 'question_update',
                'delete': 'question_deletion'
            },
            'questionoption': {
                'create': 'option_creation',
                'update': 'option_update',
                'delete': 'option_deletion'
            },
            'surveyassignment': {
                'create': 'assignment_creation',
                'update': 'assignment_update',
                'delete': 'assignment_deletion'
            },
            'qcscore': {
                'create': 'qc_review',
                'update': 'qc_update',
                'delete': 'qc_deletion'
            }
        }
        
        return context_mapping.get(model_name, {}).get(action, f'{model_name}_{action}')
    
    def _get_additional_audit_data(
        self, 
        action: str, 
        instance: models.Model, 
        validated_data: Dict[str, Any]
    ) -> Optional[Dict[str, Any]]:
        """
        Override this method in your ViewSet to add custom audit data.
        
        Args:
            action: The action being performed ('create', 'update', 'delete')
            instance: The model instance
            validated_data: The validated data from the serializer
            
        Returns:
            Dictionary of additional data to log, or None
        """
        return None


class MultiModelAuditMixin(AuditLogMixin):
    """
    Enhanced mixin for ViewSets that handle multi-model operations.
    Creates consolidated audit logs with all related model information in changes field.
    """
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self._consolidated_operation = False  # Flag to indicate consolidated operation
    
    def _serialize_model_instance(self, instance: models.Model) -> Dict[str, Any]:
        """
        Serialize a model instance to a dictionary for audit logging.
        """
        serialized = {
            'model': instance.__class__.__name__,
            'id': str(instance.pk),
            'object_repr': str(instance),
            'fields': {}
        }
        
        # Get all field values
        for field in instance._meta.fields:
            if field.name != 'id':  # Skip ID field as it's already included
                try:
                    value = getattr(instance, field.name)
                    if isinstance(value, uuid.UUID):
                        serialized['fields'][field.name] = str(value)
                    elif hasattr(value, 'pk'):  # Foreign key
                        serialized['fields'][field.name] = str(value.pk)
                    elif hasattr(value, 'all'):  # Many to many
                        serialized['fields'][field.name] = [str(item.pk) for item in value.all()]
                    else:
                        serialized['fields'][field.name] = str(value) if value is not None else None
                except Exception:
                    serialized['fields'][field.name] = None
        
        return serialized
    
    def _get_related_models_data(self, primary_instance: models.Model) -> Dict[str, Any]:
        """
        Get structured data for all related models.
        """
        related_data = {
            'sections': [],
            'questions': [],
            'options': [],
            'responses': []
        }
        
        # Handle Survey model
        if hasattr(primary_instance, 'sections'):
            for section in primary_instance.sections.all():
                section_data = self._serialize_model_instance(section)
                section_data['questions'] = []
                
                for question in section.questions.all():
                    question_data = self._serialize_model_instance(question)
                    question_data['options'] = []
                    
                    for option in question.options.all():
                        option_data = self._serialize_model_instance(option)
                        question_data['options'].append(option_data)
                    
                    section_data['questions'].append(question_data)
                
                related_data['sections'].append(section_data)
        
        # Handle Sample model
        if hasattr(primary_instance, 'responses'):
            for response in primary_instance.responses.all():
                response_data = self._serialize_model_instance(response)
                related_data['responses'].append(response_data)
        
        return related_data
    
    def _detect_multi_model_changes(
        self, 
        primary_instance_before: models.Model, 
        primary_instance_after: models.Model,
        before_instances: List[models.Model], 
        after_instances: List[models.Model]
    ) -> Dict[str, Any]:
        """
        Detect actual changes between before and after states of multi-model operations.
        Returns a user-friendly dictionary of changes for UPDATE operations.
        """
        changes = {
            'summary': {
                'total_changes': 0,
                'primary_model_changed': False,
                'related_models_changed': False
            },
            'primary_model_changes': {},
            'related_models_changes': {
                'added': [],
                'updated': [],
                'deleted': []
            }
        }
        
        # 1. Detect primary model changes (e.g., Survey status change)
        primary_changes = self._detect_model_field_changes(primary_instance_before, primary_instance_after)
        if primary_changes:
            changes['primary_model_changes'] = primary_changes
            changes['summary']['primary_model_changed'] = True
            changes['summary']['total_changes'] += len(primary_changes)
        
        # 2. Detect related model changes (sections, questions, options)
        related_changes = self._detect_related_model_changes(before_instances, after_instances)
        if related_changes['added'] or related_changes['updated'] or related_changes['deleted']:
            changes['related_models_changes'] = related_changes
            changes['summary']['related_models_changed'] = True
            changes['summary']['total_changes'] += (
                len(related_changes['added']) + 
                len(related_changes['updated']) + 
                len(related_changes['deleted'])
            )
        
        return changes
    
    def _detect_model_field_changes(self, before_instance: models.Model, after_instance: models.Model) -> Dict[str, Any]:
        """
        Detect field-level changes between two model instances.
        Returns user-friendly change descriptions.
        """
        changes = {}
        
        # Get all field names (excluding some internal fields)
        exclude_fields = {'id', 'created_at', 'updated_at', 'last_modified'}
        field_names = [field.name for field in before_instance._meta.fields 
                      if field.name not in exclude_fields]
        
        for field_name in field_names:
            if hasattr(before_instance, field_name) and hasattr(after_instance, field_name):
                before_value = getattr(before_instance, field_name)
                after_value = getattr(after_instance, field_name)
                
                # Handle different data types
                if isinstance(before_value, (str, int, float, bool)) and isinstance(after_value, (str, int, float, bool)):
                    if before_value != after_value:
                        changes[field_name] = {
                            'old': str(before_value),
                            'new': str(after_value),
                            'description': f"Changed {field_name} from '{before_value}' to '{after_value}'"
                        }
                elif before_value != after_value:
                    # For other types, convert to string for comparison
                    changes[field_name] = {
                        'old': str(before_value) if before_value is not None else 'None',
                        'new': str(after_value) if after_value is not None else 'None',
                        'description': f"Changed {field_name}"
                    }
        
        return changes
    
    def _detect_related_model_changes(self, before_instances: List[models.Model], after_instances: List[models.Model]) -> Dict[str, Any]:
        """
        Detect changes in related models (sections, questions, options).
        Returns user-friendly change descriptions.
        """
        changes = {
            'added': [],
            'updated': [],
            'deleted': []
        }
        
        # Create dictionaries for easier lookup
        before_dict = {f"{instance.__class__.__name__}:{instance.pk}": instance for instance in before_instances}
        after_dict = {f"{instance.__class__.__name__}:{instance.pk}": instance for instance in after_instances}
        
        # Find added models
        added_keys = set(after_dict.keys()) - set(before_dict.keys())
        for key in added_keys:
            instance = after_dict[key]
            changes['added'].append({
                'model_type': instance.__class__.__name__,
                'id': str(instance.pk),
                'name': str(instance),
                'description': f"Added new {instance.__class__.__name__.lower()}: {str(instance)}"
            })
        
        # Find deleted models
        deleted_keys = set(before_dict.keys()) - set(after_dict.keys())
        for key in deleted_keys:
            instance = before_dict[key]
            changes['deleted'].append({
                'model_type': instance.__class__.__name__,
                'id': str(instance.pk),
                'name': str(instance),
                'description': f"Removed {instance.__class__.__name__.lower()}: {str(instance)}"
            })
        
        # Find updated models (models that exist in both sets)
        updated_keys = set(before_dict.keys()) & set(after_dict.keys())
        for key in updated_keys:
            before_instance = before_dict[key]
            after_instance = after_dict[key]
            
            # Check if there are actual field changes
            field_changes = self._detect_model_field_changes(before_instance, after_instance)
            if field_changes:
                changes['updated'].append({
                    'model_type': after_instance.__class__.__name__,
                    'id': str(after_instance.pk),
                    'name': str(after_instance),
                    'field_changes': field_changes,
                    'description': f"Updated {after_instance.__class__.__name__.lower()}: {str(after_instance)}"
                })
        
        return changes
    
    def _create_consolidated_audit_log(
        self,
        action: str,
        primary_instance: models.Model,
        related_instances: List[models.Model],
        operation_type: str,
        description: str = None,
        additional_context: Optional[Dict[str, Any]] = None,
        changes: Optional[Dict[str, Any]] = None
    ) -> LogEntry:
        """
        Create a single consolidated audit log entry for multi-model operations.
        For UPDATE operations, only changes are stored in the changes field.
        For CREATE operations, the full object structure is stored.
        """
        # Prevent duplicate logging
        if self._audit_logged:
            return None
            
        user = self.request.user
        set_actor(user)
        
        # Get comprehensive user context
        user_context = self._get_user_context()
        
        # Get related models data first
        related_models_data = self._get_related_models_data(primary_instance)
        
        # Prepare consolidated data based on operation type
        if operation_type == 'update':
            # For UPDATE operations, store only changes in the changes field
            consolidated_changes = changes if changes else {}
            
            # Store full object information in additional_data
            full_object_data = {
                'operation_type': operation_type,
                'description': description,
                'primary_model': {
                    'model': primary_instance.__class__.__name__,
                    'id': str(primary_instance.pk),
                    'object_repr': str(primary_instance),
                    'fields': self._serialize_model_instance(primary_instance)['fields']
                },
                'related_models': related_models_data,
                'related_models_count': len(related_instances),
                'related_models_summary': {
                    'sections_count': len(related_models_data['sections']),
                    'questions_count': sum(len(section['questions']) for section in related_models_data['sections']),
                    'options_count': sum(len(question['options']) for section in related_models_data['sections'] for question in section['questions']),
                    'responses_count': len(related_models_data['responses'])
                }
            }
        else:
            # For CREATE/DELETE operations, store full object structure in changes field
            consolidated_changes = {
                'operation_type': operation_type,
                'description': description,
                'primary_model': {
                    'model': primary_instance.__class__.__name__,
                    'id': str(primary_instance.pk),
                    'object_repr': str(primary_instance),
                    'fields': self._serialize_model_instance(primary_instance)['fields']
                },
                'related_models': related_models_data,
                'related_models_count': len(related_instances),
                'related_models_summary': {
                    'sections_count': len(related_models_data['sections']),
                    'questions_count': sum(len(section['questions']) for section in related_models_data['sections']),
                    'options_count': sum(len(question['options']) for section in related_models_data['sections'] for question in section['questions']),
                    'responses_count': len(related_models_data['responses'])
                }
            }
            full_object_data = {}
        
        # Prepare enhanced additional data
        enhanced_data = {
            'user_context': user_context,
            'operation_context': f'{operation_type}_consolidated',
            'viewset_name': self.__class__.__name__,
            'action_name': self.action if hasattr(self, 'action') else None,
            'request_method': self.request.method,
            'request_path': self.request.path,
            'consolidated_logging': True,
            'multi_model_operation': True,
        }
        
        # Add full object data for UPDATE operations
        if operation_type == 'update':
            enhanced_data['full_object_data'] = full_object_data
        
        if additional_context:
            enhanced_data.update(additional_context)
        
        log_entry = LogEntry.objects.create(
            actor=user,
            actor_email=user_context['actor_email'],
            action=action,
            content_type=ContentType.objects.get_for_model(primary_instance),
            object_pk=str(primary_instance.pk),
            object_repr=str(primary_instance),
            changes=consolidated_changes,
            additional_data=enhanced_data,
            remote_addr=self.request.META.get('REMOTE_ADDR'),
            remote_port=self.request.META.get('REMOTE_PORT'),
        )
        
        # Mark as logged to prevent duplicates
        self._audit_logged = True
        
        return log_entry
    
    # Override parent methods to prevent individual audit logging when consolidated logging is used
    def perform_create(self, serializer):
        """Override to prevent individual audit logging when consolidated logging is used."""
        if self._consolidated_operation:
            # Skip individual audit logging for consolidated operations
            return serializer.save()
        else:
            # Use parent's individual audit logging for single model operations
            return super().perform_create(serializer)
    
    def perform_update(self, serializer):
        """Override to prevent individual audit logging when consolidated logging is used."""
        if self._consolidated_operation:
            # Skip individual audit logging for consolidated operations
            return serializer.save()
        else:
            # Use parent's individual audit logging for single model operations
            return super().perform_update(serializer)
    
    def perform_destroy(self, instance):
        """Override to prevent individual audit logging when consolidated logging is used."""
        if self._consolidated_operation:
            # Skip individual audit logging for consolidated operations
            if hasattr(instance, 'is_deleted'):
                # Soft delete
                instance.is_deleted = True
                instance.save()
            else:
                # Hard delete
                super().perform_destroy(instance)
        else:
            # Use parent's individual audit logging for single model operations
            return super().perform_destroy(instance)
    
    def _start_consolidated_operation(self):
        """Mark the start of a consolidated operation to prevent individual audit logging."""
        self._consolidated_operation = True
        self._audit_logged = False  # Reset audit logged flag
    
    def _end_consolidated_operation(self):
        """Mark the end of a consolidated operation."""
        self._consolidated_operation = False
        self._audit_logged = False  # Reset audit logged flag


class SoftDeleteAuditMixin(AuditLogMixin):
    """
    Mixin for ViewSets that support soft delete operations.
    Automatically logs soft delete actions with enhanced user tracking.
    """
    
    def perform_soft_delete(self, instance):
        """Perform soft delete and log it with enhanced user tracking."""
        # Get additional context data if available
        additional_data = self._get_additional_audit_data('soft_delete', instance, {})
        
        # Determine operation context
        operation_context = self._get_operation_context('delete', instance)
        
        # Log the soft delete operation
        log_entry = self._create_audit_log(
            action=LogEntry.Action.DELETE,
            instance=instance,
            additional_data=additional_data,
            operation_context=operation_context
        )
        
        # Perform soft delete
        instance.is_deleted = True
        instance.save()
        
        return instance


class BulkAuditMixin(AuditLogMixin):
    """
    Mixin for ViewSets that support bulk operations.
    Logs bulk create, update, and delete operations with enhanced user tracking.
    """
    
    def perform_bulk_create(self, serializer_list):
        """Override to log bulk CREATE operations with enhanced user tracking."""
        instances = super().perform_bulk_create(serializer_list)
        
        # Log each instance creation
        for instance in instances:
            additional_data = self._get_additional_audit_data('bulk_create', instance, {})
            operation_context = self._get_operation_context('create', instance)
            
            self._create_audit_log(
                action=LogEntry.Action.CREATE,
                instance=instance,
                additional_data=additional_data,
                operation_context=operation_context
            )
        
        return instances
    
    def perform_bulk_update(self, serializer_list):
        """Override to log bulk UPDATE operations with enhanced user tracking."""
        instances = super().perform_bulk_update(serializer_list)
        
        # Log each instance update
        for serializer in serializer_list:
            instance = serializer.instance
            changes = self._get_changes(instance, serializer.validated_data)
            additional_data = self._get_additional_audit_data('bulk_update', instance, serializer.validated_data)
            operation_context = self._get_operation_context('update', instance)
            
            self._create_audit_log(
                action=LogEntry.Action.UPDATE,
                instance=instance,
                changes=changes,
                additional_data=additional_data,
                operation_context=operation_context
            )
        
        return instances


# Enhanced utility functions for manual audit logging
def log_manual_action(
    user,
    action: str,
    instance: models.Model,
    changes: Optional[Dict[str, Any]] = None,
    additional_data: Optional[Dict[str, Any]] = None,
    request=None,
    operation_context: Optional[str] = None
) -> LogEntry:
    """
    Manually log an action when not using the mixin.
    Enhanced with better user tracking.
    
    Args:
        user: The user performing the action
        action: The action being performed
        instance: The model instance
        changes: Dictionary of field changes
        additional_data: Additional data to log
        request: The request object (optional, for IP address)
        operation_context: Context about the operation
    
    Returns:
        The created LogEntry instance
    """
    set_actor(user)
    
    # Get comprehensive user context
    user_context = {
        'actor_id': user.id,
        'actor_username': user.username,
        'actor_email': getattr(user, 'email', None),
        'actor_name': getattr(user, 'name', None),
        'actor_role': getattr(user, 'role', None),
        'actor_company': getattr(user.company, 'name', None) if hasattr(user, 'company') and user.company else None,
        'session_id': getattr(request.session, 'session_key', None) if request and hasattr(request, 'session') else None,
        'request_id': getattr(request, 'id', None) if request else None,
        'timestamp': timezone.now().isoformat(),
    }
    
    # Prepare enhanced additional data
    enhanced_data = {
        'user_context': user_context,
        'operation_context': operation_context,
        'manual_logging': True,
    }
    
    if additional_data:
        enhanced_data.update(additional_data)
    
    return LogEntry.objects.create(
        actor=user,
        actor_email=user_context['actor_email'],
        action=action,
        content_type=ContentType.objects.get_for_model(instance),
        object_pk=str(instance.pk),
        object_repr=str(instance),
        changes=changes,
        additional_data=enhanced_data,
        remote_addr=request.META.get('REMOTE_ADDR') if request else None,
        remote_port=request.META.get('REMOTE_PORT') if request else None,
    )


def log_custom_action(
    user,
    action_name: str,
    instance: models.Model,
    description: str = None,
    additional_data: Optional[Dict[str, Any]] = None,
    request=None,
    operation_context: Optional[str] = None
) -> LogEntry:
    """
    Log a custom action that doesn't fit the standard CRUD operations.
    Enhanced with better user tracking.
    
    Args:
        user: The user performing the action
        action_name: Custom action name
        instance: The model instance
        description: Description of the action
        additional_data: Additional data to log
        request: The request object (optional, for IP address)
        operation_context: Context about the operation
    
    Returns:
        The created LogEntry instance
    """
    set_actor(user)
    
    # Get comprehensive user context
    user_context = {
        'actor_id': user.id,
        'actor_username': user.username,
        'actor_email': getattr(user, 'email', None),
        'actor_name': getattr(user, 'name', None),
        'actor_role': getattr(user, 'role', None),
        'actor_company': getattr(user.company, 'name', None) if hasattr(user, 'company') and user.company else None,
        'session_id': getattr(request.session, 'session_key', None) if request and hasattr(request, 'session') else None,
        'request_id': getattr(request, 'id', None) if request else None,
        'timestamp': timezone.now().isoformat(),
    }
    
    # Prepare enhanced additional data
    custom_data = {
        'user_context': user_context,
        'action_name': action_name,
        'description': description,
        'operation_context': operation_context,
        'custom_action': True,
    }
    
    if additional_data:
        custom_data.update(additional_data)
    
    return LogEntry.objects.create(
        actor=user,
        actor_email=user_context['actor_email'],
        action=LogEntry.Action.UPDATE,  # Use UPDATE as base action for custom actions
        content_type=ContentType.objects.get_for_model(instance),
        object_pk=str(instance.pk),
        object_repr=str(instance),
        changes=None,
        additional_data=custom_data,
        remote_addr=request.META.get('REMOTE_ADDR') if request else None,
        remote_port=request.META.get('REMOTE_PORT') if request else None,
    )


def log_consolidated_multi_model_operation(
    user,
    primary_instance: models.Model,
    related_instances: List[models.Model],
    operation_type: str,
    description: str = None,
    additional_data: Optional[Dict[str, Any]] = None,
    request=None,
    operation_context: Optional[str] = None,
    changes: Optional[Dict[str, Any]] = None
) -> LogEntry:
    """
    Log a consolidated multi-model operation with all related data in a single audit log entry.
    For UPDATE operations, only changes are stored in the changes field.
    For CREATE/DELETE operations, the full object structure is stored.
    
    Args:
        user: The user performing the action
        primary_instance: The main model instance
        related_instances: List of related model instances
        operation_type: Type of operation ('create', 'update', 'delete')
        description: Description of the operation
        additional_data: Additional data to log
        request: The request object (optional, for IP address)
        operation_context: Context about the operation
        changes: Dictionary of changes for UPDATE operations
    
    Returns:
        The created LogEntry instance
    """
    set_actor(user)
    
    # Get comprehensive user context
    user_context = {
        'actor_id': user.id,
        'actor_username': user.username,
        'actor_email': getattr(user, 'email', None),
        'actor_name': getattr(user, 'name', None),
        'actor_role': getattr(user, 'role', None),
        'actor_company': getattr(user.company, 'name', None) if hasattr(user, 'company') and user.company else None,
        'session_id': getattr(request.session, 'session_key', None) if request and hasattr(request, 'session') else None,
        'request_id': getattr(request, 'id', None) if request else None,
        'timestamp': timezone.now().isoformat(),
    }
    
    # Serialize primary instance
    def serialize_instance(instance):
        serialized = {
            'model': instance.__class__.__name__,
            'id': str(instance.pk),
            'object_repr': str(instance),
            'fields': {}
        }
        
        for field in instance._meta.fields:
            if field.name != 'id':
                try:
                    value = getattr(instance, field.name)
                    if hasattr(value, 'pk'):
                        serialized['fields'][field.name] = str(value.pk)
                    elif hasattr(value, 'all'):
                        serialized['fields'][field.name] = [str(item.pk) for item in value.all()]
                    else:
                        serialized['fields'][field.name] = str(value) if value is not None else None
                except Exception:
                    serialized['fields'][field.name] = None
        
        return serialized
    
    # Get related models data
    def get_related_models_data(primary_instance):
        related_data = {
            'sections': [],
            'questions': [],
            'options': [],
            'responses': []
        }
        
        # Handle Survey model
        if hasattr(primary_instance, 'sections'):
            for section in primary_instance.sections.all():
                section_data = serialize_instance(section)
                section_data['questions'] = []
                
                for question in section.questions.all():
                    question_data = serialize_instance(question)
                    question_data['options'] = []
                    
                    for option in question.options.all():
                        option_data = serialize_instance(option)
                        question_data['options'].append(option_data)
                    
                    section_data['questions'].append(question_data)
                
                related_data['sections'].append(section_data)
        
        # Handle Sample model
        if hasattr(primary_instance, 'responses'):
            for response in primary_instance.responses.all():
                response_data = serialize_instance(response)
                related_data['responses'].append(response_data)
        
        return related_data
    
    # Prepare consolidated data based on operation type
    if operation_type == 'update':
        # For UPDATE operations, store only changes in the changes field
        consolidated_changes = changes if changes else {}
        
        # Store full object information in additional_data
        full_object_data = {
            'operation_type': operation_type,
            'description': description,
            'primary_model': serialize_instance(primary_instance),
            'related_models': get_related_models_data(primary_instance),
            'related_models_count': len(related_instances),
            'related_models_summary': {
                'sections_count': len(get_related_models_data(primary_instance)['sections']),
                'questions_count': sum(len(section['questions']) for section in get_related_models_data(primary_instance)['sections']),
                'options_count': sum(len(question['options']) for section in get_related_models_data(primary_instance)['sections'] for question in section['questions']),
                'responses_count': len(get_related_models_data(primary_instance)['responses'])
            }
        }
    else:
        # For CREATE/DELETE operations, store full object structure in changes field
        consolidated_changes = {
            'operation_type': operation_type,
            'description': description,
            'primary_model': serialize_instance(primary_instance),
            'related_models': get_related_models_data(primary_instance),
            'related_models_count': len(related_instances),
            'related_models_summary': {
                'sections_count': len(get_related_models_data(primary_instance)['sections']),
                'questions_count': sum(len(section['questions']) for section in get_related_models_data(primary_instance)['sections']),
                'options_count': sum(len(question['options']) for section in get_related_models_data(primary_instance)['sections'] for question in section['questions']),
                'responses_count': len(get_related_models_data(primary_instance)['responses'])
            }
        }
        full_object_data = {}
    
    # Prepare enhanced additional data
    enhanced_data = {
        'user_context': user_context,
        'operation_context': operation_context,
        'consolidated_logging': True,
        'multi_model_operation': True,
        'description': description,
    }
    
    # Add full object data for UPDATE operations
    if operation_type == 'update':
        enhanced_data['full_object_data'] = full_object_data
    
    if additional_data:
        enhanced_data.update(additional_data)
    
    return LogEntry.objects.create(
        actor=user,
        actor_email=user_context['actor_email'],
        action=getattr(LogEntry.Action, operation_type.upper()),
        content_type=ContentType.objects.get_for_model(primary_instance),
        object_pk=str(primary_instance.pk),
        object_repr=str(primary_instance),
        changes=consolidated_changes,
        additional_data=enhanced_data,
        remote_addr=request.META.get('REMOTE_ADDR') if request else None,
        remote_port=request.META.get('REMOTE_PORT') if request else None,
    )


# Enhanced decorator for function-based views
def audit_log_action(action_name: str = None, operation_context: str = None):
    """
    Decorator to automatically log actions for function-based views.
    Enhanced with better user tracking.
    
    Usage:
        @audit_log_action("user_login", "authentication")
        def login_view(request):
            # Your view logic
            pass
    """
    def decorator(func):
        def wrapper(request, *args, **kwargs):
            # Execute the original function
            result = func(request, *args, **kwargs)
            
            # Log the action if user is authenticated
            if hasattr(request, 'user') and request.user.is_authenticated:
                # Try to get the instance from the result or kwargs
                instance = None
                if 'pk' in kwargs:
                    # Try to get the model from the view
                    if hasattr(func, 'model'):
                        try:
                            instance = func.model.objects.get(pk=kwargs['pk'])
                        except:
                            pass
                
                if instance:
                    log_custom_action(
                        user=request.user,
                        action_name=action_name or func.__name__,
                        instance=instance,
                        description=f"Action performed via {func.__name__}",
                        request=request,
                        operation_context=operation_context
                    )
            
            return result
        return wrapper
    return decorator


# =============================================================================
# COMPREHENSIVE USAGE EXAMPLES AND DOCUMENTATION
# =============================================================================

"""
ENHANCED AUDIT SYSTEM USAGE GUIDE
=================================

This enhanced audit system provides comprehensive user/actor tracking for all CRUD operations,
especially for multi-model operations like survey and sample creation.

KEY FEATURES:
-------------
1. Explicit User Context: Every audit log includes comprehensive user information
2. Operation Context: Clear identification of what type of operation is being performed
3. Consolidated Multi-Model Support: Single audit log entry with all related model information
4. Enhanced Data: Rich metadata about the operation, request, and user
5. Duplicate Prevention: Prevents multiple audit logs for the same operation
6. Automatic Logging Disable: Temporarily disables automatic audit logging during consolidated operations

CONSOLIDATED AUDIT LOGGING:
---------------------------

The system now creates single consolidated audit log entries for multi-model operations,
storing all related model information in the changes field as structured JSON.

Example for Survey Creation:
```json
{
  "operation_type": "create",
  "description": "Complete survey creation with all components",
  "primary_model": {
    "model": "Survey",
    "id": "61",
    "object_repr": "Flow Test (Copy) (Draft)",
    "fields": {
      "title": "Flow Test (Copy)",
      "description": "A test survey",
      "status": "Draft",
      "created_by": "1"
    }
  },
  "related_models": {
    "sections": [
      {
        "model": "Section",
        "id": "200",
        "object_repr": "Flow Test (Copy) - Welcome Section",
        "fields": {
          "title": "Welcome Section",
          "order": 1,
          "survey": "61"
        },
        "questions": [
          {
            "model": "Question",
            "id": "258",
            "object_repr": "Welcome Section - What is your Gender ?",
            "fields": {
              "question_text": "What is your Gender ?",
              "type": "MCQ",
              "is_mandatory": true,
              "order": 1
            },
            "options": [
              {
                "model": "QuestionOption",
                "id": "402",
                "object_repr": "What is your Gender ? - Male",
                "fields": {
                  "option_text": "Male",
                  "order": 1
                }
              }
            ]
          }
        ]
      }
    ]
  },
  "related_models_count": 18,
  "related_models_summary": {
    "sections_count": 3,
    "questions_count": 3,
    "options_count": 12,
    "responses_count": 0
  }
}
```

DUPLICATE PREVENTION:
--------------------

The system now prevents duplicate audit logs by using a flag mechanism:
- Each ViewSet instance has an `_audit_logged` flag
- Once an audit log is created, the flag is set to prevent duplicates
- This ensures only one audit log entry per operation

AUTOMATIC LOGGING DISABLE:
-------------------------

The system can temporarily disable automatic audit logging during consolidated operations:
- Prevents the auditlog library from creating individual logs
- Ensures only consolidated logs are created
- Restores automatic logging after the operation

BASIC USAGE:
------------

1. Using MultiModelAuditMixin for consolidated logging:
   ```python
   from voter.contrib.audit import MultiModelAuditMixin
   
   class SurveyViewSet(MultiModelAuditMixin, viewsets.ModelViewSet):
       def perform_create(self, serializer):
           survey = serializer.save(created_by=self.request.user)
           
           # Collect all related models
           related_instances = []
           for section in survey.sections.all():
               related_instances.append(section)
               for question in section.questions.all():
                   related_instances.append(question)
           
           # Create consolidated audit log (SINGLE ENTRY)
           self._create_consolidated_audit_log(
               action=LogEntry.Action.CREATE,
               primary_instance=survey,
               related_instances=related_instances,
               operation_type='create',
               description="Complete survey creation with all components"
           )
           
           return survey
   ```

2. Manual consolidated logging:
   ```python
   from voter.contrib.audit import log_consolidated_multi_model_operation
   
   log_consolidated_multi_model_operation(
       user=request.user,
       primary_instance=survey,
       related_instances=[section1, question1, option1],
       operation_type='create',
       description="Survey creation with all components",
       request=request,
       operation_context='survey_creation_complete'
   )
   ```

BENEFITS OF CONSOLIDATED LOGGING:
--------------------------------

1. **Single Entry**: One audit log entry per operation instead of multiple entries
2. **Complete Context**: All related model information in one place
3. **Easy Reading**: End users can see the complete operation at a glance
4. **Structured Data**: All information is organized in a clear JSON structure
5. **Better Performance**: Fewer database writes for audit logging
6. **Easier Queries**: Simpler to query and analyze audit logs
7. **No Duplicates**: Prevents multiple audit logs for the same operation
8. **User Friendly**: Clear identification of who did what, when, and why

This consolidated approach eliminates confusion for end users while maintaining
complete audit trail information in a single, well-structured log entry.
"""
