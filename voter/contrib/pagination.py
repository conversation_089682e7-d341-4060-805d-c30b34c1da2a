from rest_framework.response import Response
from rest_framework.pagination import PageNumberPagination
from rest_framework.filters import BaseFilterBackend
from django.db.models import Q
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework import filters
from datetime import datetime, timedelta
import pytz
from math import ceil
from urllib.parse import urlencode


class StandardResultsSetPagination(PageNumberPagination):
    """
    Standard pagination class with configurable page size and metadata.
    """
    page_size = 10  # Default page size
    page_size_query_param = 'limit'  # Allow clients to specify page size
    max_page_size = 100  # Maximum page size limit
    page_query_param = 'page'  # Query parameter for page number

    def paginate_queryset(self, queryset, request, view=None):
        """
        Paginate the queryset using page number and page size.
        """
        # Store the request object for later use
        self.request = request

        # Get the page number and page size from the query parameters
        self.page_number = int(request.query_params.get(self.page_query_param, 1))  # Default page is 1
        self.page_size = min(
            int(request.query_params.get(self.page_size_query_param, self.page_size)),
            self.max_page_size
        )  # Default page size is 10, with a maximum limit

        # Calculate the offset and limit based on the page number and page size
        self.offset = (self.page_number - 1) * self.page_size
        self.limit = self.page_size

        self.count = queryset.count()  # Total number of items
        return queryset[self.offset:self.offset + self.limit]  # Slice the queryset

    def get_paginated_response(self, data):
        """
        Return the pagination metadata.
        """
        # Calculate total pages manually
        total_pages = ceil(self.count / float(self.page_size))  # Total pages based on page_size

        # Generate the next page URL
        next_page_number = self.page_number + 1
        if next_page_number > total_pages:
            next_page_url = None  # No next page if we've reached the end
        else:
            # Build the next page URL with the updated page number
            next_page_params = {
                self.page_query_param: next_page_number,
                self.page_size_query_param: self.page_size
            }
            # Include existing query parameters (e.g., ordering) in the next page URL
            existing_params = self.request.query_params.dict()
            existing_params.update(next_page_params)
            next_page_url = f"{self.request.build_absolute_uri().split('?')[0]}?{urlencode(existing_params)}"

        return Response({
            'total_items': self.count,  # Total number of items in the queryset
            'total_pages': total_pages,  # Total number of pages
            'current_page': self.page_number,  # Current page number
            'page_size': self.page_size,  # Number of items per page
            'next_page': next_page_url,  # URL for the next page (or None if no next page)
            'results': data  # The actual data
        })


class ConditionalPaginationMixin:
    """
    Mixin that conditionally applies pagination based on the 'pagination' query parameter.
    If 'pagination=true' is in the query parameters, pagination is applied.
    Otherwise, returns all results without pagination.
    """
    
    def get_paginated_response(self, data):
        """
        Override to conditionally return paginated or non-paginated response.
        """
        if self.request.query_params.get('pagination', '').lower() == 'true':
            return super().get_paginated_response(data)
        else:
            # Return all data without pagination metadata
            return Response(data)


class ConditionalPagination(StandardResultsSetPagination, ConditionalPaginationMixin):
    """
    Pagination class that conditionally applies pagination based on query parameter.
    """
    pass


class CaseInsensitiveSearchFilter(BaseFilterBackend):
    """
    Custom filter backend to perform case-insensitive search and date range filtering.
    - Use `search` query parameter for case-insensitive search.
    - Use `start_date` and `end_date` for filtering records within a date range.
    """

    def filter_queryset(self, request, queryset, view):
        search_param = request.query_params.get('search', None)
        start_date = request.query_params.get('start_date', None)
        end_date = request.query_params.get('end_date', None)
        search_fields = getattr(view, 'search_fields', None)
        date_field = getattr(view, 'date_filter_field', None)

        # Case-insensitive search
        if search_param and search_fields:
            q_objects = Q()
            for field in search_fields:
                q_objects |= Q(**{f"{field}__icontains": search_param})
            queryset = queryset.filter(q_objects)

        # Date range filtering with IST to UTC conversion
        if date_field:
            ist = pytz.timezone("Asia/Kolkata")  # IST Timezone
            utc = pytz.utc  # UTC Timezone

            if start_date:
                try:
                    # Convert start_date from IST to UTC (beginning of day)
                    start_date = datetime.strptime(start_date.strip(), "%Y-%m-%d")
                    start_date = ist.localize(start_date)  # Make it timezone-aware in IST
                    start_date = start_date.astimezone(utc)  # Convert to UTC
                    queryset = queryset.filter(**{f"{date_field}__gte": start_date})
                except ValueError:
                    pass  # Ignore invalid date format

            if end_date:
                try:
                    # Convert end_date from IST to UTC (end of day)
                    end_date = datetime.strptime(end_date.strip(), "%Y-%m-%d")
                    end_date = ist.localize(end_date) + timedelta(days=1, seconds=-1)  # 23:59:59 IST
                    end_date = end_date.astimezone(utc)  # Convert to UTC
                    queryset = queryset.filter(**{f"{date_field}__lte": end_date})
                except ValueError:
                    pass  # Ignore invalid date format

        return queryset


class DateRangeFilter(BaseFilterBackend):
    """
    Custom filter backend to filter records within a date range.
    - Use `start_date` and `end_date` query parameters.
    - The view should define `date_filter_field`.
    """

    def filter_queryset(self, request, queryset, view):
        start_date = request.query_params.get('start_date', None)
        end_date = request.query_params.get('end_date', None)
        date_field = getattr(view, 'date_filter_field', None)  # Define this in the view

        if date_field:
            if start_date:
                try:
                    start_date = datetime.strptime(start_date, "%Y-%m-%d")
                    queryset = queryset.filter(**{f"{date_field}__gte": start_date})
                except ValueError:
                    pass  # Ignore invalid date format

            if end_date:
                try:
                    end_date = datetime.strptime(end_date, "%Y-%m-%d")
                    queryset = queryset.filter(**{f"{date_field}__lte": end_date})
                except ValueError:
                    pass  # Ignore invalid date format

        return queryset


class PaginatedViewSetMixin:
    """
    Mixin that provides conditional pagination and common filtering capabilities.
    Apply this mixin to your viewsets to get automatic pagination support.
    """
    
    pagination_class = ConditionalPagination
    filter_backends = [CaseInsensitiveSearchFilter, filters.OrderingFilter, DjangoFilterBackend]
    
    def list(self, request, *args, **kwargs):
        """
        Override list method to handle conditional pagination.
        """
        # Get the base queryset and apply filters first
        queryset = self.filter_queryset(self.get_queryset())
        
        # Check if pagination is requested
        if request.query_params.get('pagination', '').lower() == 'true':
            # Apply pagination after filtering
            page = self.paginate_queryset(queryset)
            if page is not None:
                serializer = self.get_serializer(page, many=True)
                return self.get_paginated_response(serializer.data)
        
        # Return all results without pagination
        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data) 